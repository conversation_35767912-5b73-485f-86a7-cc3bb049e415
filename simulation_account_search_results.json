{"timestamp": "2025-08-18T16:23:28.005636", "user_id": "**********", "target_accounts": ["测试", "test"], "api_search_results": [{"url": "https://xueqiu.com/v4/stock/portfolio/list.json?type=simulation", "data": {"portfolios": [{"portfolio": {"id": -160, "name": "全部", "orderId": -90, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 2, "orderId": -90, "name": "全部", "id": -160}, {"portfolio": {"id": -1, "name": "全部", "orderId": -50, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 1, "orderId": -50, "name": "全部", "id": -1}, {"portfolio": {"id": -130, "name": "投顾", "orderId": -50, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 2, "orderId": -50, "name": "投顾", "id": -130}, {"portfolio": {"id": -5, "name": "只看沪深", "orderId": -40, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 5, "orderId": -40, "name": "只看沪深", "id": -5}, {"portfolio": {"id": -7, "name": "只看港股", "orderId": -30, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 7, "orderId": -30, "name": "只看港股", "id": -7}, {"portfolio": {"id": -11, "name": "期权", "orderId": -25, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 2, "orderId": -25, "name": "期权", "id": -11}, {"portfolio": {"id": -6, "name": "只看美股", "orderId": -20, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 6, "orderId": -20, "name": "只看美股", "id": -6}, {"portfolio": {"id": -4, "name": "我的持仓", "orderId": -10, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 3, "orderId": -10, "name": "我的持仓", "id": -4}], "funds": [{"portfolio": {"id": -1, "name": "全部", "orderId": -30, "stocks": "", "createAt": null, "category": 3}, "include": false, "type": 1, "orderId": -30, "name": "全部", "id": -1}, {"portfolio": {"id": -9, "name": "基金", "orderId": -20, "stocks": "", "createAt": null, "category": 3}, "include": false, "type": 9, "orderId": -20, "name": "基金", "id": -9}, {"portfolio": {"id": -10, "name": "私募", "orderId": -10, "stocks": "", "createAt": null, "category": 3}, "include": false, "type": 10, "orderId": -10, "name": "私募", "id": -10}], "cubes": [{"portfolio": {"id": -1, "name": "全部", "orderId": -50, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 1, "orderId": -50, "name": "全部", "id": -1}, {"portfolio": {"id": -5, "name": "只看沪深", "orderId": -40, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 5, "orderId": -40, "name": "只看沪深", "id": -5}, {"portfolio": {"id": -7, "name": "只看港股", "orderId": -30, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 7, "orderId": -30, "name": "只看港股", "id": -7}, {"portfolio": {"id": -6, "name": "只看美股", "orderId": -20, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 6, "orderId": -20, "name": "只看美股", "id": -6}, {"portfolio": {"id": -8, "name": "我的组合", "orderId": -10, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 4, "orderId": -10, "name": "我的组合", "id": -8}, {"portfolio": {"id": -23, "name": "基金", "orderId": -7, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 2, "orderId": -7, "name": "基金", "id": -23}]}, "contains_simulation": false}, {"url": "https://xueqiu.com/v4/stock/portfolio/list.json?user_id=**********&type=simulation", "data": {"portfolios": [{"portfolio": {"id": -160, "name": "全部", "orderId": -90, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 2, "orderId": -90, "name": "全部", "id": -160}, {"portfolio": {"id": -1, "name": "全部", "orderId": -50, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 1, "orderId": -50, "name": "全部", "id": -1}, {"portfolio": {"id": -130, "name": "投顾", "orderId": -50, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 2, "orderId": -50, "name": "投顾", "id": -130}, {"portfolio": {"id": -5, "name": "只看沪深", "orderId": -40, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 5, "orderId": -40, "name": "只看沪深", "id": -5}, {"portfolio": {"id": -7, "name": "只看港股", "orderId": -30, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 7, "orderId": -30, "name": "只看港股", "id": -7}, {"portfolio": {"id": -11, "name": "期权", "orderId": -25, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 2, "orderId": -25, "name": "期权", "id": -11}, {"portfolio": {"id": -6, "name": "只看美股", "orderId": -20, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 6, "orderId": -20, "name": "只看美股", "id": -6}, {"portfolio": {"id": -4, "name": "我的持仓", "orderId": -10, "stocks": "", "createAt": null, "category": 2}, "include": false, "type": 3, "orderId": -10, "name": "我的持仓", "id": -4}], "funds": [{"portfolio": {"id": -1, "name": "全部", "orderId": -30, "stocks": "", "createAt": null, "category": 3}, "include": false, "type": 1, "orderId": -30, "name": "全部", "id": -1}, {"portfolio": {"id": -9, "name": "基金", "orderId": -20, "stocks": "", "createAt": null, "category": 3}, "include": false, "type": 9, "orderId": -20, "name": "基金", "id": -9}, {"portfolio": {"id": -10, "name": "私募", "orderId": -10, "stocks": "", "createAt": null, "category": 3}, "include": false, "type": 10, "orderId": -10, "name": "私募", "id": -10}], "cubes": [{"portfolio": {"id": -1, "name": "全部", "orderId": -50, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 1, "orderId": -50, "name": "全部", "id": -1}, {"portfolio": {"id": -5, "name": "只看沪深", "orderId": -40, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 5, "orderId": -40, "name": "只看沪深", "id": -5}, {"portfolio": {"id": -7, "name": "只看港股", "orderId": -30, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 7, "orderId": -30, "name": "只看港股", "id": -7}, {"portfolio": {"id": -6, "name": "只看美股", "orderId": -20, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 6, "orderId": -20, "name": "只看美股", "id": -6}, {"portfolio": {"id": -8, "name": "我的组合", "orderId": -10, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 4, "orderId": -10, "name": "我的组合", "id": -8}, {"portfolio": {"id": -23, "name": "基金", "orderId": -7, "stocks": "", "createAt": null, "category": 1}, "include": false, "type": 2, "orderId": -7, "name": "基金", "id": -23}]}, "contains_simulation": false}, {"url": "https://xueqiu.com/cubes/list.json?type=simulation", "data": {"count": 50, "page": 1, "totalCount": 0, "list": [], "maxPage": 0}, "contains_simulation": false}, {"url": "https://xueqiu.com/cubes/list.json?category=simulation", "data": {"count": 50, "page": 1, "totalCount": 0, "list": [], "maxPage": 0}, "contains_simulation": false}], "name_search_results": [], "profile_page_content": true, "simulation_api_results": []}