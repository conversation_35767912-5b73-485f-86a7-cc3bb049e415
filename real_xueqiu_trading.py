#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
真实雪球模拟交易系统
基于雪球真实组合API实现模拟交易功能
"""

import json
import logging
import requests
import time
from datetime import datetime
from typing import Dict, List, Optional

try:
    import pysnowball as ball
    PYSNOWBALL_AVAILABLE = True
except ImportError:
    PYSNOWBALL_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealXueqiuTrader:
    """真实雪球模拟交易器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.token = None
        self.cookies = {}
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
        }
        self.available_cubes = []
        self.current_cube = None
        
        # 加载配置和初始化
        self.load_config()
        self.discover_available_cubes()
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 解析cookies
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.cookies[key] = value
            
            # 设置token
            xq_a_token = self.cookies.get('xq_a_token', '')
            u = self.cookies.get('u', '')
            if xq_a_token and u:
                self.token = f"xq_a_token={xq_a_token};u={u}"
                if PYSNOWBALL_AVAILABLE:
                    ball.set_token(self.token)
                logger.info("✅ 配置加载成功")
            else:
                logger.error("❌ Token格式不正确")
                
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
    
    def discover_available_cubes(self):
        """发现可用的雪球组合"""
        if not PYSNOWBALL_AVAILABLE:
            logger.error("❌ pysnowball未安装")
            return
        
        # 测试一些已知存在的组合
        test_cubes = [
            "ZH001000",   # 做空实体店
            "ZH010000",   # 苗苗熊猫概念组合
            "ZH100000",   # 钱进号
            "ZH1000000",  # 长线养猪
            "ZH2000000",  # 基金投机
            "ZH3000000",  # 亚瑟王
        ]
        
        logger.info("🔍 发现可用组合...")
        for cube_symbol in test_cubes:
            try:
                result = ball.nav_daily(cube_symbol)
                if result and len(result) > 0:
                    cube_data = result[0]
                    cube_info = {
                        'symbol': cube_symbol,
                        'name': cube_data.get('name', 'N/A'),
                        'nav': None,
                        'total_gain': None
                    }
                    
                    # 获取实时净值
                    try:
                        quote_result = ball.quote_current(cube_symbol)
                        if quote_result and cube_symbol in quote_result:
                            quote_data = quote_result[cube_symbol]
                            cube_info['nav'] = quote_data.get('net_value', 'N/A')
                            cube_info['total_gain'] = quote_data.get('total_gain', 'N/A')
                    except:
                        pass
                    
                    self.available_cubes.append(cube_info)
                    logger.info(f"   ✅ {cube_symbol}: {cube_info['name']}")
                    
            except Exception as e:
                logger.debug(f"   ❌ {cube_symbol}: 不可用")
        
        if self.available_cubes:
            # 默认使用第一个可用组合
            self.current_cube = self.available_cubes[0]['symbol']
            logger.info(f"✅ 发现 {len(self.available_cubes)} 个可用组合")
            logger.info(f"📊 当前使用组合: {self.current_cube}")
        else:
            logger.warning("⚠️ 没有发现可用组合")
    
    def get_available_cubes(self) -> List[Dict]:
        """获取可用组合列表"""
        return self.available_cubes
    
    def set_current_cube(self, cube_symbol: str) -> bool:
        """设置当前使用的组合"""
        for cube in self.available_cubes:
            if cube['symbol'] == cube_symbol:
                self.current_cube = cube_symbol
                logger.info(f"📊 切换到组合: {cube_symbol} ({cube['name']})")
                return True
        
        logger.error(f"❌ 组合 {cube_symbol} 不在可用列表中")
        return False
    
    def get_current_position(self) -> Optional[Dict]:
        """获取当前组合持仓"""
        if not self.current_cube:
            logger.error("❌ 没有设置当前组合")
            return None
        
        try:
            result = ball.rebalancing_current(self.current_cube)
            if result:
                last_rb = result.get('last_rb', {})
                holdings = last_rb.get('holdings', [])
                
                position_info = {
                    'cube_symbol': self.current_cube,
                    'cube_name': self._get_cube_name(self.current_cube),
                    'total_holdings': len(holdings),
                    'holdings': []
                }
                
                for holding in holdings:
                    position_info['holdings'].append({
                        'stock_symbol': holding.get('stock_symbol', ''),
                        'stock_name': holding.get('stock_name', ''),
                        'weight': holding.get('weight', 0),
                        'volume': holding.get('volume', 0)
                    })
                
                return position_info
            else:
                logger.error(f"❌ 获取组合 {self.current_cube} 持仓失败")
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取持仓异常: {e}")
            return None
    
    def get_cube_nav(self, cube_symbol: str = None) -> Optional[Dict]:
        """获取组合净值信息"""
        target_cube = cube_symbol or self.current_cube
        if not target_cube:
            logger.error("❌ 没有指定组合")
            return None
        
        try:
            # 获取历史净值
            nav_result = ball.nav_daily(target_cube)
            if not nav_result or len(nav_result) == 0:
                return None
            
            cube_data = nav_result[0]
            nav_list = cube_data.get('list', [])
            
            # 获取实时净值
            quote_result = ball.quote_current(target_cube)
            current_quote = {}
            if quote_result and target_cube in quote_result:
                current_quote = quote_result[target_cube]
            
            nav_info = {
                'cube_symbol': target_cube,
                'cube_name': cube_data.get('name', 'N/A'),
                'current_nav': current_quote.get('net_value', 'N/A'),
                'daily_gain': current_quote.get('daily_gain', 'N/A'),
                'total_gain': current_quote.get('total_gain', 'N/A'),
                'annualized_gain': current_quote.get('annualized_gain', 'N/A'),
                'nav_history_count': len(nav_list)
            }
            
            if nav_list:
                latest_nav = nav_list[-1]
                nav_info['latest_date'] = latest_nav.get('date', 'N/A')
                nav_info['latest_value'] = latest_nav.get('value', 'N/A')
                nav_info['latest_percent'] = latest_nav.get('percent', 'N/A')
            
            return nav_info
            
        except Exception as e:
            logger.error(f"❌ 获取组合净值异常: {e}")
            return None
    
    def get_rebalancing_history(self, cube_symbol: str = None, count: int = 10) -> Optional[List]:
        """获取调仓历史"""
        target_cube = cube_symbol or self.current_cube
        if not target_cube:
            logger.error("❌ 没有指定组合")
            return None
        
        try:
            result = ball.rebalancing_history(target_cube, count=count)
            if result and 'list' in result:
                history_list = result['list']
                
                formatted_history = []
                for record in history_list:
                    created_at = record.get('created_at', 0)
                    if created_at:
                        date_str = datetime.fromtimestamp(created_at/1000).strftime('%Y-%m-%d %H:%M')
                    else:
                        date_str = 'N/A'
                    
                    history_item = {
                        'id': record.get('id', ''),
                        'date': date_str,
                        'status': record.get('status', ''),
                        'comment': record.get('comment', ''),
                        'operations': []
                    }
                    
                    # 解析调仓操作
                    histories = record.get('rebalancing_histories', [])
                    for hist in histories:
                        operation = {
                            'stock_symbol': hist.get('stock_symbol', ''),
                            'stock_name': hist.get('stock_name', ''),
                            'prev_weight': hist.get('prev_weight', 0),
                            'target_weight': hist.get('target_weight', 0),
                            'weight_change': hist.get('target_weight', 0) - hist.get('prev_weight', 0)
                        }
                        history_item['operations'].append(operation)
                    
                    formatted_history.append(history_item)
                
                return formatted_history
            else:
                logger.error(f"❌ 获取调仓历史失败")
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取调仓历史异常: {e}")
            return None
    
    def simulate_buy(self, stock_symbol: str, weight: float) -> Dict:
        """模拟买入操作（记录交易意图）"""
        try:
            # 获取股票信息
            quote = self._get_stock_quote(stock_symbol)
            if not quote:
                return {
                    'success': False,
                    'message': f'无法获取股票 {stock_symbol} 行情'
                }
            
            # 记录模拟交易
            trade_record = {
                'action': 'buy',
                'stock_symbol': stock_symbol,
                'stock_name': quote.get('name', stock_symbol),
                'target_weight': weight,
                'current_price': quote.get('current', 0),
                'timestamp': datetime.now().isoformat(),
                'cube_symbol': self.current_cube,
                'cube_name': self._get_cube_name(self.current_cube)
            }
            
            # 保存交易记录
            self._save_trade_record(trade_record)
            
            logger.info(f"📈 模拟买入: {stock_symbol} 权重: {weight}%")
            
            return {
                'success': True,
                'message': f'模拟买入 {stock_symbol} 成功',
                'trade_record': trade_record
            }
            
        except Exception as e:
            logger.error(f"❌ 模拟买入失败: {e}")
            return {
                'success': False,
                'message': f'模拟买入失败: {str(e)}'
            }
    
    def simulate_sell(self, stock_symbol: str, weight: float = 0) -> Dict:
        """模拟卖出操作（记录交易意图）"""
        try:
            # 获取股票信息
            quote = self._get_stock_quote(stock_symbol)
            if not quote:
                return {
                    'success': False,
                    'message': f'无法获取股票 {stock_symbol} 行情'
                }
            
            # 记录模拟交易
            trade_record = {
                'action': 'sell',
                'stock_symbol': stock_symbol,
                'stock_name': quote.get('name', stock_symbol),
                'target_weight': weight,
                'current_price': quote.get('current', 0),
                'timestamp': datetime.now().isoformat(),
                'cube_symbol': self.current_cube,
                'cube_name': self._get_cube_name(self.current_cube)
            }
            
            # 保存交易记录
            self._save_trade_record(trade_record)
            
            action_desc = "清仓" if weight == 0 else f"减持至{weight}%"
            logger.info(f"📉 模拟卖出: {stock_symbol} {action_desc}")
            
            return {
                'success': True,
                'message': f'模拟卖出 {stock_symbol} 成功',
                'trade_record': trade_record
            }
            
        except Exception as e:
            logger.error(f"❌ 模拟卖出失败: {e}")
            return {
                'success': False,
                'message': f'模拟卖出失败: {str(e)}'
            }
    
    def _get_stock_quote(self, symbol: str) -> Optional[Dict]:
        """获取股票行情"""
        try:
            if PYSNOWBALL_AVAILABLE:
                result = ball.quotec(symbol)
                if result and 'data' in result and result['data']:
                    return result['data'][0]
            return None
        except Exception as e:
            logger.error(f"❌ 获取股票行情失败: {e}")
            return None
    
    def _get_cube_name(self, cube_symbol: str) -> str:
        """获取组合名称"""
        for cube in self.available_cubes:
            if cube['symbol'] == cube_symbol:
                return cube['name']
        return cube_symbol
    
    def _save_trade_record(self, trade_record: Dict):
        """保存交易记录"""
        try:
            filename = 'xueqiu_trade_records.json'
            
            # 读取现有记录
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    records = json.load(f)
            except FileNotFoundError:
                records = []
            
            # 添加新记录
            records.append(trade_record)
            
            # 保存记录
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(records, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"❌ 保存交易记录失败: {e}")
    
    def get_trade_records(self, limit: int = 20) -> List[Dict]:
        """获取交易记录"""
        try:
            filename = 'xueqiu_trade_records.json'
            with open(filename, 'r', encoding='utf-8') as f:
                records = json.load(f)
            
            # 返回最近的记录
            return records[-limit:] if len(records) > limit else records
            
        except FileNotFoundError:
            return []
        except Exception as e:
            logger.error(f"❌ 获取交易记录失败: {e}")
            return []

def main():
    """主函数 - 测试真实雪球模拟交易"""
    print("🚀 真实雪球模拟交易系统测试")
    print("=" * 50)
    
    # 创建交易器
    trader = RealXueqiuTrader()
    
    if not trader.available_cubes:
        print("❌ 没有可用的组合")
        return
    
    # 显示可用组合
    print("\n📊 可用组合:")
    for cube in trader.available_cubes:
        print(f"   {cube['symbol']}: {cube['name']} (净值: {cube['nav']})")
    
    # 获取当前持仓
    print(f"\n💼 当前持仓 ({trader.current_cube}):")
    position = trader.get_current_position()
    if position:
        print(f"   组合: {position['cube_name']}")
        print(f"   持仓数量: {position['total_holdings']}")
        for holding in position['holdings'][:5]:
            print(f"     {holding['stock_name']} ({holding['stock_symbol']}): {holding['weight']:.1f}%")
    
    # 获取组合净值
    print(f"\n📈 组合净值:")
    nav_info = trader.get_cube_nav()
    if nav_info:
        print(f"   当前净值: {nav_info['current_nav']}")
        print(f"   总收益: {nav_info['total_gain']}%")
        print(f"   日收益: {nav_info['daily_gain']}%")
    
    # 模拟交易测试
    print(f"\n🔄 模拟交易测试:")
    buy_result = trader.simulate_buy('SH600519', 10.0)
    print(f"   买入结果: {buy_result['message']}")
    
    sell_result = trader.simulate_sell('SH600519', 5.0)
    print(f"   卖出结果: {sell_result['message']}")
    
    # 显示交易记录
    print(f"\n📝 交易记录:")
    records = trader.get_trade_records(5)
    for record in records[-3:]:
        action = "买入" if record['action'] == 'buy' else "卖出"
        print(f"   {record['timestamp'][:19]}: {action} {record['stock_name']} 权重: {record['target_weight']}%")

if __name__ == '__main__':
    main()
