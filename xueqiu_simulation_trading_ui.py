#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
雪球模拟交易界面
基于真实雪球API的专业模拟交易系统
"""

from flask import Flask, request, jsonify
import json
import logging
import pysnowball as ball
from datetime import datetime

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

class XueqiuSimulationTrader:
    """雪球模拟交易器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.simulation_portfolio_id = -4  # 模拟组合ID
        self.load_token()
    
    def load_token(self):
        """加载token"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logging.info(f"✅ Token设置成功: u={u}")
                return True
            return False
        except Exception as e:
            logging.error(f"❌ 加载token失败: {e}")
            return False
    
    def get_portfolio_holdings(self):
        """获取模拟组合真实持仓信息（仅来自雪球API）"""
        try:
            # 获取基础持仓信息
            result = ball.watch_stock(self.simulation_portfolio_id)
            if not result or 'data' not in result:
                return []

            stocks = result['data'].get('stocks', [])
            holdings = []

            for stock in stocks:
                symbol = stock.get('symbol', '')

                # 获取实时行情
                quote = ball.quotec(symbol)
                quote_data = quote['data'][0] if quote and 'data' in quote and quote['data'] else {}

                # 尝试获取雪球组合的真实持仓信息
                position_info = self.get_real_position_from_xueqiu(symbol)

                current_price = quote_data.get('current', 0)

                holding = {
                    'symbol': symbol,
                    'name': stock.get('name', ''),

                    # 真实持仓信息（如果无法获取则显示为未知）
                    'shares_held': position_info.get('shares', 'N/A'),
                    'avg_cost': position_info.get('avg_cost', 'N/A'),
                    'total_cost': position_info.get('total_cost', 'N/A'),
                    'current_value': position_info.get('current_value', 'N/A'),
                    'unrealized_pnl': position_info.get('unrealized_pnl', 'N/A'),
                    'unrealized_pnl_percent': position_info.get('unrealized_pnl_percent', 'N/A'),
                    'realized_pnl': position_info.get('realized_pnl', 'N/A'),
                    'last_trade_time': position_info.get('last_trade_time', 'N/A'),
                    'first_buy_time': position_info.get('first_buy_time', 'N/A'),

                    # 实时行情数据（来自雪球API）
                    'current_price': current_price,
                    'chg': quote_data.get('chg', 0),
                    'percent': quote_data.get('percent', 0),
                    'volume': quote_data.get('volume', 0),
                    'amount': quote_data.get('amount', 0),
                    'market_capital': quote_data.get('market_capital', 0),
                    'open': quote_data.get('open', 0),
                    'high': quote_data.get('high', 0),
                    'low': quote_data.get('low', 0),
                    'last_close': quote_data.get('last_close', 0)
                }
                holdings.append(holding)

            return holdings

        except Exception as e:
            logging.error(f"❌ 获取持仓失败: {e}")
            return []

    def get_real_position_from_xueqiu(self, symbol):
        """尝试从雪球API获取真实持仓信息"""
        try:
            # 尝试多种方法获取雪球的真实持仓数据

            # 方法1: 尝试获取组合调仓记录
            try:
                rebalancing_result = ball.rebalancing_history(f"ZH{self.simulation_portfolio_id:06d}")
                if rebalancing_result and 'list' in rebalancing_result:
                    # 从调仓历史中分析持仓
                    for rebalancing in rebalancing_result['list']:
                        holdings = rebalancing.get('holdings', [])
                        for holding in holdings:
                            if holding.get('stock_symbol') == symbol:
                                return {
                                    'shares': holding.get('volume', 'N/A'),
                                    'avg_cost': holding.get('price', 'N/A'),
                                    'weight': holding.get('weight', 'N/A'),
                                    'last_trade_time': rebalancing.get('created_at', 'N/A')
                                }
            except Exception as e:
                logging.debug(f"方法1失败: {e}")

            # 方法2: 尝试获取当前持仓
            try:
                current_result = ball.rebalancing_current(f"ZH{self.simulation_portfolio_id:06d}")
                if current_result and 'last_rb' in current_result:
                    holdings = current_result['last_rb'].get('holdings', [])
                    for holding in holdings:
                        if holding.get('stock_symbol') == symbol:
                            return {
                                'shares': holding.get('volume', 'N/A'),
                                'avg_cost': holding.get('price', 'N/A'),
                                'weight': holding.get('weight', 'N/A'),
                                'last_trade_time': current_result['last_rb'].get('created_at', 'N/A')
                            }
            except Exception as e:
                logging.debug(f"方法2失败: {e}")

            # 方法3: 尝试从用户交易记录获取（如果有API）
            try:
                # 这里可以尝试其他雪球API来获取真实交易记录
                # 但目前pysnowball可能没有提供这样的API
                pass
            except Exception as e:
                logging.debug(f"方法3失败: {e}")

            # 如果所有方法都失败，返回空数据
            logging.warning(f"无法获取 {symbol} 的真实持仓信息，所有API方法都失败")
            return {
                'shares': 'N/A',
                'avg_cost': 'N/A',
                'total_cost': 'N/A',
                'current_value': 'N/A',
                'unrealized_pnl': 'N/A',
                'unrealized_pnl_percent': 'N/A',
                'realized_pnl': 'N/A',
                'last_trade_time': 'N/A',
                'first_buy_time': 'N/A'
            }

        except Exception as e:
            logging.error(f"❌ 获取真实持仓信息失败: {e}")
            return {
                'shares': 'N/A',
                'avg_cost': 'N/A',
                'total_cost': 'N/A',
                'current_value': 'N/A',
                'unrealized_pnl': 'N/A',
                'unrealized_pnl_percent': 'N/A',
                'realized_pnl': 'N/A',
                'last_trade_time': 'N/A',
                'first_buy_time': 'N/A'
            }
    
    def get_stock_quote(self, symbol):
        """获取股票行情"""
        try:
            result = ball.quotec(symbol)
            if result and 'data' in result and result['data']:
                return result['data'][0]
            return None
        except Exception as e:
            logging.error(f"❌ 获取行情失败: {e}")
            return None
    
    def get_stock_pankou(self, symbol):
        """获取五档行情"""
        try:
            result = ball.pankou(symbol)
            return result if result else None
        except Exception as e:
            logging.error(f"❌ 获取五档行情失败: {e}")
            return None
    
    def search_stocks(self, query):
        """搜索股票"""
        try:
            result = ball.suggest_stock(query)
            if result and 'data' in result:
                return result['data']
            return []
        except Exception as e:
            logging.error(f"❌ 搜索股票失败: {e}")
            return []

# 全局交易器实例
trader = XueqiuSimulationTrader()

@app.route('/')
def index():
    """主页"""
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雪球模拟交易系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); 
            min-height: 100vh; 
            color: #333;
        }
        .container { 
            max-width: 1600px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header { 
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4); 
            color: white; 
            padding: 20px; 
            border-radius: 15px; 
            text-align: center; 
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .trading-grid { 
            display: grid; 
            grid-template-columns: 1fr 1fr 1fr; 
            gap: 20px; 
            margin-bottom: 20px;
        }
        .panel { 
            background: rgba(255,255,255,0.95); 
            border-radius: 15px; 
            padding: 20px; 
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .panel h3 { 
            color: #2c3e50; 
            margin-bottom: 15px; 
            padding-bottom: 10px; 
            border-bottom: 3px solid #3498db;
            font-size: 18px;
        }
        .btn { 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 14px; 
            font-weight: bold;
            transition: all 0.3s;
            margin: 5px;
        }
        .btn-primary { background: linear-gradient(45deg, #3498db, #2980b9); color: white; }
        .btn-success { background: linear-gradient(45deg, #2ecc71, #27ae60); color: white; }
        .btn-danger { background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0,0,0,0.2); }
        
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; }
        .form-group input, .form-group select { 
            width: 100%; 
            padding: 12px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            font-size: 14px;
            transition: border-color 0.3s;
        }
        .form-group input:focus { border-color: #3498db; outline: none; }
        
        .holdings-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 13px;
            overflow-x: auto;
            display: block;
            white-space: nowrap;
        }
        .holdings-table thead, .holdings-table tbody { display: table; width: 100%; }
        .holdings-table th, .holdings-table td {
            padding: 8px 6px;
            text-align: center;
            border-bottom: 1px solid #eee;
            min-width: 80px;
        }
        .holdings-table th {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
            font-weight: bold;
            font-size: 12px;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .holdings-table tr:hover { background: #f8f9fa; }
        .holdings-table td:first-child { text-align: left; font-weight: bold; }
        .holdings-table td:nth-child(2) { font-family: 'Courier New', monospace; }
        
        .price-up { color: #e74c3c; font-weight: bold; }
        .price-down { color: #27ae60; font-weight: bold; }
        .price-flat { color: #7f8c8d; }
        
        .search-results { 
            max-height: 200px; 
            overflow-y: auto; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            margin-top: 10px;
        }
        .search-item { 
            padding: 12px; 
            border-bottom: 1px solid #f0f0f0; 
            cursor: pointer; 
            transition: background 0.3s;
        }
        .search-item:hover { background: #e3f2fd; }
        
        .pankou-table { width: 100%; border-collapse: collapse; font-family: 'Courier New', monospace; }
        .pankou-table th, .pankou-table td { 
            padding: 8px; 
            text-align: center; 
            border: 1px solid #ddd; 
        }
        .pankou-table th { background: #f8f9fa; }
        .sell-price { color: #27ae60; font-weight: bold; }
        .buy-price { color: #e74c3c; font-weight: bold; }
        
        .status-indicator { 
            display: inline-block; 
            width: 12px; 
            height: 12px; 
            border-radius: 50%; 
            margin-right: 8px;
        }
        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        
        .loading { text-align: center; padding: 20px; color: #7f8c8d; }
        .alert { 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 8px; 
            display: none;
        }
        .alert.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 雪球模拟交易系统</h1>
            <p>基于真实雪球API的专业模拟交易平台</p>
            <div>
                <span class="status-indicator status-online"></span>
                <span>已连接到雪球 | 用户: 4380321271</span>
            </div>
        </div>
        
        <div class="trading-grid">
            <!-- 左侧：持仓管理 -->
            <div class="panel">
                <h3>💼 我的持仓</h3>
                <button class="btn btn-primary" onclick="refreshHoldings()">刷新持仓</button>
                
                <div id="holdingsContainer">
                    <div class="loading">加载中...</div>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>📊 组合统计</h4>
                    <div id="portfolioStats">
                        <div>总市值: <span id="totalValue">--</span></div>
                        <div>今日盈亏: <span id="todayPnL">--</span></div>
                        <div>持仓数量: <span id="holdingCount">--</span></div>
                    </div>
                </div>
            </div>
            
            <!-- 中间：行情查看 -->
            <div class="panel">
                <h3>📊 实时行情</h3>
                
                <div class="form-group">
                    <label>搜索股票:</label>
                    <input type="text" id="searchInput" placeholder="输入股票名称或代码">
                    <button class="btn btn-primary" onclick="searchStocks()">搜索</button>
                </div>
                
                <div id="searchResults" class="search-results" style="display: none;"></div>
                
                <div class="form-group">
                    <label>当前股票:</label>
                    <input type="text" id="currentStock" value="SH600507" placeholder="股票代码">
                    <button class="btn btn-primary" onclick="loadQuote()">查看行情</button>
                </div>
                
                <div id="quoteDisplay">
                    <div class="loading">请输入股票代码</div>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>📈 五档买卖盘</h4>
                    <div id="pankouDisplay">
                        <div class="loading">请先选择股票</div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧：模拟交易 -->
            <div class="panel">
                <h3>🔄 模拟交易</h3>
                <div class="alert info" style="display: block; margin-bottom: 15px;">
                    <strong>说明：</strong>当前仅显示雪球真实数据，持仓详情需要雪球组合API权限。模拟交易仅作记录，不影响真实账户。
                </div>
                
                <div class="form-group">
                    <label>交易股票:</label>
                    <input type="text" id="tradeStock" placeholder="股票代码">
                </div>
                
                <div class="form-group">
                    <label>交易类型:</label>
                    <select id="tradeType">
                        <option value="buy">买入</option>
                        <option value="sell">卖出</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>交易数量:</label>
                    <input type="number" id="tradeAmount" placeholder="股数" min="100" step="100">
                </div>
                
                <div class="form-group">
                    <label>交易价格:</label>
                    <input type="number" id="tradePrice" placeholder="价格" step="0.01">
                    <button class="btn btn-primary" onclick="useCurrentPrice()">使用当前价</button>
                </div>
                
                <div class="form-group">
                    <button class="btn btn-success" onclick="submitTrade()">提交交易</button>
                    <button class="btn btn-danger" onclick="clearForm()">清空表单</button>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>📝 交易记录</h4>
                    <div id="tradeHistory">
                        <div class="loading">暂无交易记录</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="alertBox" class="alert"></div>
    </div>

    <script>
        let currentQuote = null;
        
        function showAlert(message, type = 'info') {
            const alertBox = document.getElementById('alertBox');
            alertBox.className = `alert ${type}`;
            alertBox.textContent = message;
            alertBox.style.display = 'block';
            
            setTimeout(() => {
                alertBox.style.display = 'none';
            }, 5000);
        }
        
        function refreshHoldings() {
            fetch('/api/holdings')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('holdingsContainer');
                    
                    if (data.success) {
                        let html = '<table class="holdings-table">';
                        html += '<tr>';
                        html += '<th>股票名称</th>';
                        html += '<th>代码</th>';
                        html += '<th>持仓</th>';
                        html += '<th>现价</th>';
                        html += '<th>摊薄成本</th>';
                        html += '<th>当前市值</th>';
                        html += '<th>浮动盈亏</th>';
                        html += '<th>累计盈亏</th>';
                        html += '<th>涨跌幅</th>';
                        html += '<th>交易时间</th>';
                        html += '</tr>';

                        let totalValue = 0;
                        let totalCost = 0;
                        let totalUnrealizedPnL = 0;
                        let totalRealizedPnL = 0;

                        data.holdings.forEach(holding => {
                            const priceClass = holding.percent > 0 ? 'price-up' :
                                             holding.percent < 0 ? 'price-down' : 'price-flat';

                            const pnlClass = holding.unrealized_pnl > 0 ? 'price-up' :
                                           holding.unrealized_pnl < 0 ? 'price-down' : 'price-flat';

                            const realizedPnlClass = holding.realized_pnl > 0 ? 'price-up' :
                                                   holding.realized_pnl < 0 ? 'price-down' : 'price-flat';

                            // 安全显示数值，处理N/A情况
                            const formatValue = (value, prefix = '', suffix = '') => {
                                if (value === 'N/A' || value === null || value === undefined) {
                                    return 'N/A';
                                }
                                if (typeof value === 'number') {
                                    return prefix + value.toFixed(2) + suffix;
                                }
                                return value;
                            };

                            const formatShares = (shares) => {
                                if (shares === 'N/A' || shares === null || shares === undefined) {
                                    return 'N/A';
                                }
                                return shares + '股';
                            };

                            html += `<tr onclick="selectStock('${holding.symbol}')" style="cursor: pointer;">`;
                            html += `<td><strong>${holding.name}</strong></td>`;
                            html += `<td>${holding.symbol}</td>`;
                            html += `<td>${formatShares(holding.shares_held)}</td>`;
                            html += `<td class="${priceClass}">¥${holding.current_price.toFixed(2)}</td>`;
                            html += `<td>${formatValue(holding.avg_cost, '¥')}</td>`;
                            html += `<td>${formatValue(holding.current_value, '¥')}</td>`;
                            html += `<td class="${pnlClass}">${formatValue(holding.unrealized_pnl, '¥')}<br><small>(${formatValue(holding.unrealized_pnl_percent, '', '%')})</small></td>`;
                            html += `<td class="${realizedPnlClass}">${formatValue(holding.realized_pnl, '¥')}</td>`;
                            html += `<td class="${priceClass}">${holding.percent.toFixed(2)}%<br><small>${holding.chg > 0 ? '+' : ''}${holding.chg.toFixed(2)}</small></td>`;
                            html += `<td><small>${holding.last_trade_time !== 'N/A' && holding.last_trade_time ? holding.last_trade_time.split('T')[0] : 'N/A'}</small></td>`;
                            html += '</tr>';

                            // 只累加有效的数值
                            if (typeof holding.current_value === 'number') {
                                totalValue += holding.current_value;
                            }
                            if (typeof holding.total_cost === 'number') {
                                totalCost += holding.total_cost;
                            }
                            if (typeof holding.unrealized_pnl === 'number') {
                                totalUnrealizedPnL += holding.unrealized_pnl;
                            }
                            if (typeof holding.realized_pnl === 'number') {
                                totalRealizedPnL += holding.realized_pnl;
                            }
                        });

                        html += '</table>';
                        container.innerHTML = html;

                        // 更新统计信息
                        const totalPnL = totalUnrealizedPnL + totalRealizedPnL;
                        const totalPnLPercent = totalCost > 0 ? (totalPnL / totalCost) * 100 : 0;

                        document.getElementById('totalValue').textContent = `¥${totalValue.toFixed(2)}`;
                        document.getElementById('todayPnL').innerHTML = `
                            <span class="${totalUnrealizedPnL > 0 ? 'price-up' : totalUnrealizedPnL < 0 ? 'price-down' : 'price-flat'}">
                                ¥${totalUnrealizedPnL.toFixed(2)} (${totalPnLPercent.toFixed(2)}%)
                            </span>
                        `;
                        document.getElementById('holdingCount').textContent = `${data.holdings.length}只`;

                        // 添加更多统计信息
                        const statsDiv = document.getElementById('portfolioStats');
                        statsDiv.innerHTML = `
                            <div>总市值: <span id="totalValue">¥${totalValue.toFixed(2)}</span></div>
                            <div>总成本: <span>¥${totalCost.toFixed(2)}</span></div>
                            <div>浮动盈亏: <span id="todayPnL" class="${totalUnrealizedPnL > 0 ? 'price-up' : totalUnrealizedPnL < 0 ? 'price-down' : 'price-flat'}">¥${totalUnrealizedPnL.toFixed(2)} (${totalPnLPercent.toFixed(2)}%)</span></div>
                            <div>累计盈亏: <span class="${totalRealizedPnL > 0 ? 'price-up' : totalRealizedPnL < 0 ? 'price-down' : 'price-flat'}">¥${totalRealizedPnL.toFixed(2)}</span></div>
                            <div>持仓数量: <span id="holdingCount">${data.holdings.length}只</span></div>
                        `;

                        showAlert('持仓刷新成功', 'success');
                    } else {
                        container.innerHTML = `<div class="alert error">${data.error}</div>`;
                        showAlert(`刷新失败: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    showAlert(`刷新异常: ${error}`, 'error');
                });
        }
        
        function searchStocks() {
            const query = document.getElementById('searchInput').value;
            if (!query) {
                showAlert('请输入搜索关键词', 'error');
                return;
            }
            
            fetch(`/api/search?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    const resultsDiv = document.getElementById('searchResults');
                    
                    if (data.success && data.stocks.length > 0) {
                        let html = '';
                        data.stocks.slice(0, 8).forEach(stock => {
                            html += `<div class="search-item" onclick="selectStock('${stock.code}')">`;
                            html += `<strong>${stock.query}</strong> (${stock.code})`;
                            html += '</div>';
                        });
                        resultsDiv.innerHTML = html;
                        resultsDiv.style.display = 'block';
                    } else {
                        resultsDiv.innerHTML = '<div class="search-item">未找到相关股票</div>';
                        resultsDiv.style.display = 'block';
                    }
                })
                .catch(error => {
                    showAlert(`搜索失败: ${error}`, 'error');
                });
        }
        
        function selectStock(symbol) {
            document.getElementById('currentStock').value = symbol;
            document.getElementById('tradeStock').value = symbol;
            document.getElementById('searchResults').style.display = 'none';
            loadQuote();
        }
        
        function loadQuote() {
            const symbol = document.getElementById('currentStock').value;
            if (!symbol) {
                showAlert('请输入股票代码', 'error');
                return;
            }
            
            // 获取实时行情
            fetch(`/api/quote/${symbol}`)
                .then(response => response.json())
                .then(data => {
                    const quoteDiv = document.getElementById('quoteDisplay');
                    
                    if (data.success) {
                        const quote = data.quote;
                        currentQuote = quote;
                        
                        const priceClass = quote.percent > 0 ? 'price-up' : 
                                         quote.percent < 0 ? 'price-down' : 'price-flat';
                        
                        quoteDiv.innerHTML = `
                            <h4>${quote.name || symbol}</h4>
                            <div style="font-size: 24px; margin: 10px 0;">
                                <span class="${priceClass}">¥${quote.current}</span>
                                <span class="${priceClass}" style="font-size: 16px;">
                                    ${quote.chg} (${quote.percent}%)
                                </span>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
                                <div>开盘: ¥${quote.open}</div>
                                <div>最高: ¥${quote.high}</div>
                                <div>最低: ¥${quote.low}</div>
                                <div>昨收: ¥${quote.last_close}</div>
                                <div>成交量: ${(quote.volume / 10000).toFixed(0)}万</div>
                                <div>成交额: ${(quote.amount / 100000000).toFixed(2)}亿</div>
                            </div>
                        `;
                        
                        // 自动填入交易价格
                        document.getElementById('tradePrice').value = quote.current;
                        
                        showAlert('行情加载成功', 'success');
                    } else {
                        quoteDiv.innerHTML = `<div class="alert error">${data.error}</div>`;
                        showAlert(`行情加载失败: ${data.error}`, 'error');
                    }
                });
            
            // 获取五档行情
            fetch(`/api/pankou/${symbol}`)
                .then(response => response.json())
                .then(data => {
                    const pankouDiv = document.getElementById('pankouDisplay');
                    
                    if (data.success && data.pankou) {
                        const p = data.pankou;
                        pankouDiv.innerHTML = `
                            <table class="pankou-table">
                                <tr><th>档位</th><th>卖价</th><th>卖量</th><th>买价</th><th>买量</th></tr>
                                <tr><td>五</td><td class="sell-price">${p.sp5 || '--'}</td><td>${p.sc5 || '--'}</td><td class="buy-price">${p.bp5 || '--'}</td><td>${p.bc5 || '--'}</td></tr>
                                <tr><td>四</td><td class="sell-price">${p.sp4 || '--'}</td><td>${p.sc4 || '--'}</td><td class="buy-price">${p.bp4 || '--'}</td><td>${p.bc4 || '--'}</td></tr>
                                <tr><td>三</td><td class="sell-price">${p.sp3 || '--'}</td><td>${p.sc3 || '--'}</td><td class="buy-price">${p.bp3 || '--'}</td><td>${p.bc3 || '--'}</td></tr>
                                <tr><td>二</td><td class="sell-price">${p.sp2 || '--'}</td><td>${p.sc2 || '--'}</td><td class="buy-price">${p.bp2 || '--'}</td><td>${p.bc2 || '--'}</td></tr>
                                <tr><td>一</td><td class="sell-price">${p.sp1 || '--'}</td><td>${p.sc1 || '--'}</td><td class="buy-price">${p.bp1 || '--'}</td><td>${p.bc1 || '--'}</td></tr>
                            </table>
                        `;
                    } else {
                        pankouDiv.innerHTML = '<div class="loading">五档行情暂不可用</div>';
                    }
                });
        }
        
        function useCurrentPrice() {
            if (currentQuote && currentQuote.current) {
                document.getElementById('tradePrice').value = currentQuote.current;
                showAlert('已使用当前价格', 'info');
            } else {
                showAlert('请先加载股票行情', 'error');
            }
        }
        
        function submitTrade() {
            const stock = document.getElementById('tradeStock').value;
            const type = document.getElementById('tradeType').value;
            const amount = document.getElementById('tradeAmount').value;
            const price = document.getElementById('tradePrice').value;
            
            if (!stock || !amount || !price) {
                showAlert('请填写完整的交易信息', 'error');
                return;
            }
            
            const tradeData = {
                symbol: stock,
                type: type,
                amount: parseInt(amount),
                price: parseFloat(price)
            };
            
            fetch('/api/trade', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(tradeData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`${type === 'buy' ? '买入' : '卖出'}成功`, 'success');
                    clearForm();
                    refreshHoldings();
                    loadTradeHistory();
                } else {
                    showAlert(`交易失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showAlert(`交易异常: ${error}`, 'error');
            });
        }
        
        function clearForm() {
            document.getElementById('tradeStock').value = '';
            document.getElementById('tradeAmount').value = '';
            document.getElementById('tradePrice').value = '';
        }
        
        function loadTradeHistory() {
            fetch('/api/trade_history')
                .then(response => response.json())
                .then(data => {
                    const historyDiv = document.getElementById('tradeHistory');

                    if (data.success && data.trades.length > 0) {
                        let html = '<div style="max-height: 300px; overflow-y: auto;">';
                        html += '<table class="holdings-table" style="font-size: 11px;">';
                        html += '<tr><th>时间</th><th>股票</th><th>操作</th><th>数量</th><th>价格</th><th>金额</th></tr>';

                        data.trades.forEach(trade => {
                            const typeClass = trade.type === 'buy' ? 'price-up' : 'price-down';
                            const typeText = trade.type === 'buy' ? '买入' : '卖出';
                            const time = new Date(trade.timestamp).toLocaleString('zh-CN', {
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit'
                            });

                            html += '<tr>';
                            html += `<td><small>${time}</small></td>`;
                            html += `<td><small>${trade.name || trade.symbol}</small></td>`;
                            html += `<td class="${typeClass}"><small>${typeText}</small></td>`;
                            html += `<td><small>${trade.amount}</small></td>`;
                            html += `<td><small>¥${trade.price.toFixed(2)}</small></td>`;
                            html += `<td><small>¥${trade.total_value.toFixed(2)}</small></td>`;
                            html += '</tr>';
                        });

                        html += '</table></div>';
                        historyDiv.innerHTML = html;
                    } else {
                        historyDiv.innerHTML = '<div class="loading">暂无交易记录</div>';
                    }
                })
                .catch(error => {
                    const historyDiv = document.getElementById('tradeHistory');
                    historyDiv.innerHTML = '<div class="loading">加载交易记录失败</div>';
                });
        }
        
        // 页面加载时初始化
        window.onload = function() {
            refreshHoldings();
            loadQuote(); // 加载默认股票（方大特钢）
            loadTradeHistory(); // 加载交易记录
        };
        
        // 定时刷新行情（每30秒）
        setInterval(() => {
            const currentStock = document.getElementById('currentStock').value;
            if (currentStock) {
                loadQuote();
            }
        }, 30000);
    </script>
</body>
</html>
    '''

# API路由
@app.route('/api/holdings')
def get_holdings():
    """获取持仓"""
    try:
        holdings = trader.get_portfolio_holdings()
        return jsonify({
            'success': True,
            'holdings': holdings
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/quote/<symbol>')
def get_quote(symbol):
    """获取股票行情"""
    try:
        quote = trader.get_stock_quote(symbol)
        if quote:
            return jsonify({
                'success': True,
                'quote': quote
            })
        else:
            return jsonify({
                'success': False,
                'error': '无法获取行情数据'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/pankou/<symbol>')
def get_pankou(symbol):
    """获取五档行情"""
    try:
        pankou = trader.get_stock_pankou(symbol)
        return jsonify({
            'success': True,
            'pankou': pankou
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/search')
def search_stocks():
    """搜索股票"""
    try:
        query = request.args.get('q', '')
        stocks = trader.search_stocks(query)
        return jsonify({
            'success': True,
            'stocks': stocks
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/trade', methods=['POST'])
def submit_trade():
    """提交模拟交易"""
    try:
        data = request.get_json()
        symbol = data.get('symbol')
        trade_type = data.get('type')
        amount = data.get('amount')
        price = data.get('price')

        # 获取股票名称
        stock_name = symbol
        try:
            quote = trader.get_stock_quote(symbol)
            if quote and 'name' in quote:
                stock_name = quote['name']
        except:
            pass

        # 创建交易记录
        trade_record = {
            'id': datetime.now().strftime('%Y%m%d%H%M%S') + str(hash(symbol + str(amount)))[-4:],
            'symbol': symbol,
            'name': stock_name,
            'type': trade_type,
            'amount': amount,
            'price': price,
            'timestamp': datetime.now().isoformat(),
            'total_value': amount * price,
            'commission': amount * price * 0.0003,  # 模拟手续费0.03%
            'status': 'completed'
        }

        # 基本验证（不验证持仓，因为这只是记录模拟交易意图）
        if not symbol or not trade_type or not amount or not price:
            return jsonify({
                'success': False,
                'error': '交易信息不完整'
            })

        if amount <= 0 or price <= 0:
            return jsonify({
                'success': False,
                'error': '数量和价格必须大于0'
            })

        if amount % 100 != 0:
            return jsonify({
                'success': False,
                'error': '股票交易数量必须是100的整数倍'
            })

        # 保存交易记录到文件
        try:
            with open('simulation_trades.json', 'r', encoding='utf-8') as f:
                trades = json.load(f)
        except FileNotFoundError:
            trades = []

        trades.append(trade_record)

        with open('simulation_trades.json', 'w', encoding='utf-8') as f:
            json.dump(trades, f, indent=2, ensure_ascii=False)

        logging.info(f"📝 模拟交易记录: {trade_type} {symbol} {amount}股 @¥{price}")

        return jsonify({
            'success': True,
            'message': f'模拟{trade_type}成功',
            'trade_record': trade_record
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def validate_trade(symbol, trade_type, amount, price):
    """验证交易"""
    try:
        # 基本验证
        if not symbol or not trade_type or not amount or not price:
            return {'valid': False, 'error': '交易信息不完整'}

        if amount <= 0 or price <= 0:
            return {'valid': False, 'error': '数量和价格必须大于0'}

        if amount % 100 != 0:
            return {'valid': False, 'error': '股票交易数量必须是100的整数倍'}

        # 卖出验证
        if trade_type == 'sell':
            # 检查是否有足够的持仓
            position = trader.get_position_details(symbol)
            current_shares = position.get('shares', 0)

            if current_shares < amount:
                return {'valid': False, 'error': f'持仓不足，当前持有{current_shares}股，无法卖出{amount}股'}

        return {'valid': True, 'error': None}

    except Exception as e:
        return {'valid': False, 'error': f'验证失败: {str(e)}'}

@app.route('/api/trade_history')
def get_trade_history():
    """获取交易历史"""
    try:
        try:
            with open('simulation_trades.json', 'r', encoding='utf-8') as f:
                trades = json.load(f)
        except FileNotFoundError:
            trades = []

        # 按时间倒序排列
        trades.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

        return jsonify({
            'success': True,
            'trades': trades[-20:]  # 返回最近20条记录
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def main():
    """主函数"""
    print("🚀 雪球模拟交易界面")
    print("基于真实雪球API的专业模拟交易系统")
    print("=" * 60)
    
    print("\n🌐 启动Web服务器...")
    print("访问地址: http://localhost:5006")
    print("功能特点:")
    print("  📊 真实雪球持仓数据")
    print("  📈 实时股票行情")
    print("  📋 五档买卖盘")
    print("  🔄 模拟交易功能")
    print("  💼 持仓管理")
    print("  🔍 股票搜索")
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5006, debug=True)

if __name__ == '__main__':
    main()
