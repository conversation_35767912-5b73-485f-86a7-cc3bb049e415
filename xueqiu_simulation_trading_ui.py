#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
雪球模拟交易界面
基于真实雪球API的专业模拟交易系统
"""

from flask import Flask, request, jsonify
import json
import logging
import pysnowball as ball
from datetime import datetime

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

class XueqiuSimulationTrader:
    """雪球模拟交易器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.simulation_portfolio_id = -4  # 模拟组合ID
        self.load_token()
    
    def load_token(self):
        """加载token"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logging.info(f"✅ Token设置成功: u={u}")
                return True
            return False
        except Exception as e:
            logging.error(f"❌ 加载token失败: {e}")
            return False
    
    def get_portfolio_holdings(self):
        """获取模拟组合持仓"""
        try:
            result = ball.watch_stock(self.simulation_portfolio_id)
            if result and 'data' in result:
                stocks = result['data'].get('stocks', [])
                
                holdings = []
                for stock in stocks:
                    # 获取实时行情
                    quote = ball.quotec(stock.get('symbol', ''))
                    quote_data = quote['data'][0] if quote and 'data' in quote and quote['data'] else {}
                    
                    holding = {
                        'symbol': stock.get('symbol', ''),
                        'name': stock.get('name', ''),
                        'current_price': quote_data.get('current', 0),
                        'chg': quote_data.get('chg', 0),
                        'percent': quote_data.get('percent', 0),
                        'volume': quote_data.get('volume', 0),
                        'amount': quote_data.get('amount', 0),
                        'market_capital': quote_data.get('market_capital', 0)
                    }
                    holdings.append(holding)
                
                return holdings
            return []
        except Exception as e:
            logging.error(f"❌ 获取持仓失败: {e}")
            return []
    
    def get_stock_quote(self, symbol):
        """获取股票行情"""
        try:
            result = ball.quotec(symbol)
            if result and 'data' in result and result['data']:
                return result['data'][0]
            return None
        except Exception as e:
            logging.error(f"❌ 获取行情失败: {e}")
            return None
    
    def get_stock_pankou(self, symbol):
        """获取五档行情"""
        try:
            result = ball.pankou(symbol)
            return result if result else None
        except Exception as e:
            logging.error(f"❌ 获取五档行情失败: {e}")
            return None
    
    def search_stocks(self, query):
        """搜索股票"""
        try:
            result = ball.suggest_stock(query)
            if result and 'data' in result:
                return result['data']
            return []
        except Exception as e:
            logging.error(f"❌ 搜索股票失败: {e}")
            return []

# 全局交易器实例
trader = XueqiuSimulationTrader()

@app.route('/')
def index():
    """主页"""
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雪球模拟交易系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); 
            min-height: 100vh; 
            color: #333;
        }
        .container { 
            max-width: 1600px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header { 
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4); 
            color: white; 
            padding: 20px; 
            border-radius: 15px; 
            text-align: center; 
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .trading-grid { 
            display: grid; 
            grid-template-columns: 1fr 1fr 1fr; 
            gap: 20px; 
            margin-bottom: 20px;
        }
        .panel { 
            background: rgba(255,255,255,0.95); 
            border-radius: 15px; 
            padding: 20px; 
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .panel h3 { 
            color: #2c3e50; 
            margin-bottom: 15px; 
            padding-bottom: 10px; 
            border-bottom: 3px solid #3498db;
            font-size: 18px;
        }
        .btn { 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 14px; 
            font-weight: bold;
            transition: all 0.3s;
            margin: 5px;
        }
        .btn-primary { background: linear-gradient(45deg, #3498db, #2980b9); color: white; }
        .btn-success { background: linear-gradient(45deg, #2ecc71, #27ae60); color: white; }
        .btn-danger { background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0,0,0,0.2); }
        
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; }
        .form-group input, .form-group select { 
            width: 100%; 
            padding: 12px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            font-size: 14px;
            transition: border-color 0.3s;
        }
        .form-group input:focus { border-color: #3498db; outline: none; }
        
        .holdings-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .holdings-table th, .holdings-table td { 
            padding: 12px; 
            text-align: left; 
            border-bottom: 1px solid #eee; 
        }
        .holdings-table th { 
            background: linear-gradient(45deg, #34495e, #2c3e50); 
            color: white; 
            font-weight: bold;
        }
        .holdings-table tr:hover { background: #f8f9fa; }
        
        .price-up { color: #e74c3c; font-weight: bold; }
        .price-down { color: #27ae60; font-weight: bold; }
        .price-flat { color: #7f8c8d; }
        
        .search-results { 
            max-height: 200px; 
            overflow-y: auto; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            margin-top: 10px;
        }
        .search-item { 
            padding: 12px; 
            border-bottom: 1px solid #f0f0f0; 
            cursor: pointer; 
            transition: background 0.3s;
        }
        .search-item:hover { background: #e3f2fd; }
        
        .pankou-table { width: 100%; border-collapse: collapse; font-family: 'Courier New', monospace; }
        .pankou-table th, .pankou-table td { 
            padding: 8px; 
            text-align: center; 
            border: 1px solid #ddd; 
        }
        .pankou-table th { background: #f8f9fa; }
        .sell-price { color: #27ae60; font-weight: bold; }
        .buy-price { color: #e74c3c; font-weight: bold; }
        
        .status-indicator { 
            display: inline-block; 
            width: 12px; 
            height: 12px; 
            border-radius: 50%; 
            margin-right: 8px;
        }
        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        
        .loading { text-align: center; padding: 20px; color: #7f8c8d; }
        .alert { 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 8px; 
            display: none;
        }
        .alert.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 雪球模拟交易系统</h1>
            <p>基于真实雪球API的专业模拟交易平台</p>
            <div>
                <span class="status-indicator status-online"></span>
                <span>已连接到雪球 | 用户: 4380321271</span>
            </div>
        </div>
        
        <div class="trading-grid">
            <!-- 左侧：持仓管理 -->
            <div class="panel">
                <h3>💼 我的持仓</h3>
                <button class="btn btn-primary" onclick="refreshHoldings()">刷新持仓</button>
                
                <div id="holdingsContainer">
                    <div class="loading">加载中...</div>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>📊 组合统计</h4>
                    <div id="portfolioStats">
                        <div>总市值: <span id="totalValue">--</span></div>
                        <div>今日盈亏: <span id="todayPnL">--</span></div>
                        <div>持仓数量: <span id="holdingCount">--</span></div>
                    </div>
                </div>
            </div>
            
            <!-- 中间：行情查看 -->
            <div class="panel">
                <h3>📊 实时行情</h3>
                
                <div class="form-group">
                    <label>搜索股票:</label>
                    <input type="text" id="searchInput" placeholder="输入股票名称或代码">
                    <button class="btn btn-primary" onclick="searchStocks()">搜索</button>
                </div>
                
                <div id="searchResults" class="search-results" style="display: none;"></div>
                
                <div class="form-group">
                    <label>当前股票:</label>
                    <input type="text" id="currentStock" value="SH600507" placeholder="股票代码">
                    <button class="btn btn-primary" onclick="loadQuote()">查看行情</button>
                </div>
                
                <div id="quoteDisplay">
                    <div class="loading">请输入股票代码</div>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>📈 五档买卖盘</h4>
                    <div id="pankouDisplay">
                        <div class="loading">请先选择股票</div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧：模拟交易 -->
            <div class="panel">
                <h3>🔄 模拟交易</h3>
                
                <div class="form-group">
                    <label>交易股票:</label>
                    <input type="text" id="tradeStock" placeholder="股票代码">
                </div>
                
                <div class="form-group">
                    <label>交易类型:</label>
                    <select id="tradeType">
                        <option value="buy">买入</option>
                        <option value="sell">卖出</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>交易数量:</label>
                    <input type="number" id="tradeAmount" placeholder="股数" min="100" step="100">
                </div>
                
                <div class="form-group">
                    <label>交易价格:</label>
                    <input type="number" id="tradePrice" placeholder="价格" step="0.01">
                    <button class="btn btn-primary" onclick="useCurrentPrice()">使用当前价</button>
                </div>
                
                <div class="form-group">
                    <button class="btn btn-success" onclick="submitTrade()">提交交易</button>
                    <button class="btn btn-danger" onclick="clearForm()">清空表单</button>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>📝 交易记录</h4>
                    <div id="tradeHistory">
                        <div class="loading">暂无交易记录</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="alertBox" class="alert"></div>
    </div>

    <script>
        let currentQuote = null;
        
        function showAlert(message, type = 'info') {
            const alertBox = document.getElementById('alertBox');
            alertBox.className = `alert ${type}`;
            alertBox.textContent = message;
            alertBox.style.display = 'block';
            
            setTimeout(() => {
                alertBox.style.display = 'none';
            }, 5000);
        }
        
        function refreshHoldings() {
            fetch('/api/holdings')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('holdingsContainer');
                    
                    if (data.success) {
                        let html = '<table class="holdings-table">';
                        html += '<tr><th>股票</th><th>代码</th><th>现价</th><th>涨跌</th><th>涨跌幅</th><th>市值</th></tr>';
                        
                        let totalValue = 0;
                        let totalPnL = 0;
                        
                        data.holdings.forEach(holding => {
                            const priceClass = holding.percent > 0 ? 'price-up' : 
                                             holding.percent < 0 ? 'price-down' : 'price-flat';
                            
                            html += `<tr onclick="selectStock('${holding.symbol}')">`;
                            html += `<td>${holding.name}</td>`;
                            html += `<td>${holding.symbol}</td>`;
                            html += `<td class="${priceClass}">¥${holding.current_price}</td>`;
                            html += `<td class="${priceClass}">${holding.chg}</td>`;
                            html += `<td class="${priceClass}">${holding.percent}%</td>`;
                            html += `<td>¥${(holding.market_capital / 100000000).toFixed(2)}亿</td>`;
                            html += '</tr>';
                            
                            totalValue += holding.market_capital;
                            totalPnL += holding.chg * 100; // 简化计算
                        });
                        
                        html += '</table>';
                        container.innerHTML = html;
                        
                        // 更新统计信息
                        document.getElementById('totalValue').textContent = `¥${(totalValue / 100000000).toFixed(2)}亿`;
                        document.getElementById('todayPnL').textContent = `¥${totalPnL.toFixed(2)}`;
                        document.getElementById('holdingCount').textContent = `${data.holdings.length}只`;
                        
                        showAlert('持仓刷新成功', 'success');
                    } else {
                        container.innerHTML = `<div class="alert error">${data.error}</div>`;
                        showAlert(`刷新失败: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    showAlert(`刷新异常: ${error}`, 'error');
                });
        }
        
        function searchStocks() {
            const query = document.getElementById('searchInput').value;
            if (!query) {
                showAlert('请输入搜索关键词', 'error');
                return;
            }
            
            fetch(`/api/search?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    const resultsDiv = document.getElementById('searchResults');
                    
                    if (data.success && data.stocks.length > 0) {
                        let html = '';
                        data.stocks.slice(0, 8).forEach(stock => {
                            html += `<div class="search-item" onclick="selectStock('${stock.code}')">`;
                            html += `<strong>${stock.query}</strong> (${stock.code})`;
                            html += '</div>';
                        });
                        resultsDiv.innerHTML = html;
                        resultsDiv.style.display = 'block';
                    } else {
                        resultsDiv.innerHTML = '<div class="search-item">未找到相关股票</div>';
                        resultsDiv.style.display = 'block';
                    }
                })
                .catch(error => {
                    showAlert(`搜索失败: ${error}`, 'error');
                });
        }
        
        function selectStock(symbol) {
            document.getElementById('currentStock').value = symbol;
            document.getElementById('tradeStock').value = symbol;
            document.getElementById('searchResults').style.display = 'none';
            loadQuote();
        }
        
        function loadQuote() {
            const symbol = document.getElementById('currentStock').value;
            if (!symbol) {
                showAlert('请输入股票代码', 'error');
                return;
            }
            
            // 获取实时行情
            fetch(`/api/quote/${symbol}`)
                .then(response => response.json())
                .then(data => {
                    const quoteDiv = document.getElementById('quoteDisplay');
                    
                    if (data.success) {
                        const quote = data.quote;
                        currentQuote = quote;
                        
                        const priceClass = quote.percent > 0 ? 'price-up' : 
                                         quote.percent < 0 ? 'price-down' : 'price-flat';
                        
                        quoteDiv.innerHTML = `
                            <h4>${quote.name || symbol}</h4>
                            <div style="font-size: 24px; margin: 10px 0;">
                                <span class="${priceClass}">¥${quote.current}</span>
                                <span class="${priceClass}" style="font-size: 16px;">
                                    ${quote.chg} (${quote.percent}%)
                                </span>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
                                <div>开盘: ¥${quote.open}</div>
                                <div>最高: ¥${quote.high}</div>
                                <div>最低: ¥${quote.low}</div>
                                <div>昨收: ¥${quote.last_close}</div>
                                <div>成交量: ${(quote.volume / 10000).toFixed(0)}万</div>
                                <div>成交额: ${(quote.amount / 100000000).toFixed(2)}亿</div>
                            </div>
                        `;
                        
                        // 自动填入交易价格
                        document.getElementById('tradePrice').value = quote.current;
                        
                        showAlert('行情加载成功', 'success');
                    } else {
                        quoteDiv.innerHTML = `<div class="alert error">${data.error}</div>`;
                        showAlert(`行情加载失败: ${data.error}`, 'error');
                    }
                });
            
            // 获取五档行情
            fetch(`/api/pankou/${symbol}`)
                .then(response => response.json())
                .then(data => {
                    const pankouDiv = document.getElementById('pankouDisplay');
                    
                    if (data.success && data.pankou) {
                        const p = data.pankou;
                        pankouDiv.innerHTML = `
                            <table class="pankou-table">
                                <tr><th>档位</th><th>卖价</th><th>卖量</th><th>买价</th><th>买量</th></tr>
                                <tr><td>五</td><td class="sell-price">${p.sp5 || '--'}</td><td>${p.sc5 || '--'}</td><td class="buy-price">${p.bp5 || '--'}</td><td>${p.bc5 || '--'}</td></tr>
                                <tr><td>四</td><td class="sell-price">${p.sp4 || '--'}</td><td>${p.sc4 || '--'}</td><td class="buy-price">${p.bp4 || '--'}</td><td>${p.bc4 || '--'}</td></tr>
                                <tr><td>三</td><td class="sell-price">${p.sp3 || '--'}</td><td>${p.sc3 || '--'}</td><td class="buy-price">${p.bp3 || '--'}</td><td>${p.bc3 || '--'}</td></tr>
                                <tr><td>二</td><td class="sell-price">${p.sp2 || '--'}</td><td>${p.sc2 || '--'}</td><td class="buy-price">${p.bp2 || '--'}</td><td>${p.bc2 || '--'}</td></tr>
                                <tr><td>一</td><td class="sell-price">${p.sp1 || '--'}</td><td>${p.sc1 || '--'}</td><td class="buy-price">${p.bp1 || '--'}</td><td>${p.bc1 || '--'}</td></tr>
                            </table>
                        `;
                    } else {
                        pankouDiv.innerHTML = '<div class="loading">五档行情暂不可用</div>';
                    }
                });
        }
        
        function useCurrentPrice() {
            if (currentQuote && currentQuote.current) {
                document.getElementById('tradePrice').value = currentQuote.current;
                showAlert('已使用当前价格', 'info');
            } else {
                showAlert('请先加载股票行情', 'error');
            }
        }
        
        function submitTrade() {
            const stock = document.getElementById('tradeStock').value;
            const type = document.getElementById('tradeType').value;
            const amount = document.getElementById('tradeAmount').value;
            const price = document.getElementById('tradePrice').value;
            
            if (!stock || !amount || !price) {
                showAlert('请填写完整的交易信息', 'error');
                return;
            }
            
            const tradeData = {
                symbol: stock,
                type: type,
                amount: parseInt(amount),
                price: parseFloat(price)
            };
            
            fetch('/api/trade', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(tradeData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`${type === 'buy' ? '买入' : '卖出'}成功`, 'success');
                    clearForm();
                    refreshHoldings();
                    loadTradeHistory();
                } else {
                    showAlert(`交易失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showAlert(`交易异常: ${error}`, 'error');
            });
        }
        
        function clearForm() {
            document.getElementById('tradeStock').value = '';
            document.getElementById('tradeAmount').value = '';
            document.getElementById('tradePrice').value = '';
        }
        
        function loadTradeHistory() {
            // 这里可以加载交易历史记录
            const historyDiv = document.getElementById('tradeHistory');
            historyDiv.innerHTML = '<div class="loading">交易记录功能开发中...</div>';
        }
        
        // 页面加载时初始化
        window.onload = function() {
            refreshHoldings();
            loadQuote(); // 加载默认股票（方大特钢）
        };
        
        // 定时刷新行情（每30秒）
        setInterval(() => {
            const currentStock = document.getElementById('currentStock').value;
            if (currentStock) {
                loadQuote();
            }
        }, 30000);
    </script>
</body>
</html>
    '''

# API路由
@app.route('/api/holdings')
def get_holdings():
    """获取持仓"""
    try:
        holdings = trader.get_portfolio_holdings()
        return jsonify({
            'success': True,
            'holdings': holdings
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/quote/<symbol>')
def get_quote(symbol):
    """获取股票行情"""
    try:
        quote = trader.get_stock_quote(symbol)
        if quote:
            return jsonify({
                'success': True,
                'quote': quote
            })
        else:
            return jsonify({
                'success': False,
                'error': '无法获取行情数据'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/pankou/<symbol>')
def get_pankou(symbol):
    """获取五档行情"""
    try:
        pankou = trader.get_stock_pankou(symbol)
        return jsonify({
            'success': True,
            'pankou': pankou
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/search')
def search_stocks():
    """搜索股票"""
    try:
        query = request.args.get('q', '')
        stocks = trader.search_stocks(query)
        return jsonify({
            'success': True,
            'stocks': stocks
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/trade', methods=['POST'])
def submit_trade():
    """提交模拟交易"""
    try:
        data = request.get_json()
        symbol = data.get('symbol')
        trade_type = data.get('type')
        amount = data.get('amount')
        price = data.get('price')
        
        # 这里实现模拟交易逻辑
        # 由于雪球的组合调仓API比较复杂，我们先记录交易意图
        trade_record = {
            'symbol': symbol,
            'type': trade_type,
            'amount': amount,
            'price': price,
            'timestamp': datetime.now().isoformat(),
            'total_value': amount * price
        }
        
        # 保存交易记录到文件
        try:
            with open('simulation_trades.json', 'r', encoding='utf-8') as f:
                trades = json.load(f)
        except FileNotFoundError:
            trades = []
        
        trades.append(trade_record)
        
        with open('simulation_trades.json', 'w', encoding='utf-8') as f:
            json.dump(trades, f, indent=2, ensure_ascii=False)
        
        logging.info(f"📝 模拟交易记录: {trade_type} {symbol} {amount}股 @{price}")
        
        return jsonify({
            'success': True,
            'message': f'模拟{trade_type}成功',
            'trade_record': trade_record
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def main():
    """主函数"""
    print("🚀 雪球模拟交易界面")
    print("基于真实雪球API的专业模拟交易系统")
    print("=" * 60)
    
    print("\n🌐 启动Web服务器...")
    print("访问地址: http://localhost:5006")
    print("功能特点:")
    print("  📊 真实雪球持仓数据")
    print("  📈 实时股票行情")
    print("  📋 五档买卖盘")
    print("  🔄 模拟交易功能")
    print("  💼 持仓管理")
    print("  🔍 股票搜索")
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5006, debug=True)

if __name__ == '__main__':
    main()
