# ATR RSI策略模拟交易完整指南

## 🎯 策略概述

**ATR RSI策略**是一个结合趋势判断和反转信号的量化交易策略：

### 📊 核心指标
- **ATR (平均真实波幅)**: 衡量市场波动性，判断趋势强度
- **RSI (相对强弱指数)**: 判断超买超卖，寻找反转信号

### 🔧 策略逻辑
1. **趋势判断**: ATR > ATR均线时为趋势市场
2. **入场信号**: 趋势市场中RSI极值反转
3. **止损方式**: 移动止损跟踪最高/最低价
4. **出场条件**: 止损触发或趋势结束

## 🚀 模拟交易操作步骤

### 步骤1: 启动VeighNa系统 ✅

您已经运行了 `python test_atr_rsi_simulation.py`，VeighNa图形界面应该已经打开。

### 步骤2: 配置模拟账户 💰

1. **打开模拟账户模块**:
   - 在VeighNa主界面，点击菜单 `功能` → `模拟账户`
   - 如果没有看到，点击 `系统` → `应用管理` 确保模拟账户已启用

2. **设置模拟账户参数**:
   ```
   初始资金: 1,000,000 (一百万)
   手续费率: 0.0001 (万分之一)
   滑点设置: 0.2
   ```

3. **启动模拟账户**:
   - 点击 `启动` 按钮
   - 确认模拟账户状态为 "运行中"

### 步骤3: 加载ATR RSI策略 📈

1. **打开CTA策略模块**:
   - 点击菜单 `功能` → `CTA策略`

2. **添加新策略**:
   - 点击 `新增策略` 按钮
   - 策略类名: `AtrRsiStrategy`
   - 策略实例名: `atr_rsi_test`
   - 本地代码: `IF2312.CFFEX` (沪深300期货主力合约)

3. **配置策略参数**:
   ```
   atr_length: 14        # ATR计算周期
   atr_ma_length: 30     # ATR均线周期
   rsi_length: 14        # RSI计算周期
   rsi_entry: 16         # RSI入场阈值
   trailing_percent: 0.8 # 移动止损百分比
   fixed_size: 1         # 固定交易手数
   ```

### 步骤4: 启动策略交易 🎯

1. **初始化策略**:
   - 选中您的策略实例
   - 点击 `初始化` 按钮
   - 等待状态变为 "已初始化"

2. **启动策略**:
   - 点击 `启动` 按钮
   - 策略状态变为 "交易中"

3. **监控策略运行**:
   - 观察策略日志输出
   - 查看持仓变化
   - 监控盈亏情况

### 步骤5: 监控和分析 📊

1. **实时监控**:
   - 持仓信息: 查看当前多空仓位
   - 委托记录: 查看下单情况
   - 成交记录: 查看成交明细
   - 策略日志: 查看策略运行日志

2. **性能分析**:
   - 总盈亏: 策略累计盈亏
   - 胜率: 盈利交易占比
   - 最大回撤: 最大亏损幅度
   - 夏普比率: 风险调整后收益

## 📋 策略参数详解

### 🔧 核心参数

| 参数 | 默认值 | 说明 | 调整建议 |
|------|--------|------|----------|
| `atr_length` | 14 | ATR计算周期 | 增大=更平滑，减小=更敏感 |
| `atr_ma_length` | 30 | ATR均线周期 | 用于判断趋势强度 |
| `rsi_length` | 14 | RSI计算周期 | 标准设置，一般不调整 |
| `rsi_entry` | 16 | RSI入场阈值 | 增大=更严格，减小=更频繁 |
| `trailing_percent` | 0.8 | 移动止损% | 增大=更宽松，减小=更严格 |
| `fixed_size` | 1 | 固定手数 | 根据资金量调整 |

### 🎯 参数优化建议

**保守型设置** (适合新手):
```
atr_length: 20
atr_ma_length: 40
rsi_entry: 20
trailing_percent: 1.0
```

**激进型设置** (适合有经验者):
```
atr_length: 10
atr_ma_length: 20
rsi_entry: 12
trailing_percent: 0.5
```

## ⚠️ 风险控制

### 🛡️ 内置风险控制
1. **移动止损**: 自动跟踪价格保护利润
2. **趋势过滤**: 只在趋势市场交易
3. **固定手数**: 控制单笔交易规模

### 📊 建议的额外风控
1. **最大持仓**: 不超过总资金的50%
2. **单日亏损**: 不超过总资金的2%
3. **连续亏损**: 连续3次亏损后暂停
4. **定期评估**: 每周评估策略表现

## 🔍 常见问题

### Q1: 策略不下单怎么办？
**A**: 检查以下几点：
- 确认模拟账户已启动
- 检查策略是否已初始化和启动
- 确认有实时行情数据
- 查看策略日志是否有错误

### Q2: 如何调整策略参数？
**A**: 
1. 先停止策略
2. 修改参数设置
3. 重新初始化和启动

### Q3: 模拟交易结果可信吗？
**A**: 
- 模拟交易能验证策略逻辑
- 但实盘会有滑点、延时等因素
- 建议先模拟测试，再小资金实盘

### Q4: 如何保存策略结果？
**A**:
- VeighNa会自动保存交易记录
- 可以导出CSV格式数据
- 建议定期备份策略配置

## 📈 策略优化方向

### 🔧 技术优化
1. **多时间框架**: 结合不同周期信号
2. **动态参数**: 根据市场状态调整参数
3. **资金管理**: 根据胜率调整仓位
4. **止盈策略**: 添加分批止盈逻辑

### 📊 市场适应性
1. **品种选择**: 测试不同交易品种
2. **时间段**: 分析不同时间段表现
3. **市场环境**: 牛市、熊市、震荡市适应性

## 🎉 总结

ATR RSI策略是一个经典的趋势跟踪策略，具有以下特点：

**优势**:
- ✅ 逻辑清晰，易于理解
- ✅ 有趋势过滤，避免震荡市场
- ✅ 移动止损保护利润
- ✅ 参数相对稳定

**注意事项**:
- ⚠️ 趋势市场表现更好
- ⚠️ 需要合理的参数设置
- ⚠️ 建议结合其他指标使用
- ⚠️ 定期评估和优化

**下一步建议**:
1. 先在模拟环境充分测试
2. 尝试不同的参数组合
3. 分析不同市场环境下的表现
4. 考虑加入更多风控措施

祝您模拟交易顺利！ 🚀
