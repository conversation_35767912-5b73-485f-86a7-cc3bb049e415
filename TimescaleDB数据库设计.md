# TimescaleDB数据库设计方案

## 🎯 TimescaleDB选择优势

### 为什么选择TimescaleDB？
1. **专为时序数据设计**: 股票数据本质上是时序数据
2. **自动分区**: 按时间自动分区，查询性能优异
3. **数据压缩**: 自动压缩历史数据，节省存储空间
4. **SQL兼容**: 完整的PostgreSQL功能支持
5. **聚合查询**: 内置时序聚合函数，适合技术指标计算
6. **扩展性**: 支持水平扩展和高可用

### 性能特点
- **插入性能**: 100万行/秒的插入速度
- **查询性能**: 时序查询比普通PostgreSQL快10-100倍
- **存储效率**: 数据压缩率可达90%
- **内存优化**: 智能内存管理，适合大数据量

## 📊 数据库表结构设计

### 1. 用户管理模块 (yonghu)

#### yonghu_xinxi (用户信息表)
```sql
CREATE TABLE yonghu_xinxi (
    id SERIAL PRIMARY KEY,
    yonghuming VARCHAR(50) UNIQUE NOT NULL,        -- 用户名
    youxiang VARCHAR(100) UNIQUE NOT NULL,         -- 邮箱
    mima_hash VARCHAR(255) NOT NULL,               -- 密码哈希
    dingyue_leixing VARCHAR(20) DEFAULT 'jiben',   -- 订阅类型: jiben/gaoji
    dingyue_kaishi DATE,                           -- 订阅开始时间
    dingyue_jieshu DATE,                           -- 订阅结束时间
    xukezheng_miyao VARCHAR(255) UNIQUE,           -- 许可证密钥
    shebei_id VARCHAR(255),                        -- 设备ID
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(), -- 创建时间
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW(),     -- 更新时间
    shi_huoyue BOOLEAN DEFAULT TRUE                -- 是否活跃
);

-- 创建索引
CREATE INDEX idx_yonghu_yonghuming ON yonghu_xinxi(yonghuming);
CREATE INDEX idx_yonghu_youxiang ON yonghu_xinxi(youxiang);
CREATE INDEX idx_yonghu_xukezheng ON yonghu_xinxi(xukezheng);
```

#### yonghu_shezhi (用户设置表)
```sql
CREATE TABLE yonghu_shezhi (
    id SERIAL PRIMARY KEY,
    yonghu_id INTEGER REFERENCES yonghu_xinxi(id) ON DELETE CASCADE,
    shezhi_jian VARCHAR(100) NOT NULL,             -- 设置键
    shezhi_zhi TEXT,                               -- 设置值
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_yonghu_shezhi_yonghu_id ON yonghu_shezhi(yonghu_id);
```

### 2. 股票数据模块 (gupiao)

#### gupiao_jiben (股票基本信息表)
```sql
CREATE TABLE gupiao_jiben (
    id SERIAL PRIMARY KEY,
    ts_daima VARCHAR(20) UNIQUE NOT NULL,          -- TuShare代码
    gupiao_daima VARCHAR(10) NOT NULL,             -- 股票代码
    gupiao_mingcheng VARCHAR(50) NOT NULL,         -- 股票名称
    diqu VARCHAR(20),                              -- 地域
    hangye VARCHAR(50),                            -- 行业
    shichang VARCHAR(20),                          -- 市场类型
    shangshi_riqi DATE,                            -- 上市日期
    shi_huoyue BOOLEAN DEFAULT TRUE,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_gupiao_ts_daima ON gupiao_jiben(ts_daima);
CREATE INDEX idx_gupiao_daima ON gupiao_jiben(gupiao_daima);
CREATE INDEX idx_gupiao_hangye ON gupiao_jiben(hangye);
```

#### gupiao_rixian (股票日线数据表) - 时序表
```sql
CREATE TABLE gupiao_rixian (
    shijian TIMESTAMPTZ NOT NULL,                 -- 时间戳
    ts_daima VARCHAR(20) NOT NULL,                -- TuShare代码
    jiaoyi_riqi DATE NOT NULL,                    -- 交易日期
    kaipan_jia DECIMAL(10,3),                     -- 开盘价
    zuigao_jia DECIMAL(10,3),                     -- 最高价
    zuidi_jia DECIMAL(10,3),                      -- 最低价
    shoupan_jia DECIMAL(10,3),                    -- 收盘价
    qian_shoupan DECIMAL(10,3),                   -- 前收盘价
    zhangdie_e DECIMAL(10,3),                     -- 涨跌额
    zhangdie_fu DECIMAL(8,4),                     -- 涨跌幅
    chengjiao_liang BIGINT,                       -- 成交量(手)
    chengjiao_e DECIMAL(20,2),                    -- 成交额(千元)
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- 创建时序表
SELECT create_hypertable('gupiao_rixian', 'shijian', 
    chunk_time_interval => INTERVAL '1 month');

-- 创建索引
CREATE INDEX idx_gupiao_rixian_ts_daima_shijian 
    ON gupiao_rixian(ts_daima, shijian DESC);
CREATE INDEX idx_gupiao_rixian_jiaoyi_riqi 
    ON gupiao_rixian(jiaoyi_riqi);

-- 创建唯一约束
CREATE UNIQUE INDEX idx_gupiao_rixian_unique 
    ON gupiao_rixian(ts_daima, jiaoyi_riqi);
```

#### jishu_zhibiao (技术指标表) - 时序表
```sql
CREATE TABLE jishu_zhibiao (
    shijian TIMESTAMPTZ NOT NULL,
    ts_daima VARCHAR(20) NOT NULL,
    jiaoyi_riqi DATE NOT NULL,
    zhibiao_mingcheng VARCHAR(50) NOT NULL,       -- 指标名称: MA5, MA10, RSI, MACD等
    zhibiao_zhi DECIMAL(15,6),                    -- 指标值
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- 创建时序表
SELECT create_hypertable('jishu_zhibiao', 'shijian',
    chunk_time_interval => INTERVAL '1 month');

-- 创建索引
CREATE INDEX idx_jishu_zhibiao_ts_daima_shijian 
    ON jishu_zhibiao(ts_daima, shijian DESC);
CREATE INDEX idx_jishu_zhibiao_mingcheng 
    ON jishu_zhibiao(zhibiao_mingcheng);

-- 创建唯一约束
CREATE UNIQUE INDEX idx_jishu_zhibiao_unique 
    ON jishu_zhibiao(ts_daima, jiaoyi_riqi, zhibiao_mingcheng);
```

### 3. 策略管理模块 (celue)

#### celue_xinxi (策略信息表)
```sql
CREATE TABLE celue_xinxi (
    id SERIAL PRIMARY KEY,
    yonghu_id INTEGER REFERENCES yonghu_xinxi(id) ON DELETE CASCADE,
    celue_mingcheng VARCHAR(100) NOT NULL,        -- 策略名称
    celue_miaoshu TEXT,                           -- 策略描述
    celue_leixing VARCHAR(50),                    -- 策略类型: xitong/zidingyi
    celue_daima TEXT,                             -- 加密存储的策略代码
    canshu_peizhi JSONB,                          -- 策略参数配置
    shi_huoyue BOOLEAN DEFAULT TRUE,
    shi_xitong BOOLEAN DEFAULT FALSE,             -- 是否为系统策略
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_celue_yonghu_id ON celue_xinxi(yonghu_id);
CREATE INDEX idx_celue_leixing ON celue_xinxi(celue_leixing);
CREATE INDEX idx_celue_canshu_peizhi ON celue_xinxi USING GIN(canshu_peizhi);
```

#### celue_moban (策略模板表)
```sql
CREATE TABLE celue_moban (
    id SERIAL PRIMARY KEY,
    moban_mingcheng VARCHAR(100) NOT NULL,        -- 模板名称
    moban_fenlei VARCHAR(50),                     -- 模板分类
    moban_miaoshu TEXT,                           -- 模板描述
    moban_daima TEXT,                             -- 加密的模板代码
    moren_canshu JSONB,                           -- 默认参数
    dingyue_dengji VARCHAR(20),                   -- 订阅等级: jiben/gaoji
    shi_huoyue BOOLEAN DEFAULT TRUE,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_celue_moban_fenlei ON celue_moban(moban_fenlei);
CREATE INDEX idx_celue_moban_dengji ON celue_moban(dingyue_dengji);
```

### 4. 回测模块 (huice)

#### huice_jilu (回测记录表)
```sql
CREATE TABLE huice_jilu (
    id SERIAL PRIMARY KEY,
    yonghu_id INTEGER REFERENCES yonghu_xinxi(id) ON DELETE CASCADE,
    celue_id INTEGER REFERENCES celue_xinxi(id) ON DELETE CASCADE,
    huice_mingcheng VARCHAR(100),                 -- 回测名称
    kaishi_riqi DATE NOT NULL,                    -- 开始日期
    jieshu_riqi DATE NOT NULL,                    -- 结束日期
    chushi_zijin DECIMAL(15,2),                   -- 初始资金
    zuizhong_zijin DECIMAL(15,2),                 -- 最终资金
    zongshou_yi DECIMAL(8,4),                     -- 总收益率
    nianhua_shouyi DECIMAL(8,4),                  -- 年化收益率
    zuida_huiche DECIMAL(8,4),                    -- 最大回撤
    xiapulv DECIMAL(8,4),                         -- 夏普比率
    shenglv DECIMAL(8,4),                         -- 胜率
    zongjiao_yi_cishu INTEGER,                    -- 总交易次数
    zhuangtai VARCHAR(20) DEFAULT 'dengdai',      -- 状态: dengdai/yunxing/wancheng/shibai
    jieguo_shuju JSONB,                           -- 详细回测结果
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    wancheng_shijian TIMESTAMPTZ
);

CREATE INDEX idx_huice_yonghu_id ON huice_jilu(yonghu_id);
CREATE INDEX idx_huice_celue_id ON huice_jilu(celue_id);
CREATE INDEX idx_huice_zhuangtai ON huice_jilu(zhuangtai);
```

#### huice_jiaoyi (回测交易记录表) - 时序表
```sql
CREATE TABLE huice_jiaoyi (
    shijian TIMESTAMPTZ NOT NULL,
    huice_id INTEGER REFERENCES huice_jilu(id) ON DELETE CASCADE,
    ts_daima VARCHAR(20) NOT NULL,
    jiaoyi_riqi DATE NOT NULL,
    caozuo VARCHAR(10) NOT NULL,                  -- 操作: mai/mai
    shuliang INTEGER NOT NULL,                    -- 数量
    jiage DECIMAL(10,3) NOT NULL,                 -- 价格
    jine DECIMAL(15,2) NOT NULL,                  -- 金额
    shouxufei DECIMAL(10,2),                      -- 手续费
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- 创建时序表
SELECT create_hypertable('huice_jiaoyi', 'shijian',
    chunk_time_interval => INTERVAL '1 month');

CREATE INDEX idx_huice_jiaoyi_huice_id ON huice_jiaoyi(huice_id);
CREATE INDEX idx_huice_jiaoyi_ts_daima ON huice_jiaoyi(ts_daima);
```

### 5. 模拟交易模块 (moni_jiaoyi)

#### xuni_zhanghu (虚拟账户表)
```sql
CREATE TABLE xuni_zhanghu (
    id SERIAL PRIMARY KEY,
    yonghu_id INTEGER REFERENCES yonghu_xinxi(id) ON DELETE CASCADE,
    zhanghu_mingcheng VARCHAR(100),               -- 账户名称
    chushi_zijin DECIMAL(15,2),                   -- 初始资金
    dangqian_zijin DECIMAL(15,2),                 -- 当前资金
    keyong_xianjin DECIMAL(15,2),                 -- 可用现金
    shizhi DECIMAL(15,2),                         -- 市值
    zongzi_chan DECIMAL(15,2),                    -- 总资产
    zongshou_yi DECIMAL(8,4),                     -- 总收益率
    shi_huoyue BOOLEAN DEFAULT TRUE,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_xuni_zhanghu_yonghu_id ON xuni_zhanghu(yonghu_id);
```

#### xuni_chicang (虚拟持仓表)
```sql
CREATE TABLE xuni_chicang (
    id SERIAL PRIMARY KEY,
    zhanghu_id INTEGER REFERENCES xuni_zhanghu(id) ON DELETE CASCADE,
    ts_daima VARCHAR(20) NOT NULL,
    shuliang INTEGER NOT NULL,                    -- 持仓数量
    pingjun_chengben DECIMAL(10,3) NOT NULL,      -- 平均成本
    dangqian_jiage DECIMAL(10,3),                 -- 当前价格
    shizhi DECIMAL(15,2),                         -- 市值
    fudong_yingkui DECIMAL(15,2),                 -- 浮动盈亏
    yishixian_yingkui DECIMAL(15,2),              -- 已实现盈亏
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_xuni_chicang_zhanghu_id ON xuni_chicang(zhanghu_id);
CREATE INDEX idx_xuni_chicang_ts_daima ON xuni_chicang(ts_daima);
CREATE UNIQUE INDEX idx_xuni_chicang_unique ON xuni_chicang(zhanghu_id, ts_daima);
```

## 🔧 TimescaleDB优化配置

### 数据压缩策略
```sql
-- 启用自动压缩 (7天后压缩数据)
ALTER TABLE gupiao_rixian SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'ts_daima',
    timescaledb.compress_orderby = 'shijian DESC'
);

SELECT add_compression_policy('gupiao_rixian', INTERVAL '7 days');

-- 技术指标表压缩
ALTER TABLE jishu_zhibiao SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'ts_daima,zhibiao_mingcheng',
    timescaledb.compress_orderby = 'shijian DESC'
);

SELECT add_compression_policy('jishu_zhibiao', INTERVAL '7 days');
```

### 数据保留策略
```sql
-- 保留2年的日线数据
SELECT add_retention_policy('gupiao_rixian', INTERVAL '2 years');

-- 保留1年的技术指标数据
SELECT add_retention_policy('jishu_zhibiao', INTERVAL '1 year');

-- 保留6个月的回测交易记录
SELECT add_retention_policy('huice_jiaoyi', INTERVAL '6 months');
```

### 连续聚合视图
```sql
-- 创建月度股票统计视图
CREATE MATERIALIZED VIEW gupiao_yuedu_tongji
WITH (timescaledb.continuous) AS
SELECT 
    ts_daima,
    time_bucket('1 month', shijian) AS yue,
    first(kaipan_jia, shijian) AS yue_kaipan,
    max(zuigao_jia) AS yue_zuigao,
    min(zuidi_jia) AS yue_zuidi,
    last(shoupan_jia, shijian) AS yue_shoupan,
    sum(chengjiao_liang) AS yue_chengjiao_liang,
    sum(chengjiao_e) AS yue_chengjiao_e
FROM gupiao_rixian
GROUP BY ts_daima, yue;

-- 添加刷新策略
SELECT add_continuous_aggregate_policy('gupiao_yuedu_tongji',
    start_offset => INTERVAL '1 month',
    end_offset => INTERVAL '1 day',
    schedule_interval => INTERVAL '1 day');
```

## 📊 性能优化建议

### 查询优化
1. **时间范围查询**: 始终包含时间条件
2. **符号过滤**: 优先使用ts_daima过滤
3. **批量插入**: 使用COPY或批量INSERT
4. **连接池**: 使用asyncpg连接池

### 监控指标
1. **查询性能**: 监控慢查询
2. **存储使用**: 监控磁盘使用率
3. **压缩效果**: 监控压缩比率
4. **内存使用**: 监控缓存命中率

这个TimescaleDB设计方案专门针对股票量化交易数据进行了优化，能够高效处理大量时序数据并提供快速的查询性能。
