/* 专业股票交易客户端样式 - 参考同花顺设计 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    overflow-x: hidden;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.trading-header {
    background: linear-gradient(90deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    border-bottom: 2px solid #00d4ff;
}

.header-left h1 {
    font-size: 24px;
    margin-bottom: 5px;
    background: linear-gradient(45deg, #00d4ff, #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.mode-switch {
    display: flex;
    gap: 15px;
}

.mode-switch label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background 0.3s;
}

.mode-switch label:hover {
    background: rgba(255,255,255,0.1);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    border: 1px solid rgba(255,255,255,0.2);
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ff4757;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #2ed573;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 主要内容区域 */
.trading-main {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    gap: 15px;
    padding: 15px;
    min-height: calc(100vh - 80px);
}

/* 面板通用样式 */
.panel {
    background: rgba(255,255,255,0.95);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.panel h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
    font-size: 16px;
}

/* 左侧面板 */
.left-panel {
    display: flex;
    flex-direction: column;
}

.search-box {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.search-box input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.search-results {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 5px;
}

.search-item {
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background 0.3s;
}

.search-item:hover {
    background: #f8f9fa;
}

.account-info, .position-info {
    font-size: 14px;
    line-height: 1.6;
}

.account-item, .position-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

/* 中间面板 */
.center-panel {
    display: flex;
    flex-direction: column;
}

.stock-selector {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.stock-selector input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.quote-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.quote-item {
    text-align: center;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quote-label {
    font-size: 12px;
    opacity: 0.8;
    margin-bottom: 5px;
}

.quote-value {
    font-size: 18px;
    font-weight: bold;
}

.quote-value.up {
    color: #ff4757;
}

.quote-value.down {
    color: #2ed573;
}

/* 五档行情 */
.pankou-display {
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.pankou-table {
    width: 100%;
    border-collapse: collapse;
}

.pankou-table th,
.pankou-table td {
    padding: 8px;
    text-align: center;
    border: 1px solid #eee;
}

.pankou-table th {
    background: #f8f9fa;
    font-weight: bold;
}

.sell-price {
    color: #2ed573;
}

.buy-price {
    color: #ff4757;
}

/* 右侧面板 */
.right-panel {
    display: flex;
    flex-direction: column;
}

.trade-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    font-weight: bold;
    color: #2c3e50;
}

.form-group input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.trade-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s;
    text-transform: uppercase;
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.btn-buy {
    background: linear-gradient(45deg, #ff4757, #ff3742);
    color: white;
    flex: 1;
}

.btn-sell {
    background: linear-gradient(45deg, #2ed573, #20bf6b);
    color: white;
    flex: 1;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* 交易记录和日志 */
.trade-history, .system-log {
    max-height: 200px;
    overflow-y: auto;
    font-size: 12px;
    line-height: 1.4;
}

.trade-entry, .log-entry {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 5px;
}

.trade-entry.buy {
    border-left: 3px solid #ff4757;
}

.trade-entry.sell {
    border-left: 3px solid #2ed573;
}

.log-entry {
    font-family: 'Courier New', monospace;
    color: #666;
}

/* 加载和状态 */
.loading {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.no-data {
    text-align: center;
    padding: 20px;
    color: #999;
}

/* 图表区域 */
.chart-display {
    min-height: 300px;
    background: #f8f9fa;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-placeholder {
    color: #666;
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .trading-main {
        grid-template-columns: 250px 1fr 250px;
    }
}

@media (max-width: 768px) {
    .trading-main {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .trading-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .header-left, .header-right {
        width: 100%;
        justify-content: center;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
