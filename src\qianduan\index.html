<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>A股量化交易平台</title>
    <meta name="description" content="专业的A股量化交易平台，提供策略开发、回测分析、模拟交易等功能" />
    <meta name="keywords" content="量化交易,A股,股票,策略,回测,模拟交易" />
    <meta name="author" content="量化交易平台开发团队" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 全局样式 -->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      html, body {
        height: 100%;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      #app {
        height: 100%;
      }
      
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #1a1a1a;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-logo {
        font-size: 2rem;
        color: #409eff;
        margin-bottom: 1rem;
        animation: pulse 2s infinite;
      }
      
      .loading-text {
        color: #909399;
        font-size: 0.9rem;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
      
      /* 隐藏加载动画 */
      .loading-container.hidden {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 初始加载动画 -->
      <div id="loading" class="loading-container">
        <div class="loading-logo">📈</div>
        <div class="loading-spinner"></div>
        <div class="loading-text">A股量化交易平台加载中...</div>
      </div>
    </div>
    
    <script>
      // 隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('hidden');
            setTimeout(() => loading.remove(), 300);
          }
        }, 500);
      });
    </script>
    
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
