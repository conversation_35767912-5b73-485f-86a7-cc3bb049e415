<template>
  <div class="admin-login-container">
    <div class="admin-login-card">
      <div class="admin-header">
        <div class="admin-logo">
          <el-icon class="logo-icon"><Setting /></el-icon>
          <h1 class="logo-text">系统管理后台</h1>
        </div>
        <p class="subtitle">A股量化交易平台 - 管理员登录</p>
      </div>
      
      <el-form
        ref="adminLoginFormRef"
        :model="adminLoginForm"
        :rules="adminLoginRules"
        class="admin-login-form"
        size="large"
      >
        <el-form-item prop="guanli_yonghuming">
          <el-input
            v-model="adminLoginForm.guanli_yonghuming"
            placeholder="管理员用户名"
            prefix-icon="UserFilled"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="guanli_mima">
          <el-input
            v-model="adminLoginForm.guanli_mima"
            type="password"
            placeholder="管理员密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleAdminLogin"
          />
        </el-form-item>
        
        <el-form-item prop="yanzheng_ma">
          <div class="captcha-container">
            <el-input
              v-model="adminLoginForm.yanzheng_ma"
              placeholder="验证码"
              prefix-icon="Key"
              clearable
              class="captcha-input"
            />
            <div class="captcha-image" @click="refreshCaptcha">
              <span class="captcha-text">{{ captchaText }}</span>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="danger"
            class="admin-login-button"
            :loading="loading"
            @click="handleAdminLogin"
          >
            <el-icon><Lock /></el-icon>
            {{ loading ? '验证中...' : '管理员登录' }}
          </el-button>
        </el-form-item>
        
        <el-form-item>
          <div class="back-link">
            <el-link type="primary" @click="$router.push('/login')">
              <el-icon><ArrowLeft /></el-icon>
              返回用户登录
            </el-link>
          </div>
        </el-form-item>
      </el-form>
      
      <div class="security-notice">
        <el-alert
          title="安全提示"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            管理员登录需要额外的安全验证，请确保您有相应的管理权限。
          </template>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useAdminStore } from '@/shangdian/admin'

const router = useRouter()
const adminStore = useAdminStore()
const adminLoginFormRef = ref<FormInstance>()
const loading = ref(false)
const captchaText = ref('')

// 管理员登录表单
interface AdminLoginForm {
  guanli_yonghuming: string
  guanli_mima: string
  yanzheng_ma: string
}

const adminLoginForm = reactive<AdminLoginForm>({
  guanli_yonghuming: '',
  guanli_mima: '',
  yanzheng_ma: ''
})

// 表单验证规则
const adminLoginRules: FormRules = {
  guanli_yonghuming: [
    { required: true, message: '请输入管理员用户名', trigger: 'blur' },
    { min: 5, max: 20, message: '管理员用户名长度在 5 到 20 个字符', trigger: 'blur' }
  ],
  guanli_mima: [
    { required: true, message: '请输入管理员密码', trigger: 'blur' },
    { min: 8, max: 30, message: '管理员密码长度在 8 到 30 个字符', trigger: 'blur' }
  ],
  yanzheng_ma: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码为4位字符', trigger: 'blur' }
  ]
}

// 管理员账号配置
const ADMIN_CREDENTIALS = {
  guanli_yonghuming: 'superadmin',
  guanli_mima: 'SuperAdmin@2024',
  role: 'super_admin'
}

// 生成验证码
const generateCaptcha = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 刷新验证码
const refreshCaptcha = () => {
  captchaText.value = generateCaptcha()
}

// 验证管理员账号
const validateAdminCredentials = (yonghuming: string, mima: string, yanzheng: string) => {
  // 验证验证码
  if (yanzheng.toUpperCase() !== captchaText.value.toUpperCase()) {
    return { success: false, message: '验证码错误' }
  }
  
  // 验证管理员账号
  if (yonghuming === ADMIN_CREDENTIALS.guanli_yonghuming && mima === ADMIN_CREDENTIALS.guanli_mima) {
    return { success: true, admin: ADMIN_CREDENTIALS }
  }
  
  return { success: false, message: '管理员用户名或密码错误' }
}

// 管理员登录处理
const handleAdminLogin = async () => {
  if (!adminLoginFormRef.value) return
  
  try {
    const valid = await adminLoginFormRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    // 模拟网络延迟和安全检查
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 验证管理员账号
    const result = validateAdminCredentials(
      adminLoginForm.guanli_yonghuming,
      adminLoginForm.guanli_mima,
      adminLoginForm.yanzheng_ma
    )
    
    if (!result.success) {
      ElMessage.error(result.message)
      refreshCaptcha() // 验证失败后刷新验证码
      adminLoginForm.yanzheng_ma = ''
      return
    }
    
    // 设置管理员登录状态
    const adminToken = 'admin-jwt-token-' + Date.now()
    const adminInfo = {
      id: 1,
      guanli_yonghuming: result.admin!.guanli_yonghuming,
      role: result.admin!.role,
      youxiang: '<EMAIL>',
      chuangjian_shijian: new Date().toISOString(),
      zuihou_denglu: new Date().toISOString(),
      shi_huoyue: true
    }
    
    // 更新管理员状态
    adminStore.setAdminToken(adminToken)
    adminStore.setAdminInfo(adminInfo)
    adminStore.setAdminPermissions([
      'user_management',
      'system_config',
      'data_management',
      'security_audit',
      'order_management',
      'strategy_management',
      'finance_management'
    ])
    
    ElMessage.success('管理员登录成功')
    
    // 跳转到管理员仪表板
    await router.push('/numendavid中国/dashboard')
  } catch (error) {
    console.error('管理员登录失败:', error)
    ElMessage.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 组件挂载时生成验证码
onMounted(() => {
  refreshCaptcha()
})
</script>

<style lang="scss" scoped>
.admin-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.admin-login-card {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  padding: 40px;
  border: 2px solid #ff6b6b;
}

.admin-header {
  text-align: center;
  margin-bottom: 32px;
  
  .admin-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;
    
    .logo-icon {
      font-size: 32px;
      color: #ff6b6b;
    }
    
    .logo-text {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
    }
  }
  
  .subtitle {
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin: 0;
  }
}

.admin-login-form {
  .captcha-container {
    display: flex;
    gap: 12px;
    
    .captcha-input {
      flex: 1;
    }
    
    .captcha-image {
      width: 100px;
      height: 40px;
      background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      user-select: none;
      
      &:hover {
        background: linear-gradient(45deg, #e8e8e8, #d8d8d8);
      }
      
      .captcha-text {
        font-family: 'Courier New', monospace;
        font-size: 16px;
        font-weight: bold;
        color: #333;
        letter-spacing: 2px;
      }
    }
  }
  
  .admin-login-button {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border: none;
    
    &:hover {
      background: linear-gradient(135deg, #ff5252, #e53e3e);
    }
  }
  
  .back-link {
    text-align: center;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
}

.security-notice {
  margin-top: 24px;
}

// 响应式设计
@media (max-width: 480px) {
  .admin-login-card {
    padding: 24px;
  }
  
  .admin-header .admin-logo .logo-text {
    font-size: 20px;
  }
  
  .captcha-container {
    flex-direction: column;
    
    .captcha-image {
      width: 100%;
      height: 44px;
    }
  }
}
</style>
