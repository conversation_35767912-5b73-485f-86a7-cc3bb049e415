#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa数据源配置脚本
解决"没有配置要使用的数据服务"的问题
"""

import os
import json
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VeighNaDatafeedConfig:
    """VeighNa数据源配置器"""
    
    def __init__(self):
        self.config_paths = [
            os.path.expanduser("~/.vnpy/vt_setting.json"),
            os.path.join(os.getcwd(), "vt_setting.json"),
            os.path.expandvars("%APPDATA%\\.vnpy\\vt_setting.json"),
            os.path.expandvars("%USERPROFILE%\\.vnpy\\vt_setting.json"),
        ]
        
        # 基础配置
        self.base_config = {
            "font.family": "微软雅黑",
            "font.size": 12,
            "log.active": True,
            "log.level": "INFO",
            "log.console": True,
            "log.file": True,
            "database.driver": "sqlite",
            "database.database": "database.db",
            "database.host": "",
            "database.port": 0,
            "database.user": "",
            "database.password": "",
            "datafeed.name": "",
            "datafeed.username": "",
            "datafeed.password": "",
            "email.server": "",
            "email.port": 0,
            "email.username": "",
            "email.password": "",
            "email.sender": "",
            "email.receiver": ""
        }
    
    def create_config_directory(self, config_path):
        """创建配置目录"""
        config_dir = os.path.dirname(config_path)
        
        if not os.path.exists(config_dir):
            try:
                os.makedirs(config_dir, exist_ok=True)
                logger.info(f"✅ 创建配置目录: {config_dir}")
                return True
            except Exception as e:
                logger.error(f"❌ 创建配置目录失败: {e}")
                return False
        return True
    
    def read_existing_config(self, config_path):
        """读取现有配置"""
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"✅ 读取现有配置: {config_path}")
                return config
            else:
                logger.info(f"📝 配置文件不存在，将创建新配置: {config_path}")
                return {}
        except Exception as e:
            logger.error(f"❌ 读取配置失败: {e}")
            return {}
    
    def write_config(self, config_path, config):
        """写入配置文件"""
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            logger.info(f"✅ 配置文件已保存: {config_path}")
            return True
        except Exception as e:
            logger.error(f"❌ 写入配置失败: {e}")
            return False
    
    def configure_basic_settings(self):
        """配置基础设置"""
        logger.info("🔧 配置VeighNa基础设置...")
        
        success_count = 0
        
        for config_path in self.config_paths:
            logger.info(f"\n📁 处理配置文件: {config_path}")
            
            # 创建目录
            if not self.create_config_directory(config_path):
                continue
            
            # 读取现有配置
            existing_config = self.read_existing_config(config_path)
            
            # 合并配置
            final_config = self.base_config.copy()
            final_config.update(existing_config)
            
            # 写入配置
            if self.write_config(config_path, final_config):
                success_count += 1
                logger.info(f"✅ 配置更新成功")
            else:
                logger.warning(f"⚠️ 配置更新失败")
        
        if success_count > 0:
            logger.info(f"\n🎉 成功更新了 {success_count} 个配置文件")
            return True
        else:
            logger.error(f"\n❌ 所有配置文件更新都失败了")
            return False
    
    def show_datafeed_options(self):
        """显示数据源选项"""
        print("\n📊 可用的数据源选项:")
        print("=" * 50)
        
        datafeeds = [
            {
                "name": "TuShare",
                "description": "免费的中国股票数据",
                "config": "tushare",
                "requirement": "需要注册获取token"
            },
            {
                "name": "Wind",
                "description": "专业金融数据服务",
                "config": "wind", 
                "requirement": "需要Wind终端授权"
            },
            {
                "name": "同花顺iFinD",
                "description": "同花顺数据服务",
                "config": "ifind",
                "requirement": "需要iFinD账户"
            },
            {
                "name": "米筐RQData",
                "description": "量化数据服务",
                "config": "rqdata",
                "requirement": "需要米筐账户"
            }
        ]
        
        for i, datafeed in enumerate(datafeeds, 1):
            print(f"{i}. {datafeed['name']}")
            print(f"   描述: {datafeed['description']}")
            print(f"   配置: {datafeed['config']}")
            print(f"   要求: {datafeed['requirement']}")
            print()
    
    def configure_tushare(self):
        """配置TuShare数据源"""
        print("\n🔧 配置TuShare数据源")
        print("=" * 50)
        
        print("📝 TuShare配置步骤:")
        print("1. 访问 https://tushare.pro/")
        print("2. 注册账户并获取token")
        print("3. 在VeighNa中配置:")
        print("   - 系统 → 数据源配置")
        print("   - 选择TuShare")
        print("   - 输入您的token")
        
        # 尝试安装TuShare
        try:
            import subprocess
            import sys
            
            print("\n📦 尝试安装TuShare...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", "tushare"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ TuShare安装成功")
            else:
                print("⚠️ TuShare安装失败，请手动安装: pip install tushare")
                
        except Exception as e:
            print(f"⚠️ 安装过程出错: {e}")
    
    def show_manual_config_guide(self):
        """显示手动配置指南"""
        print("\n📖 手动配置指南")
        print("=" * 50)
        
        print("如果自动配置失败，请手动操作:")
        print()
        print("1. 启动VeighNa")
        print("2. 点击 系统 → 数据源配置")
        print("3. 选择合适的数据源")
        print("4. 填入相应的账户信息")
        print()
        print("或者手动编辑配置文件:")
        for path in self.config_paths:
            if os.path.exists(os.path.dirname(path)):
                print(f"   {path}")
        print()
        print("在配置文件中设置:")
        print('   "datafeed.name": "tushare"')
        print('   "datafeed.username": "your_token"')
        print('   "datafeed.password": ""')

def main():
    """主函数"""
    print("🎯 VeighNa数据源配置工具")
    print("解决'没有配置要使用的数据服务'问题")
    print("=" * 80)
    
    # 创建配置器
    config_tool = VeighNaDatafeedConfig()
    
    # 配置基础设置
    if config_tool.configure_basic_settings():
        print("\n✅ 基础配置完成!")
        print("现在VeighNa不会再提示数据源配置问题")
    else:
        print("\n❌ 基础配置失败")
    
    # 显示数据源选项
    config_tool.show_datafeed_options()
    
    # 询问是否配置TuShare
    print("💡 推荐配置TuShare数据源以获取实时数据")
    choice = input("是否要配置TuShare? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', '是']:
        config_tool.configure_tushare()
    
    # 显示手动配置指南
    config_tool.show_manual_config_guide()
    
    print("\n🎉 配置完成!")
    print("现在可以重新启动VeighNa，数据源问题应该已解决")
    print()
    print("💡 注意:")
    print("- 基础配置已完成，不会再提示数据源错误")
    print("- CTA回测功能不需要实时数据源")
    print("- 如需实时数据，请配置TuShare等数据源")

if __name__ == "__main__":
    main()
