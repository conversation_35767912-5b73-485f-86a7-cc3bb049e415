#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查找雪球组合管理相关的API
重点关注"管理组合"和"添加组合"功能
"""

import json
import logging
import requests
import pysnowball as ball
from datetime import datetime
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PortfolioManagementFinder:
    """组合管理API查找器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'Referer': 'https://xueqiu.com/performance',
            'Accept': 'application/json, text/plain, */*',
            'X-Requested-With': 'XMLHttpRequest',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ Token设置成功: u={u}")
                self.user_id = u
            
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
    
    def get_performance_page_with_js(self):
        """重新获取performance页面，关注JavaScript加载"""
        logger.info("🌐 重新获取performance页面")
        logger.info("=" * 60)
        
        try:
            # 先访问主页确保session有效
            self.session.get("https://xueqiu.com/", timeout=10)
            
            # 再访问performance页面
            url = "https://xueqiu.com/performance"
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                logger.info(f"✅ 页面获取成功: {len(response.text)} 字符")
                
                # 查找Vue组件和API调用
                self.analyze_page_for_apis(response.text)
                
                return response.text
            else:
                logger.error(f"❌ 页面获取失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取页面异常: {e}")
            return None
    
    def analyze_page_for_apis(self, page_content):
        """分析页面中的API调用"""
        logger.info("🔍 分析页面中的API调用")
        
        # 查找可能的API端点
        api_patterns = [
            r'["\']([^"\']*api[^"\']*)["\']',
            r'["\']([^"\']*performance[^"\']*\.json)["\']',
            r'["\']([^"\']*portfolio[^"\']*\.json)["\']',
            r'["\']([^"\']*moni[^"\']*\.json)["\']',
            r'url:\s*["\']([^"\']+)["\']',
            r'ajax\([^)]*url:\s*["\']([^"\']+)["\']',
        ]
        
        found_apis = set()
        
        for pattern in api_patterns:
            matches = re.findall(pattern, page_content, re.IGNORECASE)
            for match in matches:
                if any(keyword in match.lower() for keyword in ['api', 'json', 'performance', 'portfolio', 'moni']):
                    found_apis.add(match)
        
        if found_apis:
            logger.info(f"   🎯 找到可能的API端点:")
            for api in sorted(found_apis):
                logger.info(f"     • {api}")
        else:
            logger.info("   ⚠️ 未找到明显的API端点")
        
        return list(found_apis)
    
    def try_portfolio_management_apis(self):
        """尝试组合管理相关API"""
        logger.info("📊 尝试组合管理API")
        logger.info("=" * 60)
        
        # 组合管理相关的API端点
        management_apis = [
            # 获取组合列表
            "https://xueqiu.com/performance/portfolio/list.json",
            "https://xueqiu.com/performance/moni/list.json",
            "https://xueqiu.com/v4/performance/portfolio/list.json",
            "https://xueqiu.com/v4/performance/moni/list.json",
            "https://xueqiu.com/moni/portfolio/list.json",
            
            # 获取用户的组合
            f"https://xueqiu.com/performance/portfolio/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/performance/moni/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/v4/performance/portfolio/list.json?user_id={self.user_id}",
            
            # 组合详情
            "https://xueqiu.com/performance/portfolio/detail.json",
            "https://xueqiu.com/performance/moni/detail.json",
            "https://xueqiu.com/v4/performance/portfolio/detail.json",
            
            # 持仓信息
            "https://xueqiu.com/performance/portfolio/positions.json",
            "https://xueqiu.com/performance/moni/positions.json",
            "https://xueqiu.com/v4/performance/portfolio/positions.json",
            
            # 交易记录
            "https://xueqiu.com/performance/portfolio/trades.json",
            "https://xueqiu.com/performance/moni/trades.json",
            "https://xueqiu.com/v4/performance/portfolio/trades.json",
        ]
        
        successful_apis = []
        
        for api_url in management_apis:
            try:
                logger.info(f"\n🔗 测试: {api_url}")
                response = self.session.get(api_url, timeout=10)
                
                logger.info(f"   状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   ✅ 成功获取JSON数据")
                        
                        # 检查数据内容
                        data_str = json.dumps(data, ensure_ascii=False)
                        
                        # 查找关键信息
                        keywords = ['portfolio', 'position', 'holding', 'trade', '组合', '持仓', '交易', '测试', 'test']
                        found_keywords = [kw for kw in keywords if kw in data_str.lower()]
                        
                        if found_keywords:
                            logger.info(f"   🎯 包含关键词: {found_keywords}")
                            successful_apis.append({
                                'url': api_url,
                                'data': data,
                                'keywords': found_keywords
                            })
                        
                        # 显示数据结构
                        if isinstance(data, dict):
                            keys = list(data.keys())
                            logger.info(f"   数据字段: {keys}")
                            
                            # 如果有列表数据，显示数量
                            for key in ['list', 'data', 'portfolios', 'positions', 'trades']:
                                if key in data and isinstance(data[key], list):
                                    logger.info(f"   {key}: {len(data[key])} 个项目")
                        
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON响应")
                        
                elif response.status_code == 404:
                    logger.info(f"   ❌ API不存在")
                elif response.status_code in [401, 403]:
                    logger.info(f"   🔒 需要认证")
                else:
                    logger.info(f"   ❌ 失败")
                    
            except Exception as e:
                logger.info(f"   ❌ 异常: {e}")
        
        return successful_apis
    
    def try_create_portfolio_apis(self):
        """尝试创建组合的API"""
        logger.info("➕ 尝试创建组合API")
        logger.info("=" * 60)
        
        # 创建组合的API端点
        create_apis = [
            "https://xueqiu.com/performance/portfolio/create.json",
            "https://xueqiu.com/performance/moni/create.json",
            "https://xueqiu.com/v4/performance/portfolio/create.json",
            "https://xueqiu.com/v4/performance/moni/create.json",
            "https://xueqiu.com/moni/portfolio/create.json",
        ]
        
        # 创建数据
        create_data_variants = [
            {"name": "测试", "initial_cash": 1000000, "market": "cn"},
            {"name": "test", "initial_cash": 1000000, "market": "cn"},
            {"portfolio_name": "测试", "cash": 1000000},
            {"portfolio_name": "test", "cash": 1000000},
            {"title": "测试", "amount": 1000000},
            {"title": "test", "amount": 1000000},
        ]
        
        create_results = []
        
        for api_url in create_apis:
            for data in create_data_variants:
                try:
                    logger.info(f"\n🔗 测试创建: {api_url}")
                    logger.info(f"   数据: {data}")
                    
                    # 先尝试GET看看API是否存在
                    get_response = self.session.get(api_url, timeout=5)
                    if get_response.status_code == 404:
                        logger.info(f"   ❌ API不存在")
                        continue
                    
                    # 尝试POST创建
                    response = self.session.post(api_url, json=data, timeout=10)
                    logger.info(f"   状态: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            logger.info(f"   ✅ 创建响应: {result}")
                            create_results.append({
                                'api': api_url,
                                'data': data,
                                'result': result,
                                'success': True
                            })
                        except json.JSONDecodeError:
                            logger.info(f"   ⚠️ 非JSON响应: {response.text[:200]}")
                    else:
                        logger.info(f"   ❌ 创建失败: {response.status_code}")
                        if response.text:
                            logger.info(f"   错误信息: {response.text[:200]}")
                        
                except Exception as e:
                    logger.info(f"   ❌ 异常: {e}")
        
        return create_results
    
    def check_existing_portfolios_detailed(self):
        """详细检查现有组合"""
        logger.info("🔍 详细检查现有组合")
        logger.info("=" * 60)
        
        try:
            # 获取自选股列表
            watch_list = ball.watch_list()
            if not watch_list or 'data' not in watch_list:
                logger.warning("⚠️ 无法获取自选股列表")
                return {}
            
            results = {}
            
            # 检查每个分类
            for category, items in watch_list['data'].items():
                logger.info(f"\n📂 检查分类: {category}")
                
                if isinstance(items, list):
                    for item in items:
                        item_id = item.get('id')
                        item_name = item.get('name', '')
                        
                        logger.info(f"   📋 {item_name} (ID: {item_id})")
                        
                        try:
                            # 获取该分类的详细信息
                            stocks = ball.watch_stock(item_id)
                            if stocks and 'data' in stocks:
                                stock_data = stocks['data']
                                stock_list = stock_data.get('stocks', [])
                                
                                logger.info(f"     股票数量: {len(stock_list)}")
                                
                                if stock_list:
                                    logger.info(f"     股票列表:")
                                    for stock in stock_list[:5]:  # 只显示前5个
                                        name = stock.get('name', 'N/A')
                                        symbol = stock.get('symbol', 'N/A')
                                        logger.info(f"       • {name} ({symbol})")
                                    
                                    if len(stock_list) > 5:
                                        logger.info(f"       ... 还有 {len(stock_list) - 5} 只股票")
                                
                                results[f"{category}_{item_name}"] = {
                                    'id': item_id,
                                    'name': item_name,
                                    'category': category,
                                    'stocks': stock_list,
                                    'stock_count': len(stock_list)
                                }
                                
                        except Exception as e:
                            logger.info(f"     ❌ 获取详情失败: {e}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 检查现有组合失败: {e}")
            return {}

def main():
    """主函数"""
    print("🎯 查找雪球组合管理API")
    print("重点关注'管理组合'和'添加组合'功能")
    print("=" * 80)
    
    finder = PortfolioManagementFinder()
    
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'user_id': getattr(finder, 'user_id', None),
        'page_apis': [],
        'management_apis': [],
        'create_apis': [],
        'existing_portfolios': {}
    }
    
    # 1. 重新分析performance页面
    logger.info("\n🌐 1. 重新分析performance页面")
    page_content = finder.get_performance_page_with_js()
    if page_content:
        page_apis = finder.analyze_page_for_apis(page_content)
        all_results['page_apis'] = page_apis
    
    # 2. 尝试组合管理API
    logger.info("\n📊 2. 尝试组合管理API")
    management_results = finder.try_portfolio_management_apis()
    all_results['management_apis'] = management_results
    
    # 3. 尝试创建组合API
    logger.info("\n➕ 3. 尝试创建组合API")
    create_results = finder.try_create_portfolio_apis()
    all_results['create_apis'] = create_results
    
    # 4. 详细检查现有组合
    logger.info("\n🔍 4. 详细检查现有组合")
    existing_portfolios = finder.check_existing_portfolios_detailed()
    all_results['existing_portfolios'] = existing_portfolios
    
    # 5. 汇总结果
    logger.info("\n📈 5. 结果汇总")
    logger.info("=" * 60)
    
    if management_results:
        logger.info(f"✅ 组合管理API: 找到 {len(management_results)} 个有效API")
        for result in management_results:
            logger.info(f"   🎯 {result['url']}: {result['keywords']}")
    else:
        logger.warning("⚠️ 组合管理API: 未找到有效API")
    
    if create_results:
        logger.info(f"✅ 创建组合API: 找到 {len(create_results)} 个成功的API")
        for result in create_results:
            logger.info(f"   ➕ {result['api']}: {result['result']}")
    else:
        logger.warning("⚠️ 创建组合API: 所有尝试都失败")
    
    if existing_portfolios:
        logger.info(f"✅ 现有组合: 找到 {len(existing_portfolios)} 个组合/分类")
        for key, portfolio in existing_portfolios.items():
            logger.info(f"   📊 {portfolio['name']}: {portfolio['stock_count']} 只股票")
    else:
        logger.warning("⚠️ 现有组合: 未找到任何组合")
    
    # 6. 保存结果
    try:
        with open('portfolio_management_apis.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        logger.info(f"\n💾 完整结果已保存到: portfolio_management_apis.json")
    except Exception as e:
        logger.error(f"\n❌ 保存结果失败: {e}")
    
    print(f"\n🎉 组合管理API查找完成！")
    
    # 给出具体建议
    if management_results:
        print(f"🎯 发现有效的组合管理API，可以获取您的真实持仓数据")
    elif existing_portfolios:
        print(f"📊 发现现有组合数据，可以基于此创建模拟持仓")
    else:
        print(f"💡 建议: 请在雪球网页上手动创建模拟盈亏账户")

if __name__ == '__main__':
    main()
