// 订单服务 - 处理各种订单类型的管理

export interface Order {
  id: string
  user_id: number
  user_name: string
  order_type: 'recharge' | 'subscription' | 'strategy' | 'withdraw' | 'refund'
  title: string
  description: string
  amount: number
  original_amount?: number // 原价（用于折扣）
  discount_amount?: number // 折扣金额
  payment_method: 'alipay' | 'wechat' | 'bank' | 'balance' | 'combo'
  status: 'pending' | 'paid' | 'cancelled' | 'refunded' | 'failed'
  create_time: string
  pay_time?: string
  cancel_time?: string
  refund_time?: string
  expire_time?: string
  transaction_id?: string // 第三方交易号
  remark?: string
  extra_data?: any // 额外数据（如策略ID、订阅类型等）
}

export interface UserBalance {
  user_id: number
  balance: number
  frozen_balance: number
  total_recharge: number
  total_consume: number
  update_time: string
}

export interface StrategyProduct {
  id: string
  creator_id: number
  creator_name: string
  name: string
  description: string
  price: number
  original_price?: number
  category: string
  tags: string[]
  sales_count: number
  rating: number
  status: 'active' | 'inactive' | 'deleted'
  create_time: string
  update_time: string
}

// 模拟订单数据库
class OrderDatabase {
  private orders: Order[] = []
  private userBalances: Map<number, UserBalance> = new Map()
  private strategyProducts: StrategyProduct[] = []
  private nextOrderId = 1

  constructor() {
    this.initializeData()
  }

  private initializeData() {
    // 初始化策略商品
    this.strategyProducts = [
      {
        id: 'strategy_001',
        creator_id: 1,
        creator_name: 'admin',
        name: 'AI量化趋势策略',
        description: '基于机器学习的趋势跟踪策略，适合中长线投资',
        price: 299,
        original_price: 399,
        category: '趋势策略',
        tags: ['AI', '机器学习', '趋势跟踪'],
        sales_count: 156,
        rating: 4.8,
        status: 'active',
        create_time: '2024-01-01T00:00:00Z',
        update_time: '2024-08-01T00:00:00Z'
      },
      {
        id: 'strategy_002',
        creator_id: 2,
        creator_name: 'quant_master',
        name: '均值回归套利策略',
        description: '经典的均值回归策略，稳健收益，风险可控',
        price: 199,
        category: '套利策略',
        tags: ['均值回归', '套利', '稳健'],
        sales_count: 89,
        rating: 4.5,
        status: 'active',
        create_time: '2024-02-01T00:00:00Z',
        update_time: '2024-07-15T00:00:00Z'
      }
    ]

    // 生成模拟订单数据
    this.generateMockOrders()
    
    // 初始化用户余额
    this.initializeUserBalances()
  }

  private generateMockOrders() {
    const orderTypes = ['recharge', 'subscription', 'strategy', 'withdraw', 'refund'] as const
    const paymentMethods = ['alipay', 'wechat', 'bank', 'balance'] as const
    const statuses = ['pending', 'paid', 'cancelled', 'refunded', 'failed'] as const

    // 生成最近30天的订单
    for (let i = 0; i < 200; i++) {
      const orderType = orderTypes[Math.floor(Math.random() * orderTypes.length)]
      const userId = Math.floor(Math.random() * 10) + 1
      const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      
      let title = ''
      let description = ''
      let amount = 0
      let extraData: any = {}

      switch (orderType) {
        case 'recharge':
          amount = [50, 100, 200, 500, 1000][Math.floor(Math.random() * 5)]
          title = '账户充值'
          description = `充值${amount}元到账户余额`
          break
        case 'subscription':
          const subTypes = [
            { type: 'jiben', name: '基础版', price: 99 },
            { type: 'gaoji', name: '高级版', price: 299 }
          ]
          const subType = subTypes[Math.floor(Math.random() * subTypes.length)]
          amount = subType.price
          title = `${subType.name}订阅`
          description = `升级到${subType.name}，享受更多功能`
          extraData = { subscription_type: subType.type, duration: 30 }
          break
        case 'strategy':
          const strategy = this.strategyProducts[Math.floor(Math.random() * this.strategyProducts.length)]
          amount = strategy.price
          title = `购买策略：${strategy.name}`
          description = `购买${strategy.creator_name}的量化策略`
          extraData = { strategy_id: strategy.id, creator_id: strategy.creator_id }
          break
        case 'withdraw':
          amount = Math.floor(Math.random() * 500) + 100
          title = '余额提现'
          description = `提现${amount}元到银行卡`
          break
        case 'refund':
          amount = Math.floor(Math.random() * 300) + 50
          title = '订单退款'
          description = '订阅服务退款'
          break
      }

      const order: Order = {
        id: `ORD${Date.now()}${i.toString().padStart(3, '0')}`,
        user_id: userId,
        user_name: `user${userId}`,
        order_type: orderType,
        title,
        description,
        amount,
        payment_method: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
        create_time: createTime.toISOString(),
        transaction_id: `TXN${Date.now()}${i}`,
        extra_data: extraData
      }

      // 设置支付时间等
      if (order.status === 'paid') {
        order.pay_time = new Date(createTime.getTime() + Math.random() * 60 * 60 * 1000).toISOString()
      } else if (order.status === 'cancelled') {
        order.cancel_time = new Date(createTime.getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString()
      } else if (order.status === 'refunded') {
        order.pay_time = new Date(createTime.getTime() + Math.random() * 60 * 60 * 1000).toISOString()
        order.refund_time = new Date(createTime.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      }

      this.orders.push(order)
    }

    // 按创建时间排序
    this.orders.sort((a, b) => new Date(b.create_time).getTime() - new Date(a.create_time).getTime())
  }

  private initializeUserBalances() {
    // 为前10个用户初始化余额
    for (let i = 1; i <= 10; i++) {
      this.userBalances.set(i, {
        user_id: i,
        balance: Math.floor(Math.random() * 1000),
        frozen_balance: Math.floor(Math.random() * 100),
        total_recharge: Math.floor(Math.random() * 5000),
        total_consume: Math.floor(Math.random() * 3000),
        update_time: new Date().toISOString()
      })
    }
  }

  // 获取订单列表
  getOrders(options: {
    page?: number
    pageSize?: number
    orderType?: string
    status?: string
    userId?: number
    startDate?: string
    endDate?: string
    keyword?: string
  } = {}): { orders: Order[], total: number } {
    let filtered = [...this.orders]

    // 筛选条件
    if (options.orderType) {
      filtered = filtered.filter(order => order.order_type === options.orderType)
    }
    if (options.status) {
      filtered = filtered.filter(order => order.status === options.status)
    }
    if (options.userId) {
      filtered = filtered.filter(order => order.user_id === options.userId)
    }
    if (options.startDate) {
      filtered = filtered.filter(order => order.create_time >= options.startDate!)
    }
    if (options.endDate) {
      filtered = filtered.filter(order => order.create_time <= options.endDate!)
    }
    if (options.keyword) {
      const keyword = options.keyword.toLowerCase()
      filtered = filtered.filter(order => 
        order.title.toLowerCase().includes(keyword) ||
        order.user_name.toLowerCase().includes(keyword) ||
        order.id.toLowerCase().includes(keyword)
      )
    }

    const total = filtered.length
    
    // 分页
    const page = options.page || 1
    const pageSize = options.pageSize || 20
    const start = (page - 1) * pageSize
    const orders = filtered.slice(start, start + pageSize)

    return { orders, total }
  }

  // 获取订单详情
  getOrderById(orderId: string): Order | null {
    return this.orders.find(order => order.id === orderId) || null
  }

  // 获取用户余额
  getUserBalance(userId: number): UserBalance | null {
    return this.userBalances.get(userId) || null
  }

  // 获取策略商品列表
  getStrategyProducts(): StrategyProduct[] {
    return this.strategyProducts.filter(product => product.status === 'active')
  }

  // 获取订单统计
  getOrderStats() {
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)

    const todayOrders = this.orders.filter(order => new Date(order.create_time) >= todayStart)
    const monthOrders = this.orders.filter(order => new Date(order.create_time) >= monthStart)
    const paidOrders = this.orders.filter(order => order.status === 'paid')

    const todayRevenue = todayOrders
      .filter(order => order.status === 'paid')
      .reduce((sum, order) => sum + order.amount, 0)

    const monthRevenue = monthOrders
      .filter(order => order.status === 'paid')
      .reduce((sum, order) => sum + order.amount, 0)

    const totalRevenue = paidOrders.reduce((sum, order) => sum + order.amount, 0)

    return {
      totalOrders: this.orders.length,
      todayOrders: todayOrders.length,
      monthOrders: monthOrders.length,
      paidOrders: paidOrders.length,
      pendingOrders: this.orders.filter(order => order.status === 'pending').length,
      todayRevenue,
      monthRevenue,
      totalRevenue,
      averageOrderValue: paidOrders.length > 0 ? totalRevenue / paidOrders.length : 0
    }
  }

  // 更新订单状态
  updateOrderStatus(orderId: string, status: Order['status'], remark?: string): boolean {
    const order = this.orders.find(o => o.id === orderId)
    if (!order) return false

    order.status = status
    if (remark) order.remark = remark

    const now = new Date().toISOString()
    switch (status) {
      case 'paid':
        order.pay_time = now
        break
      case 'cancelled':
        order.cancel_time = now
        break
      case 'refunded':
        order.refund_time = now
        break
    }

    return true
  }
}

// 创建全局订单数据库实例
const orderDB = new OrderDatabase()

// 导出订单服务
export const orderService = {
  // 获取订单列表
  getOrders: async (options: any = {}) => {
    await new Promise(resolve => setTimeout(resolve, 400))
    return orderDB.getOrders(options)
  },

  // 获取订单详情
  getOrderDetail: async (orderId: string) => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return orderDB.getOrderById(orderId)
  },

  // 获取用户余额
  getUserBalance: async (userId: number) => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return orderDB.getUserBalance(userId)
  },

  // 获取策略商品
  getStrategyProducts: async () => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return orderDB.getStrategyProducts()
  },

  // 获取订单统计
  getOrderStats: async () => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return orderDB.getOrderStats()
  },

  // 更新订单状态
  updateOrderStatus: async (orderId: string, status: Order['status'], remark?: string) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return orderDB.updateOrderStatus(orderId, status, remark)
  }
}

export default orderService
