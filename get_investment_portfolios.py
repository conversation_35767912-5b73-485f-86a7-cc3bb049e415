#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取雪球投资组合（模拟盈亏组合）
区别于自选股，这是真正的投资组合
"""

import json
import logging
import requests
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InvestmentPortfolioManager:
    """投资组合管理器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 解析cookies
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ Token设置成功: u={u}")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            return False
    
    def get_investment_portfolios_via_api(self):
        """通过API获取投资组合列表"""
        try:
            logger.info("🔍 通过API获取投资组合...")
            
            # 尝试不同的API端点
            api_urls = [
                'https://xueqiu.com/cubes/list.json',  # 组合列表
                'https://xueqiu.com/v4/stock/portfolio/list.json',  # 投资组合列表
                'https://xueqiu.com/stock/portfolio/list.json',  # 另一个投资组合API
                'https://xueqiu.com/v5/stock/portfolio/list.json',  # v5版本
            ]
            
            for url in api_urls:
                try:
                    logger.info(f"   尝试API: {url}")
                    response = self.session.get(url, timeout=10)
                    
                    if response.status_code == 200:
                        data = response.json()
                        logger.info(f"   ✅ API响应成功: {url}")
                        logger.info(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        
                        # 检查是否包含组合数据
                        if 'list' in data or 'data' in data:
                            return data
                    else:
                        logger.warning(f"   ❌ API响应失败: {response.status_code}")
                        
                except Exception as e:
                    logger.warning(f"   ❌ API调用异常: {e}")
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取投资组合失败: {e}")
            return None
    
    def search_user_cubes(self):
        """搜索用户创建的组合"""
        try:
            logger.info("🔍 搜索用户创建的组合...")
            
            # 获取用户ID
            u = self.session.cookies.get('u', '')
            if not u:
                logger.error("❌ 无法获取用户ID")
                return []
            
            # 尝试通过用户页面获取组合
            user_url = f"https://xueqiu.com/u/{u}"
            logger.info(f"   访问用户页面: {user_url}")
            
            response = self.session.get(user_url, timeout=10)
            if response.status_code == 200:
                logger.info("   ✅ 用户页面访问成功")
                # 这里可以解析页面内容获取组合信息
                # 但通常需要解析HTML，比较复杂
            
            # 尝试组合搜索API
            search_url = "https://xueqiu.com/cubes/search.json"
            params = {
                'q': '测试',  # 搜索"测试"组合
                'count': 20,
                'market': 'cn'
            }
            
            logger.info(f"   搜索组合: {search_url}")
            response = self.session.get(search_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"   ✅ 搜索响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return data
            else:
                logger.warning(f"   ❌ 搜索失败: {response.status_code}")
            
            return []
            
        except Exception as e:
            logger.error(f"❌ 搜索用户组合失败: {e}")
            return []
    
    def try_direct_cube_access(self):
        """尝试直接访问可能的组合ID"""
        try:
            logger.info("🔍 尝试直接访问可能的组合...")
            
            # 获取用户ID
            u = self.session.cookies.get('u', '')
            if not u:
                logger.error("❌ 无法获取用户ID")
                return []
            
            logger.info(f"   用户ID: {u}")
            
            # 尝试一些可能的组合ID格式
            # 雪球组合ID通常是数字，我们可以尝试一些范围
            possible_cube_ids = []
            
            # 基于用户ID生成可能的组合ID
            user_id_int = int(u)
            
            # 尝试一些可能的组合ID
            test_ranges = [
                range(1, 100),  # 1-99
                range(user_id_int, user_id_int + 100),  # 基于用户ID的范围
                range(user_id_int - 100, user_id_int),  # 用户ID之前的范围
            ]
            
            found_cubes = []
            
            for test_range in test_ranges:
                for cube_id in test_range:
                    cube_symbol = f"ZH{cube_id:06d}"
                    
                    try:
                        # 尝试获取组合信息
                        result = ball.nav_daily(cube_symbol)
                        if result and len(result) > 0:
                            cube_data = result[0]
                            cube_name = cube_data.get('name', '')
                            
                            # 检查是否是目标组合
                            if cube_name in ['测试', 'test'] or '测试' in cube_name.lower() or 'test' in cube_name.lower():
                                logger.info(f"   ✅ 找到可能的组合: {cube_name} ({cube_symbol})")
                                found_cubes.append({
                                    'symbol': cube_symbol,
                                    'name': cube_name,
                                    'id': cube_id
                                })
                        
                        # 限制搜索数量，避免过多请求
                        if len(found_cubes) >= 5:
                            break
                            
                    except:
                        # 忽略错误，继续搜索
                        pass
                
                if found_cubes:
                    break
            
            return found_cubes
            
        except Exception as e:
            logger.error(f"❌ 直接访问组合失败: {e}")
            return []
    
    def get_portfolio_by_name_search(self, target_names=['测试', 'test']):
        """通过名称搜索获取组合"""
        try:
            logger.info(f"🔍 搜索名为 {target_names} 的组合...")
            
            found_portfolios = []
            
            for name in target_names:
                try:
                    # 使用pysnowball的搜索功能
                    logger.info(f"   搜索: {name}")
                    
                    # 这里我们需要尝试不同的搜索方法
                    # 方法1: 直接搜索组合
                    search_url = f"https://xueqiu.com/cubes/search.json?q={name}&count=20&market=cn"
                    
                    response = self.session.get(search_url, timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        
                        if 'list' in data:
                            cubes = data['list']
                            for cube in cubes:
                                cube_name = cube.get('name', '')
                                cube_symbol = cube.get('symbol', '')
                                
                                if cube_name == name:
                                    logger.info(f"   ✅ 找到匹配组合: {cube_name} ({cube_symbol})")
                                    found_portfolios.append({
                                        'name': cube_name,
                                        'symbol': cube_symbol,
                                        'id': cube.get('id', ''),
                                        'data': cube
                                    })
                    
                except Exception as e:
                    logger.warning(f"   搜索 {name} 失败: {e}")
            
            return found_portfolios
            
        except Exception as e:
            logger.error(f"❌ 按名称搜索组合失败: {e}")
            return []
    
    def refresh_portfolio_holdings(self, portfolio_symbol, portfolio_name):
        """刷新组合持仓"""
        try:
            logger.info(f"🔄 刷新组合持仓: {portfolio_name} ({portfolio_symbol})")
            logger.info("=" * 50)
            
            # 获取组合当前持仓
            holdings_result = ball.rebalancing_current(portfolio_symbol)
            
            if holdings_result:
                last_rb = holdings_result.get('last_rb', {})
                holdings = last_rb.get('holdings', [])
                
                logger.info(f"✅ 持仓信息:")
                logger.info(f"   股票数量: {len(holdings)}")
                
                if holdings:
                    total_weight = 0
                    for i, holding in enumerate(holdings, 1):
                        stock_symbol = holding.get('stock_symbol', 'N/A')
                        stock_name = holding.get('stock_name', 'N/A')
                        weight = holding.get('weight', 0)
                        volume = holding.get('volume', 0)
                        
                        total_weight += weight
                        
                        logger.info(f"   {i}. {stock_name} ({stock_symbol})")
                        logger.info(f"      权重: {weight:.2f}%")
                        logger.info(f"      数量: {volume}")
                    
                    logger.info(f"   总权重: {total_weight:.2f}%")
                    logger.info(f"   现金比例: {100 - total_weight:.2f}%")
                else:
                    logger.info("   📝 组合为空")
            else:
                logger.warning(f"⚠️ 无法获取组合持仓")
            
            # 获取组合表现
            performance_result = ball.quote_current(portfolio_symbol)
            
            if performance_result and portfolio_symbol in performance_result:
                perf_data = performance_result[portfolio_symbol]
                
                logger.info(f"📈 组合表现:")
                logger.info(f"   当前净值: {perf_data.get('net_value', 'N/A')}")
                logger.info(f"   日收益率: {perf_data.get('daily_gain', 'N/A')}%")
                logger.info(f"   总收益率: {perf_data.get('total_gain', 'N/A')}%")
                logger.info(f"   年化收益率: {perf_data.get('annualized_gain', 'N/A')}%")
            else:
                logger.warning(f"⚠️ 无法获取组合表现")
            
            logger.info("✅ 刷新完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 刷新组合持仓失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 雪球投资组合管理器")
    print("获取用户的模拟盈亏组合并刷新持仓")
    print("=" * 60)
    
    manager = InvestmentPortfolioManager()
    
    # 方法1: 通过API获取
    logger.info("📋 方法1: 通过API获取投资组合")
    api_result = manager.get_investment_portfolios_via_api()
    
    # 方法2: 搜索用户组合
    logger.info("\n📋 方法2: 搜索用户组合")
    search_result = manager.search_user_cubes()
    
    # 方法3: 按名称搜索
    logger.info("\n📋 方法3: 按名称搜索组合")
    name_search_result = manager.get_portfolio_by_name_search(['测试', 'test'])
    
    # 方法4: 尝试直接访问
    logger.info("\n📋 方法4: 尝试直接访问可能的组合")
    direct_result = manager.try_direct_cube_access()
    
    # 汇总结果
    logger.info("\n📊 搜索结果汇总:")
    logger.info("=" * 50)
    
    all_found = []
    
    if name_search_result:
        all_found.extend(name_search_result)
        logger.info(f"✅ 按名称搜索找到 {len(name_search_result)} 个组合")
    
    if direct_result:
        all_found.extend(direct_result)
        logger.info(f"✅ 直接访问找到 {len(direct_result)} 个组合")
    
    if all_found:
        logger.info(f"\n🎯 找到的目标组合:")
        for portfolio in all_found:
            logger.info(f"   📊 {portfolio['name']} ({portfolio['symbol']})")
            
            # 刷新每个找到的组合
            manager.refresh_portfolio_holdings(portfolio['symbol'], portfolio['name'])
            logger.info("-" * 50)
    else:
        logger.warning("⚠️ 没有找到名为'测试'或'test'的组合")
        logger.info("💡 建议:")
        logger.info("   1. 确认组合名称是否正确")
        logger.info("   2. 检查组合是否为公开状态")
        logger.info("   3. 确认登录账户是否正确")

if __name__ == '__main__':
    main()
