<template>
  <div class="user-management-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">用户管理</span>
          <el-button type="primary" @click="handleAddUser">
            <el-icon><Plus /></el-icon>
            添加用户
          </el-button>
        </div>
      </template>
      
      <div class="user-management-content">
        <!-- 搜索和筛选 -->
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索用户名或邮箱"
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select v-model="statusFilter" placeholder="用户状态" style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="活跃" value="active" />
            <el-option label="未激活" value="inactive" />
          </el-select>
          
          <el-select v-model="subscriptionFilter" placeholder="订阅类型" style="width: 140px">
            <el-option label="全部" value="" />
            <el-option label="注册用户版" value="zhuce" />
            <el-option label="基础版" value="jiben" />
            <el-option label="高级版" value="gaoji" />
          </el-select>
        </div>
        
        <!-- 用户表格 -->
        <el-table :data="filteredUsers" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="yonghuming" label="用户名" width="150" />
          <el-table-column prop="youxiang" label="邮箱" width="200" />
          <el-table-column prop="dingyue_leixing" label="订阅类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getSubscriptionTagType(row.dingyue_leixing)">
                {{ getSubscriptionText(row.dingyue_leixing) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="shi_huoyue" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.shi_huoyue ? 'success' : 'danger'">
                {{ row.shi_huoyue ? '活跃' : '未激活' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="chuangjian_shijian" label="注册时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.chuangjian_shijian) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEditUser(row)">
                编辑
              </el-button>
              <el-button 
                :type="row.shi_huoyue ? 'warning' : 'success'" 
                size="small" 
                @click="handleToggleStatus(row)"
              >
                {{ row.shi_huoyue ? '禁用' : '启用' }}
              </el-button>
              <el-button type="danger" size="small" @click="handleDeleteUser(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalUsers"
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { userService } from '@/fuwu/userService'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const subscriptionFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// 用户数据
const users = ref<any[]>([])

// 计算属性
const filteredUsers = computed(() => {
  let filtered = users.value
  
  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(user => 
      user.yonghuming.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      user.youxiang.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(user => 
      statusFilter.value === 'active' ? user.shi_huoyue : !user.shi_huoyue
    )
  }
  
  // 订阅类型过滤
  if (subscriptionFilter.value) {
    filtered = filtered.filter(user => user.dingyue_leixing === subscriptionFilter.value)
  }
  
  return filtered
})

const totalUsers = computed(() => filteredUsers.value.length)

// 方法
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const getSubscriptionText = (type: string) => {
  const typeMap: Record<string, string> = {
    zhuce: '注册用户版',
    jiben: '基础版',
    gaoji: '高级版'
  }
  return typeMap[type] || '未知'
}

const getSubscriptionTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    zhuce: 'info',
    jiben: 'warning',
    gaoji: 'success'
  }
  return typeMap[type] || 'info'
}

const handleAddUser = () => {
  ElMessage.info('添加用户功能开发中')
}

const handleEditUser = (user: any) => {
  ElMessage.info(`编辑用户: ${user.yonghuming}`)
}

const handleToggleStatus = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要${user.shi_huoyue ? '禁用' : '启用'}用户 ${user.yonghuming} 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await userService.updateUser(user.id, {
      shi_huoyue: !user.shi_huoyue
    })

    if (result.success) {
      user.shi_huoyue = !user.shi_huoyue
      ElMessage.success(`用户状态已${user.shi_huoyue ? '启用' : '禁用'}`)
    } else {
      ElMessage.error(result.message)
    }
  } catch {
    // 用户取消操作
  }
}

const handleDeleteUser = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${user.yonghuming} 吗？此操作不可恢复！`,
      '危险操作',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const result = await userService.deleteUser(user.id)

    if (result.success) {
      const index = users.value.findIndex(u => u.id === user.id)
      if (index > -1) {
        users.value.splice(index, 1)
        ElMessage.success('用户已删除')
      }
    } else {
      ElMessage.error(result.message)
    }
  } catch {
    // 用户取消操作
  }
}

// 加载用户数据
const loadUsers = async () => {
  try {
    loading.value = true
    const allUsers = await userService.getAllUsers()
    users.value = allUsers
  } catch (error) {
    console.error('加载用户数据失败:', error)
    ElMessage.error('加载用户数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  console.log('用户管理页面已加载')
  loadUsers()
})
</script>

<style lang="scss" scoped>
.user-management-container {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .card-title {
    font-size: 18px;
    font-weight: 600;
  }
}

.user-management-content {
  .search-bar {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    align-items: center;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    align-items: stretch;
    
    .el-input,
    .el-select {
      width: 100% !important;
    }
  }
}
</style>
