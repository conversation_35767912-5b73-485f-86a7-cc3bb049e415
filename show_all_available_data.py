#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
展示从雪球API能获取到的所有数据
清楚显示哪些数据可以获取，哪些无法获取
"""

import json
import logging
import requests
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class XueqiuDataExplorer:
    """雪球数据探索器"""
    
    def __init__(self):
        self.user_id = "4380321271"
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ 使用用户 {u} 的Token")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            return False
    
    def test_pysnowball_functions(self):
        """测试pysnowball库的所有可用功能"""
        print("\n🔍 测试pysnowball库功能")
        print("=" * 60)
        
        # pysnowball的主要功能
        test_functions = [
            # 基础行情功能
            ("实时行情", lambda: ball.quotec("SH600507")),
            ("五档行情", lambda: ball.pankou("SH600507")),
            ("股票搜索", lambda: ball.suggest_stock("方大特钢")),
            
            # 自选股功能
            ("自选股列表", lambda: ball.watch_list()),
            ("我的持仓(-4)", lambda: ball.watch_stock(-4)),
            ("我的组合(-8)", lambda: ball.watch_stock(-8)),
            ("全部自选(-1)", lambda: ball.watch_stock(-1)),
            ("沪深自选(-5)", lambda: ball.watch_stock(-5)),
            ("港股自选(-7)", lambda: ball.watch_stock(-7)),
            ("美股自选(-6)", lambda: ball.watch_stock(-6)),
            
            # 组合功能
            ("组合净值", lambda: ball.nav_daily("ZH000001")),
            ("组合当前调仓", lambda: ball.rebalancing_current("ZH000001")),
            ("组合历史调仓", lambda: ball.rebalancing_history("ZH000001")),
            
            # 其他功能
            ("热门股票", lambda: ball.hot_stock()),
            ("涨跌排行", lambda: ball.rank_list()),
        ]
        
        results = {}
        
        for func_name, func in test_functions:
            try:
                print(f"\n📊 测试: {func_name}")
                result = func()
                
                if result:
                    print(f"   ✅ 成功获取数据")
                    print(f"   数据类型: {type(result)}")
                    
                    if isinstance(result, dict):
                        keys = list(result.keys())
                        print(f"   主要字段: {keys[:5]}{'...' if len(keys) > 5 else ''}")
                        
                        # 显示部分数据
                        if 'data' in result:
                            data = result['data']
                            if isinstance(data, list) and data:
                                print(f"   数据条数: {len(data)}")
                                print(f"   样本数据: {json.dumps(data[0], ensure_ascii=False)[:200]}...")
                            elif isinstance(data, dict):
                                print(f"   数据内容: {json.dumps(data, ensure_ascii=False)[:200]}...")
                        
                    elif isinstance(result, list):
                        print(f"   数据条数: {len(result)}")
                        if result:
                            print(f"   样本数据: {json.dumps(result[0], ensure_ascii=False)[:200]}...")
                    
                    results[func_name] = {"status": "success", "data": result}
                else:
                    print(f"   ⚠️ 返回空数据")
                    results[func_name] = {"status": "empty", "data": None}
                    
            except Exception as e:
                print(f"   ❌ 失败: {e}")
                results[func_name] = {"status": "error", "error": str(e)}
        
        return results
    
    def test_direct_api_calls(self):
        """测试直接API调用"""
        print("\n🌐 测试直接API调用")
        print("=" * 60)
        
        # 各种雪球API端点
        api_tests = [
            # 用户相关
            ("用户信息", f"https://xueqiu.com/account/info.json"),
            ("用户组合列表", f"https://xueqiu.com/v4/stock/portfolio/list.json?user_id={self.user_id}"),
            ("用户持仓", f"https://xueqiu.com/v4/stock/portfolio/stocks.json?user_id={self.user_id}"),
            
            # 组合相关
            ("组合搜索", "https://xueqiu.com/cubes/search.json?q=测试&count=20"),
            ("我的组合", f"https://xueqiu.com/cubes/list.json?user_id={self.user_id}"),
            
            # 交易相关
            ("交易记录", f"https://xueqiu.com/v4/stock/portfolio/trades.json?user_id={self.user_id}"),
            ("持仓详情", f"https://xueqiu.com/v4/stock/portfolio/detail.json?user_id={self.user_id}"),
            
            # 行情相关
            ("股票行情", "https://xueqiu.com/v5/stock/quote.json?symbol=SH600507"),
            ("实时价格", "https://xueqiu.com/v5/stock/realtime/quotec.json?symbol=SH600507"),
        ]
        
        results = {}
        
        for api_name, url in api_tests:
            try:
                print(f"\n🔗 测试API: {api_name}")
                print(f"   URL: {url}")
                
                response = self.session.get(url, timeout=10)
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"   ✅ 成功获取JSON数据")
                        print(f"   数据大小: {len(str(data))} 字符")
                        
                        # 显示主要字段
                        if isinstance(data, dict):
                            keys = list(data.keys())
                            print(f"   主要字段: {keys}")
                            
                            # 显示部分数据内容
                            preview = json.dumps(data, ensure_ascii=False, indent=2)[:500]
                            print(f"   数据预览: {preview}...")
                        
                        results[api_name] = {"status": "success", "data": data}
                        
                    except json.JSONDecodeError:
                        print(f"   ⚠️ 非JSON响应")
                        text_preview = response.text[:200]
                        print(f"   响应内容: {text_preview}...")
                        results[api_name] = {"status": "non_json", "content": response.text}
                        
                else:
                    print(f"   ❌ HTTP错误: {response.status_code}")
                    print(f"   错误内容: {response.text[:200]}...")
                    results[api_name] = {"status": "http_error", "code": response.status_code, "content": response.text}
                    
            except Exception as e:
                print(f"   ❌ 请求异常: {e}")
                results[api_name] = {"status": "exception", "error": str(e)}
        
        return results
    
    def analyze_available_data(self):
        """分析可获取的数据"""
        print("\n📊 数据可用性分析")
        print("=" * 60)
        
        # 已知可以获取的数据
        available_data = {
            "✅ 完全可用": [
                "股票实时行情 (价格、涨跌、成交量等)",
                "五档买卖盘数据",
                "股票搜索功能",
                "自选股列表 (您的方大特钢)",
                "热门股票排行",
                "涨跌排行榜",
                "股票基本信息",
            ],
            "⚠️ 部分可用": [
                "用户组合分类 (能看到分类，但无详细持仓)",
                "组合搜索 (能搜索公开组合)",
                "用户基本信息 (部分字段)",
            ],
            "❌ 无法获取": [
                "具体持仓数量 (多少股)",
                "买入成本价格",
                "持仓盈亏计算",
                "交易历史记录",
                "资金余额",
                "私有组合详情",
                "实际交易权限",
            ]
        }
        
        for category, items in available_data.items():
            print(f"\n{category}:")
            for item in items:
                print(f"   • {item}")
        
        print(f"\n💡 总结:")
        print(f"   • 雪球的公开API主要提供行情数据和基础信息")
        print(f"   • 个人交易数据需要更高权限或专门的交易API")
        print(f"   • 持仓详情可能需要通过雪球的组合功能或第三方券商API")
    
    def show_your_real_data(self):
        """展示您的真实可用数据"""
        print("\n🎯 您的真实可用数据")
        print("=" * 60)
        
        # 1. 您的自选股
        print("\n📋 1. 您的自选股 (我的持仓)")
        try:
            result = ball.watch_stock(-4)
            if result and 'data' in result:
                stocks = result['data'].get('stocks', [])
                print(f"   股票数量: {len(stocks)}")
                
                for i, stock in enumerate(stocks, 1):
                    symbol = stock.get('symbol', '')
                    name = stock.get('name', '')
                    
                    # 获取实时行情
                    quote = ball.quotec(symbol)
                    if quote and 'data' in quote and quote['data']:
                        q = quote['data'][0]
                        print(f"   {i}. {name} ({symbol})")
                        print(f"      当前价: ¥{q.get('current', 0)}")
                        print(f"      涨跌: {q.get('chg', 0)} ({q.get('percent', 0)}%)")
                        print(f"      成交量: {q.get('volume', 0):,}")
                        print(f"      成交额: ¥{q.get('amount', 0):,.2f}")
                        print(f"      总市值: ¥{q.get('market_capital', 0):,.2f}")
                        print(f"      开高低收: {q.get('open', 0)}/{q.get('high', 0)}/{q.get('low', 0)}/{q.get('last_close', 0)}")
        except Exception as e:
            print(f"   ❌ 获取失败: {e}")
        
        # 2. 五档行情
        print("\n📈 2. 方大特钢五档行情")
        try:
            pankou = ball.pankou("SH600507")
            if pankou:
                print("   买盘:")
                for i in range(1, 6):
                    bp = pankou.get(f'bp{i}', 0)
                    bc = pankou.get(f'bc{i}', 0)
                    print(f"     买{i}: ¥{bp} × {bc}")
                
                print("   卖盘:")
                for i in range(1, 6):
                    sp = pankou.get(f'sp{i}', 0)
                    sc = pankou.get(f'sc{i}', 0)
                    print(f"     卖{i}: ¥{sp} × {sc}")
        except Exception as e:
            print(f"   ❌ 获取失败: {e}")
        
        # 3. 您的组合分类
        print("\n📂 3. 您的组合分类")
        try:
            watch_list = ball.watch_list()
            if watch_list and 'data' in watch_list:
                data = watch_list['data']
                
                categories = ['portfolios', 'cubes', 'funds']
                for category in categories:
                    if category in data:
                        items = data[category]
                        print(f"   {category} ({len(items)}个):")
                        for item in items:
                            name = item.get('name', 'N/A')
                            item_id = item.get('id', 'N/A')
                            print(f"     • {name} (ID: {item_id})")
        except Exception as e:
            print(f"   ❌ 获取失败: {e}")
        
        # 4. 搜索功能演示
        print("\n🔍 4. 搜索功能演示")
        search_terms = ['茅台', '平安', '腾讯']
        for term in search_terms:
            try:
                result = ball.suggest_stock(term)
                if result and 'data' in result:
                    stocks = result['data'][:3]  # 只显示前3个
                    print(f"   搜索'{term}' (前3个结果):")
                    for stock in stocks:
                        print(f"     • {stock.get('query', 'N/A')} ({stock.get('code', 'N/A')})")
            except Exception as e:
                print(f"   搜索'{term}'失败: {e}")

def main():
    """主函数"""
    print("🔍 雪球API数据探索报告")
    print("展示所有可获取的数据")
    print("=" * 80)
    
    explorer = XueqiuDataExplorer()
    
    # 1. 测试pysnowball功能
    pysnowball_results = explorer.test_pysnowball_functions()
    
    # 2. 测试直接API调用
    api_results = explorer.test_direct_api_calls()
    
    # 3. 分析数据可用性
    explorer.analyze_available_data()
    
    # 4. 展示您的真实数据
    explorer.show_your_real_data()
    
    # 5. 保存完整结果
    full_results = {
        'user_id': explorer.user_id,
        'timestamp': datetime.now().isoformat(),
        'pysnowball_results': pysnowball_results,
        'api_results': api_results
    }
    
    try:
        with open('xueqiu_data_exploration_report.json', 'w', encoding='utf-8') as f:
            json.dump(full_results, f, indent=2, ensure_ascii=False)
        print(f"\n💾 完整报告已保存到: xueqiu_data_exploration_report.json")
    except Exception as e:
        print(f"\n❌ 保存报告失败: {e}")
    
    print(f"\n🎉 数据探索完成！")
    print(f"📊 总结: 雪球API主要提供行情数据，个人交易数据需要更高权限")

if __name__ == '__main__':
    main()
