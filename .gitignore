# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 数据库
*.db
*.sqlite3
data/timescaledb/
data/redis/
data/pgadmin/

# 日志
logs/
*.log

# 配置文件中的敏感信息
.env.local
.env.production
config/secrets.yaml

# 测试
.coverage
htmlcov/
.pytest_cache/
.tox/

# 前端
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.DS_Store

# Electron
dist-electron/
release/

# 临时文件
*.tmp
*.temp
.cache/

# 系统文件
Thumbs.db
.DS_Store
