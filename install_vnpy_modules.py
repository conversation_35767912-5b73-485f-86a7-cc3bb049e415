#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
安装VeighNa额外模块
包括交易接口、策略应用、数据源等
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_package(package_name, description=""):
    """安装单个包"""
    try:
        logger.info(f"📦 安装 {package_name} {description}")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name, 
            "--extra-index-url", "https://pypi.vnpy.com"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"   ✅ {package_name} 安装成功")
            return True
        else:
            logger.warning(f"   ⚠️ {package_name} 安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"   ❌ {package_name} 安装异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 VeighNa模块安装器")
    print("安装交易接口、策略应用、数据源等模块")
    print("=" * 80)
    
    # 核心策略应用
    strategy_apps = [
        ("vnpy-ctastrategy", "CTA策略引擎"),
        ("vnpy-ctabacktester", "CTA策略回测"),
        ("vnpy-portfoliostrategy", "组合策略"),
        ("vnpy-algotrading", "算法交易"),
        ("vnpy-paperaccount", "模拟账户"),
        ("vnpy-spreadtrading", "价差交易"),
        ("vnpy-optionmaster", "期权策略"),
        ("vnpy-scripttrader", "脚本策略"),
    ]
    
    # 数据和工具
    data_tools = [
        ("vnpy-datamanager", "数据管理"),
        ("vnpy-datarecorder", "数据记录"),
        ("vnpy-chartwizard", "图表分析"),
        ("vnpy-riskmanager", "风险管理"),
        ("vnpy-webtrader", "Web交易"),
        ("vnpy-portfoliomanager", "组合管理"),
    ]
    
    # A股交易接口
    a_stock_gateways = [
        ("vnpy-ctp", "CTP期货接口"),
        ("vnpy-mini", "CTP Mini接口"),
        ("vnpy-xtp", "中泰XTP A股接口"),
        ("vnpy-tora", "华鑫奇点A股接口"),
        ("vnpy-ost", "东方证券A股接口"),
        ("vnpy-emt", "东方财富A股接口"),
    ]
    
    # 数据源
    data_feeds = [
        ("vnpy-rqdata", "米筐RQData数据源"),
        ("vnpy-tushare", "TuShare数据源"),
        ("vnpy-wind", "Wind数据源"),
        ("vnpy-ifind", "同花顺iFinD数据源"),
    ]
    
    # 数据库
    databases = [
        ("vnpy-sqlite", "SQLite数据库"),
        ("vnpy-mysql", "MySQL数据库"),
        ("vnpy-postgresql", "PostgreSQL数据库"),
        ("vnpy-mongodb", "MongoDB数据库"),
    ]
    
    print("\n📦 1. 安装核心策略应用")
    print("-" * 40)
    for package, desc in strategy_apps:
        install_package(package, f"({desc})")
    
    print("\n🔧 2. 安装数据和工具")
    print("-" * 40)
    for package, desc in data_tools:
        install_package(package, f"({desc})")
    
    print("\n🏦 3. 安装A股交易接口")
    print("-" * 40)
    for package, desc in a_stock_gateways:
        install_package(package, f"({desc})")
    
    print("\n📊 4. 安装数据源")
    print("-" * 40)
    for package, desc in data_feeds:
        install_package(package, f"({desc})")
    
    print("\n💾 5. 安装数据库支持")
    print("-" * 40)
    for package, desc in databases:
        install_package(package, f"({desc})")
    
    print("\n🎉 VeighNa模块安装完成！")
    print("=" * 60)
    print("💡 使用说明:")
    print("   1. 重新启动VeighNa: python run_vnpy_trader.py")
    print("   2. 在系统菜单中配置交易接口")
    print("   3. 在功能菜单中使用各种应用模块")
    print("   4. 查看官方文档: https://www.vnpy.com/docs/")

if __name__ == '__main__':
    main()
