[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "quantitative-trading-platform"
version = "0.1.0"
description = "A股量化交易平台 - 个人投资者专业量化工具"
authors = [
    {name = "开发团队", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
keywords = ["quantitative", "trading", "stock", "a-share", "investment"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Office/Business :: Financial :: Investment",
]

dependencies = [
    # Web框架
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    
    # 数据库
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    
    # 数据处理
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "tushare>=1.2.89",
    
    # 技术指标
    "talib>=0.4.0",
    
    # 任务调度
    "apscheduler>=3.10.0",
    
    # 加密和安全
    "cryptography>=41.0.0",
    "passlib[bcrypt]>=1.7.4",
    "python-jose[cryptography]>=3.3.0",
    
    # 配置管理
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    
    # 日志
    "loguru>=0.7.0",
    
    # HTTP客户端
    "httpx>=0.25.0",
    "requests>=2.31.0",
    
    # 工具库
    "python-multipart>=0.0.6",
    "python-dateutil>=2.8.2",
    "pytz>=2023.3",
]

[project.optional-dependencies]
dev = [
    # 代码质量
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
    
    # 测试
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "httpx>=0.25.0",  # 用于测试API
    
    # 文档
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    
    # 开发工具
    "ipython>=8.15.0",
    "jupyter>=1.0.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "httpx>=0.25.0",
]

docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/your-org/quantitative-trading-platform"
Documentation = "https://your-org.github.io/quantitative-trading-platform"
Repository = "https://github.com/your-org/quantitative-trading-platform.git"
Issues = "https://github.com/your-org/quantitative-trading-platform/issues"

[project.scripts]
qtp-server = "src.backend.main:main"

# 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除的目录
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | node_modules
)/
'''

# 导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = ["fastapi", "sqlalchemy", "pandas", "numpy", "tushare"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

# 类型检查配置
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "tushare.*",
    "talib.*",
    "apscheduler.*",
]
ignore_missing_imports = true

# 测试配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
testpaths = ["test"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

# 测试覆盖率配置
[tool.coverage.run]
source = ["src"]
omit = [
    "*/test/*",
    "*/tests/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/virtualenv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Flake8配置 (在setup.cfg中配置，因为flake8不支持pyproject.toml)
# 这里仅作为参考，实际配置在.flake8文件中

[tool.setuptools.packages.find]
where = ["src"]
include = ["*"]
exclude = ["test*"]

[tool.setuptools.package-data]
"*" = ["*.txt", "*.md", "*.json", "*.yaml", "*.yml"]
