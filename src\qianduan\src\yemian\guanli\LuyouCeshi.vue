<template>
  <div class="route-debug-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">路由调试工具</span>
          <el-button @click="refreshInfo">刷新信息</el-button>
        </div>
      </template>
      
      <div class="debug-content">
        <!-- 当前路由信息 -->
        <el-card class="debug-section">
          <template #header>当前路由信息</template>
          <div class="info-grid">
            <div class="info-item">
              <label>当前路径:</label>
              <span>{{ route.fullPath }}</span>
            </div>
            <div class="info-item">
              <label>解码路径:</label>
              <span>{{ decodeURIComponent(route.fullPath) }}</span>
            </div>
            <div class="info-item">
              <label>路由名称:</label>
              <span>{{ route.name }}</span>
            </div>
            <div class="info-item">
              <label>路由参数:</label>
              <span>{{ JSON.stringify(route.params) }}</span>
            </div>
            <div class="info-item">
              <label>查询参数:</label>
              <span>{{ JSON.stringify(route.query) }}</span>
            </div>
          </div>
        </el-card>
        
        <!-- 用户状态信息 -->
        <el-card class="debug-section">
          <template #header>用户状态</template>
          <div class="info-grid">
            <div class="info-item">
              <label>普通用户登录:</label>
              <el-tag :type="userStore.isLoggedIn ? 'success' : 'danger'">
                {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
              </el-tag>
            </div>
            <div class="info-item">
              <label>管理员登录:</label>
              <el-tag :type="adminStore.isAdminLoggedIn ? 'success' : 'danger'">
                {{ adminStore.isAdminLoggedIn ? '已登录' : '未登录' }}
              </el-tag>
            </div>
            <div class="info-item" v-if="adminStore.isAdminLoggedIn">
              <label>管理员信息:</label>
              <span>{{ adminStore.adminInfo?.username }}</span>
            </div>
          </div>
        </el-card>
        
        <!-- 权限信息 -->
        <el-card class="debug-section" v-if="adminStore.isAdminLoggedIn">
          <template #header>管理员权限</template>
          <div class="permissions-grid">
            <el-tag 
              v-for="permission in adminStore.adminPermissions" 
              :key="permission"
              type="success"
              class="permission-tag"
            >
              {{ permission }}
            </el-tag>
          </div>
        </el-card>
        
        <!-- 系统操作 -->
        <el-card class="debug-section">
          <template #header>系统操作</template>
          <div class="system-actions">
            <el-button type="warning" @click="forceRefreshAdminState">
              强制刷新管理员状态
            </el-button>
            <el-button type="danger" @click="clearAllStorage">
              清除所有存储
            </el-button>
            <el-button type="info" @click="reloginAdmin">
              重新登录管理员
            </el-button>
          </div>
        </el-card>

        <!-- 路由测试 -->
        <el-card class="debug-section">
          <template #header>路由测试</template>
          <div class="route-tests">
            <el-button
              v-for="testRoute in testRoutes"
              :key="testRoute.path"
              @click="testNavigation(testRoute)"
              :type="testRoute.type"
              class="test-button"
            >
              {{ testRoute.name }}
            </el-button>
          </div>
        </el-card>
        
        <!-- 错误日志 -->
        <el-card class="debug-section" v-if="errorLogs.length > 0">
          <template #header>错误日志</template>
          <div class="error-logs">
            <div 
              v-for="(error, index) in errorLogs" 
              :key="index"
              class="error-item"
            >
              <div class="error-time">{{ error.time }}</div>
              <div class="error-message">{{ error.message }}</div>
              <div class="error-details" v-if="error.details">{{ error.details }}</div>
            </div>
          </div>
        </el-card>
        
        <!-- 本地存储信息 -->
        <el-card class="debug-section">
          <template #header>本地存储</template>
          <div class="storage-info">
            <div class="storage-item">
              <label>管理员Token:</label>
              <span>{{ localStorage.getItem('admin-token') ? '存在' : '不存在' }}</span>
            </div>
            <div class="storage-item">
              <label>管理员信息:</label>
              <span>{{ localStorage.getItem('admin-info') ? '存在' : '不存在' }}</span>
            </div>
            <div class="storage-item">
              <label>管理员权限:</label>
              <span>{{ localStorage.getItem('admin-permissions') ? '存在' : '不存在' }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/shangdian/user'
import { useAdminStore } from '@/shangdian/admin'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const adminStore = useAdminStore()

const errorLogs = ref<Array<{time: string, message: string, details?: string}>>([])

const testRoutes = [
  { path: '/numendavid中国/dashboard', name: '管理员仪表板', type: 'primary' },
  { path: '/numendavid中国/data', name: '数据管理', type: 'success' },
  { path: '/numendavid中国/subscriptions', name: '订阅类型管理', type: 'warning' },
  { path: '/numendavid中国/strategies', name: '策略商品', type: 'info' },
  { path: '/numendavid中国/users', name: '用户管理', type: 'primary' },
  { path: '/numendavid中国/system', name: '系统配置', type: 'success' }
]

const addErrorLog = (message: string, details?: string) => {
  errorLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    details
  })
  
  // 只保留最近10条错误
  if (errorLogs.value.length > 10) {
    errorLogs.value = errorLogs.value.slice(0, 10)
  }
}

const testNavigation = async (testRoute: any) => {
  try {
    console.log(`测试导航到: ${testRoute.path}`)
    
    // 检查权限
    const routeConfig = router.getRoutes().find(r => r.path === testRoute.path)
    if (routeConfig?.meta?.permission) {
      const hasPermission = adminStore.hasAdminPermission(routeConfig.meta.permission as any)
      console.log(`权限检查 (${routeConfig.meta.permission}):`, hasPermission)
      
      if (!hasPermission) {
        addErrorLog(`权限不足: ${routeConfig.meta.permission}`, `路由: ${testRoute.path}`)
        ElMessage.error(`权限不足: ${routeConfig.meta.permission}`)
        return
      }
    }
    
    await router.push(testRoute.path)
    ElMessage.success(`成功导航到: ${testRoute.name}`)
  } catch (error: any) {
    console.error('导航失败:', error)
    addErrorLog('导航失败', `${testRoute.path}: ${error.message}`)
    ElMessage.error(`导航失败: ${error.message}`)
  }
}

const refreshInfo = () => {
  // 强制刷新所有响应式数据
  console.log('=== 路由调试信息 ===')
  console.log('当前路由:', route.fullPath)
  console.log('用户登录状态:', userStore.isLoggedIn)
  console.log('管理员登录状态:', adminStore.isAdminLoggedIn)
  console.log('管理员权限:', adminStore.adminPermissions)
  console.log('本地存储 - 管理员Token:', localStorage.getItem('admin-token'))
  console.log('本地存储 - 管理员信息:', localStorage.getItem('admin-info'))
  console.log('本地存储 - 管理员权限:', localStorage.getItem('admin-permissions'))

  ElMessage.info('调试信息已输出到控制台')
}

const forceRefreshAdminState = () => {
  try {
    console.log('强制刷新管理员状态...')

    // 从localStorage重新加载状态
    const savedToken = localStorage.getItem('admin-token')
    const savedAdminInfo = localStorage.getItem('admin-info')
    const savedPermissions = localStorage.getItem('admin-permissions')

    if (savedToken && savedAdminInfo && savedPermissions) {
      adminStore.setAdminToken(savedToken)
      adminStore.setAdminInfo(JSON.parse(savedAdminInfo))
      adminStore.setAdminPermissions(JSON.parse(savedPermissions))

      console.log('管理员状态已刷新')
      console.log('新的权限列表:', adminStore.adminPermissions)

      ElMessage.success('管理员状态已刷新')
    } else {
      ElMessage.warning('未找到有效的管理员状态数据')
    }
  } catch (error: any) {
    console.error('刷新管理员状态失败:', error)
    ElMessage.error('刷新失败: ' + error.message)
  }
}

const clearAllStorage = async () => {
  try {
    await ElMessageBox.confirm('确定要清除所有本地存储数据吗？这将注销所有用户。', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 清除所有localStorage数据
    localStorage.clear()

    // 重置所有store状态
    adminStore.resetAdmin()
    userStore.logout()

    ElMessage.success('所有存储数据已清除')

    // 重定向到首页
    router.push('/')
  } catch {
    // 用户取消操作
  }
}

const reloginAdmin = () => {
  // 清除管理员状态
  adminStore.adminLogout()

  // 重定向到管理员登录页
  router.push('/numendavid中国/login')

  ElMessage.info('请重新登录管理员账号')
}

onMounted(() => {
  console.log('路由调试工具已加载')
  refreshInfo()
})
</script>

<style lang="scss" scoped>
.route-debug-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .card-title {
    font-size: 18px;
    font-weight: 600;
  }
}

.debug-content {
  .debug-section {
    margin-bottom: 20px;
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    
    .info-item {
      display: flex;
      align-items: center;
      gap: 8px;
      
      label {
        font-weight: 600;
        min-width: 100px;
      }
    }
  }
  
  .permissions-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .permission-tag {
      margin: 0;
    }
  }
  
  .route-tests {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    
    .test-button {
      margin: 0;
    }
  }
  
  .error-logs {
    .error-item {
      padding: 12px;
      border: 1px solid var(--el-border-color);
      border-radius: 6px;
      margin-bottom: 8px;
      background: var(--el-bg-color);
      
      .error-time {
        font-size: 12px;
        color: var(--el-text-color-placeholder);
        margin-bottom: 4px;
      }
      
      .error-message {
        font-weight: 600;
        color: var(--el-color-danger);
        margin-bottom: 4px;
      }
      
      .error-details {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
  }
  
  .storage-info {
    .storage-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      
      label {
        font-weight: 600;
        min-width: 120px;
      }
    }
  }
}
</style>
