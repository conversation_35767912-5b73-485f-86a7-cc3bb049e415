<template>
  <div class="security-audit-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">安全审计</span>
          <div class="header-actions">
            <el-button @click="refreshLogs">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="primary" @click="exportLogs">
              <el-icon><Download /></el-icon>
              导出日志
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="audit-content">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 登录日志 -->
          <el-tab-pane label="登录日志" name="login">
            <div class="log-filters">
              <el-date-picker
                v-model="loginFilters.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
              <el-select v-model="loginFilters.status" placeholder="登录状态" style="width: 120px">
                <el-option label="全部" value="" />
                <el-option label="成功" value="success" />
                <el-option label="失败" value="failed" />
              </el-select>
              <el-input
                v-model="loginFilters.username"
                placeholder="用户名"
                style="width: 200px"
                clearable
              />
            </div>
            
            <el-table :data="loginLogs" style="width: 100%" v-loading="loginLoading">
              <el-table-column prop="username" label="用户名" width="150" />
              <el-table-column prop="ip" label="IP地址" width="150" />
              <el-table-column prop="userAgent" label="浏览器" width="200" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                    {{ row.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="loginTime" label="登录时间" width="180" />
              <el-table-column prop="location" label="地理位置" />
            </el-table>
          </el-tab-pane>
          
          <!-- 操作日志 -->
          <el-tab-pane label="操作日志" name="operation">
            <div class="log-filters">
              <el-date-picker
                v-model="operationFilters.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
              <el-select v-model="operationFilters.module" placeholder="功能模块" style="width: 150px">
                <el-option label="全部" value="" />
                <el-option label="用户管理" value="user" />
                <el-option label="策略管理" value="strategy" />
                <el-option label="系统配置" value="system" />
              </el-select>
              <el-input
                v-model="operationFilters.username"
                placeholder="操作用户"
                style="width: 150px"
                clearable
              />
            </div>
            
            <el-table :data="operationLogs" style="width: 100%" v-loading="operationLoading">
              <el-table-column prop="username" label="操作用户" width="120" />
              <el-table-column prop="module" label="功能模块" width="120" />
              <el-table-column prop="action" label="操作内容" width="200" />
              <el-table-column prop="ip" label="IP地址" width="150" />
              <el-table-column prop="operationTime" label="操作时间" width="180" />
              <el-table-column prop="result" label="结果" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.result === 'success' ? 'success' : 'danger'">
                    {{ row.result === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="details" label="详情" />
            </el-table>
          </el-tab-pane>
          
          <!-- 安全事件 -->
          <el-tab-pane label="安全事件" name="security">
            <div class="security-events">
              <el-alert
                title="安全监控"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  系统会自动检测异常登录、暴力破解、权限异常等安全事件
                </template>
              </el-alert>
              
              <div class="event-filters">
                <el-select v-model="securityFilters.level" placeholder="威胁级别" style="width: 120px">
                  <el-option label="全部" value="" />
                  <el-option label="高危" value="high" />
                  <el-option label="中危" value="medium" />
                  <el-option label="低危" value="low" />
                </el-select>
                <el-select v-model="securityFilters.status" placeholder="处理状态" style="width: 120px">
                  <el-option label="全部" value="" />
                  <el-option label="待处理" value="pending" />
                  <el-option label="已处理" value="resolved" />
                  <el-option label="已忽略" value="ignored" />
                </el-select>
              </div>
              
              <el-table :data="securityEvents" style="width: 100%" v-loading="securityLoading">
                <el-table-column prop="type" label="事件类型" width="150" />
                <el-table-column prop="level" label="威胁级别" width="100">
                  <template #default="{ row }">
                    <el-tag 
                      :type="row.level === 'high' ? 'danger' : row.level === 'medium' ? 'warning' : 'info'"
                    >
                      {{ row.level === 'high' ? '高危' : row.level === 'medium' ? '中危' : '低危' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="事件描述" width="300" />
                <el-table-column prop="ip" label="来源IP" width="150" />
                <el-table-column prop="occurTime" label="发生时间" width="180" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag 
                      :type="row.status === 'resolved' ? 'success' : row.status === 'pending' ? 'warning' : 'info'"
                    >
                      {{ row.status === 'resolved' ? '已处理' : row.status === 'pending' ? '待处理' : '已忽略' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-button 
                      v-if="row.status === 'pending'"
                      type="primary" 
                      size="small" 
                      @click="handleSecurityEvent(row, 'resolved')"
                    >
                      处理
                    </el-button>
                    <el-button 
                      v-if="row.status === 'pending'"
                      type="info" 
                      size="small" 
                      @click="handleSecurityEvent(row, 'ignored')"
                    >
                      忽略
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

// 响应式数据
const activeTab = ref('login')
const loginLoading = ref(false)
const operationLoading = ref(false)
const securityLoading = ref(false)

// 过滤条件
const loginFilters = reactive({
  dateRange: [],
  status: '',
  username: ''
})

const operationFilters = reactive({
  dateRange: [],
  module: '',
  username: ''
})

const securityFilters = reactive({
  level: '',
  status: ''
})

// 登录日志
const loginLogs = ref([
  {
    username: 'trader001',
    ip: '*************',
    userAgent: 'Chrome 91.0',
    status: 'success',
    loginTime: dayjs().subtract(10, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    location: '北京市'
  },
  {
    username: 'admin',
    ip: '*************',
    userAgent: 'Firefox 89.0',
    status: 'failed',
    loginTime: dayjs().subtract(30, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    location: '上海市'
  }
])

// 操作日志
const operationLogs = ref([
  {
    username: 'superadmin',
    module: '用户管理',
    action: '创建用户',
    ip: '***********',
    operationTime: dayjs().subtract(5, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    result: 'success',
    details: '创建用户 newuser001'
  },
  {
    username: 'admin',
    module: '系统配置',
    action: '修改配置',
    ip: '***********',
    operationTime: dayjs().subtract(15, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    result: 'success',
    details: '修改邮件服务器配置'
  }
])

// 安全事件
const securityEvents = ref([
  {
    id: 1,
    type: '暴力破解',
    level: 'high',
    description: '检测到来自 ***********00 的暴力破解尝试',
    ip: '***********00',
    occurTime: dayjs().subtract(1, 'hour').format('YYYY-MM-DD HH:mm:ss'),
    status: 'pending'
  },
  {
    id: 2,
    type: '异常登录',
    level: 'medium',
    description: '用户在异常时间段登录',
    ip: '*************',
    occurTime: dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'),
    status: 'resolved'
  }
])

// 方法
const refreshLogs = () => {
  ElMessage.success('日志已刷新')
}

const exportLogs = () => {
  ElMessage.info('正在导出日志文件...')
}

const handleSecurityEvent = (event: any, action: string) => {
  event.status = action
  ElMessage.success(`安全事件已${action === 'resolved' ? '处理' : '忽略'}`)
}

// 生命周期
onMounted(() => {
  console.log('安全审计页面已加载')
})
</script>

<style lang="scss" scoped>
.security-audit-container {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .card-title {
    font-size: 18px;
    font-weight: 600;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.audit-content {
  :deep(.el-tabs__content) {
    padding: 20px;
  }
}

.log-filters,
.event-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  align-items: center;
}

.security-events {
  .event-filters {
    margin-top: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .log-filters,
  .event-filters {
    flex-direction: column;
    align-items: stretch;
    
    .el-date-picker,
    .el-select,
    .el-input {
      width: 100% !important;
    }
  }
}
</style>
