<template>
  <div class="dashboard-container">
    <!-- 顶部统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon profit">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ formatCurrency(accountStats.totalAssets) }}</div>
            <div class="stat-label">总资产</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon" :class="accountStats.totalReturn >= 0 ? 'profit' : 'loss'">
            <el-icon><Promotion /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value" :class="accountStats.totalReturn >= 0 ? 'profit-text' : 'loss-text'">
              {{ formatPercent(accountStats.totalReturn) }}
            </div>
            <div class="stat-label">总收益率</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon strategy">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ strategyStats.activeCount }}</div>
            <div class="stat-label">运行策略</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon backtest">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ backtestStats.totalCount }}</div>
            <div class="stat-label">回测次数</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧图表区域 -->
      <div class="chart-section">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">资产走势</span>
              <div class="chart-controls">
                <el-radio-group v-model="chartPeriod" size="small">
                  <el-radio-button label="1D">日</el-radio-button>
                  <el-radio-button label="1W">周</el-radio-button>
                  <el-radio-button label="1M">月</el-radio-button>
                  <el-radio-button label="3M">季</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          
          <div class="chart-container">
            <v-chart 
              :option="assetChartOption" 
              :loading="chartLoading"
              class="asset-chart"
              autoresize
            />
          </div>
        </el-card>
      </div>
      
      <!-- 右侧信息面板 -->
      <div class="info-section">
        <!-- 策略状态 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">策略状态</span>
              <el-button type="primary" size="small" @click="$router.push('/strategy/list')">
                管理策略
              </el-button>
            </div>
          </template>
          
          <div class="strategy-list">
            <div 
              v-for="strategy in recentStrategies" 
              :key="strategy.id"
              class="strategy-item"
            >
              <div class="strategy-info">
                <div class="strategy-name">{{ strategy.name }}</div>
                <div class="strategy-return" :class="strategy.return >= 0 ? 'profit-text' : 'loss-text'">
                  {{ formatPercent(strategy.return) }}
                </div>
              </div>
              <div class="strategy-status">
                <el-tag 
                  :type="strategy.status === 'running' ? 'success' : 'info'"
                  size="small"
                >
                  {{ strategy.status === 'running' ? '运行中' : '已停止' }}
                </el-tag>
              </div>
            </div>
            
            <div v-if="recentStrategies.length === 0" class="empty-state">
              <el-empty description="暂无策略" :image-size="80" />
            </div>
          </div>
        </el-card>
        
        <!-- 持仓概览 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">持仓概览</span>
              <el-button type="primary" size="small" @click="$router.push('/trading/position')">
                查看详情
              </el-button>
            </div>
          </template>
          
          <div class="position-list">
            <div 
              v-for="position in topPositions" 
              :key="position.symbol"
              class="position-item"
            >
              <div class="position-info">
                <div class="position-symbol">{{ position.symbol }}</div>
                <div class="position-name">{{ position.name }}</div>
              </div>
              <div class="position-value">
                <div class="position-amount">{{ formatCurrency(position.marketValue) }}</div>
                <div class="position-return" :class="position.return >= 0 ? 'profit-text' : 'loss-text'">
                  {{ formatPercent(position.return) }}
                </div>
              </div>
            </div>
            
            <div v-if="topPositions.length === 0" class="empty-state">
              <el-empty description="暂无持仓" :image-size="80" />
            </div>
          </div>
        </el-card>
        
        <!-- 最新资讯 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">市场资讯</span>
              <el-button type="text" size="small">
                更多
              </el-button>
            </div>
          </template>
          
          <div class="news-list">
            <div 
              v-for="news in marketNews" 
              :key="news.id"
              class="news-item"
            >
              <div class="news-content">
                <div class="news-title">{{ news.title }}</div>
                <div class="news-time">{{ formatTime(news.publishTime) }}</div>
              </div>
              <div class="news-tag">
                <el-tag :type="getNewsTagType(news.type)" size="small">
                  {{ news.type }}
                </el-tag>
              </div>
            </div>
            
            <div v-if="marketNews.length === 0" class="empty-state">
              <el-empty description="暂无资讯" :image-size="80" />
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { use } from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import dayjs from 'dayjs'

// 注册ECharts组件
use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

// 响应式数据
const chartPeriod = ref('1M')
const chartLoading = ref(false)

// 模拟数据
const accountStats = ref({
  totalAssets: 1050000,
  totalReturn: 0.05,
  availableCash: 800000,
  marketValue: 250000
})

const strategyStats = ref({
  activeCount: 3,
  totalCount: 8,
  profitCount: 5
})

const backtestStats = ref({
  totalCount: 15,
  successCount: 12,
  avgReturn: 0.08
})

const recentStrategies = ref([
  { id: 1, name: 'ATR RSI策略', return: 0.08, status: 'running' },
  { id: 2, name: '双均线策略', return: 0.03, status: 'running' },
  { id: 3, name: 'MACD策略', return: -0.02, status: 'stopped' }
])

const topPositions = ref([
  { symbol: '000001', name: '平安银行', marketValue: 50000, return: 0.05 },
  { symbol: '000002', name: '万科A', marketValue: 45000, return: -0.02 },
  { symbol: '600519', name: '贵州茅台', marketValue: 80000, return: 0.12 }
])

const marketNews = ref([
  { id: 1, title: 'A股三大指数集体上涨，创业板指涨超1%', type: '市场', publishTime: new Date() },
  { id: 2, title: '央行降准释放流动性，利好股市', type: '政策', publishTime: new Date(Date.now() - 3600000) },
  { id: 3, title: '人工智能板块持续活跃，多股涨停', type: '板块', publishTime: new Date(Date.now() - 7200000) }
])

// 图表配置
const assetChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const data = params[0]
      return `${data.name}<br/>总资产: ${formatCurrency(data.value)}`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: generateDateRange(),
    axisLine: {
      lineStyle: {
        color: '#e4e7ed'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => formatCurrency(value, false)
    },
    axisLine: {
      lineStyle: {
        color: '#e4e7ed'
      }
    },
    splitLine: {
      lineStyle: {
        color: '#f5f7fa'
      }
    }
  },
  series: [
    {
      name: '总资产',
      type: 'line',
      data: generateAssetData(),
      smooth: true,
      lineStyle: {
        color: '#409eff',
        width: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
          ]
        }
      }
    }
  ]
}))

// 工具函数
const formatCurrency = (value: number, showSymbol = true) => {
  const formatted = (value / 10000).toFixed(1) + '万'
  return showSymbol ? `¥${formatted}` : formatted
}

const formatPercent = (value: number) => {
  return `${(value * 100).toFixed(2)}%`
}

const formatTime = (time: Date) => {
  return dayjs(time).format('HH:mm')
}

const getNewsTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    '市场': 'primary',
    '政策': 'success',
    '板块': 'warning'
  }
  return typeMap[type] || 'info'
}

const generateDateRange = () => {
  const days = chartPeriod.value === '1D' ? 7 : 30
  const dates = []
  for (let i = days - 1; i >= 0; i--) {
    dates.push(dayjs().subtract(i, 'day').format('MM-DD'))
  }
  return dates
}

const generateAssetData = () => {
  const baseValue = 1000000
  const days = chartPeriod.value === '1D' ? 7 : 30
  const data = []
  
  for (let i = 0; i < days; i++) {
    const randomChange = (Math.random() - 0.5) * 20000
    const value = baseValue + randomChange + (i * 1000)
    data.push(Math.max(value, baseValue * 0.9))
  }
  
  return data
}

// 生命周期
onMounted(() => {
  // 这里可以加载真实数据
  console.log('仪表板页面已加载')
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  
  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        
        &.profit {
          background: linear-gradient(135deg, #67c23a, #85ce61);
          color: white;
        }
        
        &.loss {
          background: linear-gradient(135deg, #f56c6c, #f78989);
          color: white;
        }
        
        &.strategy {
          background: linear-gradient(135deg, #409eff, #66b1ff);
          color: white;
        }
        
        &.backtest {
          background: linear-gradient(135deg, #e6a23c, #ebb563);
          color: white;
        }
      }
      
      .stat-info {
        flex: 1;
        
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
          
          &.profit-text {
            color: var(--el-color-success);
          }
          
          &.loss-text {
            color: var(--el-color-danger);
          }
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

.main-content {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  min-height: 0;
}

.chart-section {
  .chart-card {
    height: 100%;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-title {
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .chart-container {
      height: 400px;
      
      .asset-chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  .info-card {
    flex: 1;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-title {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
}

.strategy-list,
.position-list,
.news-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.strategy-item,
.position-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  
  &:last-child {
    border-bottom: none;
  }
}

.strategy-info,
.position-info {
  flex: 1;
  
  .strategy-name,
  .position-symbol {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
  
  .position-name {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-top: 2px;
  }
}

.strategy-return,
.position-return {
  font-size: 12px;
  font-weight: 500;
  
  &.profit-text {
    color: var(--el-color-success);
  }
  
  &.loss-text {
    color: var(--el-color-danger);
  }
}

.position-value {
  text-align: right;
  
  .position-amount {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}

.news-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  
  &:last-child {
    border-bottom: none;
  }
  
  .news-content {
    flex: 1;
    margin-right: 12px;
    
    .news-title {
      font-size: 14px;
      color: var(--el-text-color-primary);
      line-height: 1.4;
      margin-bottom: 4px;
    }
    
    .news-time {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  .info-section {
    flex-direction: row;
    
    .info-card {
      flex: 1;
    }
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .info-section {
    flex-direction: column;
  }
  
  .chart-container {
    height: 300px !important;
  }
}
</style>
