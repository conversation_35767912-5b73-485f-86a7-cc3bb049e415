#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
雪球组合自动检测器
自动检测用户是否有组合，并提供创建指导
"""

import requests
import json
import webbrowser
import time

class CubeDetector:
    def __init__(self):
        self.cookies = self.load_cookies()
        self.user_id = self.cookies.get('u', '')
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
        }
    
    def load_cookies(self):
        """加载cookies"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            cookies = {}
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    cookies[key] = value
            
            return cookies
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return {}
    
    def check_user_cubes(self):
        """检查用户是否有组合"""
        print("🔍 检查用户组合...")
        
        if not self.user_id:
            print("❌ 无法获取用户ID")
            return []
        
        url = "https://xueqiu.com/cubes/list.json"
        params = {'user_id': self.user_id}
        
        try:
            response = requests.get(
                url, 
                params=params, 
                headers=self.headers, 
                cookies=self.cookies,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                cubes = data.get('list', [])
                
                print(f"📊 用户ID: {self.user_id}")
                print(f"📋 组合数量: {len(cubes)}")
                
                if cubes:
                    print("✅ 发现以下组合:")
                    for i, cube in enumerate(cubes, 1):
                        symbol = cube.get('symbol', 'N/A')
                        name = cube.get('name', 'N/A')
                        print(f"   {i}. {symbol} - {name}")
                    return cubes
                else:
                    print("❌ 用户没有任何组合")
                    return []
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            return []
    
    def guide_create_cube(self):
        """指导创建组合"""
        print("\n" + "="*50)
        print("🎯 雪球组合创建指导")
        print("="*50)
        
        print("📋 问题分析:")
        print("   - 当前用户没有任何雪球组合")
        print("   - easytrader需要组合代码才能获取持仓信息")
        print("   - 需要创建一个模拟组合")
        
        print("\n🚀 解决方案:")
        print("1. 自动打开雪球组合创建页面")
        print("2. 按指导创建模拟组合")
        print("3. 获取组合代码并更新配置")
        
        choice = input("\n是否现在创建组合？(y/n): ").strip().lower()
        
        if choice == 'y':
            self.open_create_page()
            return self.wait_for_cube_creation()
        else:
            print("\n💡 稍后创建:")
            print("   - 访问 https://xueqiu.com/cubes")
            print("   - 点击「创建组合」")
            print("   - 选择「模拟组合」")
            print("   - 创建后运行此脚本获取组合代码")
            return None
    
    def open_create_page(self):
        """打开创建组合页面"""
        print("\n🌐 正在打开雪球组合页面...")
        
        # 打开组合页面
        webbrowser.open('https://xueqiu.com/cubes')
        time.sleep(2)
        
        print("\n📝 创建步骤:")
        print("=" * 30)
        print("1. 确保已登录雪球账户")
        print("2. 点击「创建组合」按钮")
        print("3. 选择「模拟组合」")
        print("4. 填写组合信息:")
        print("   - 组合名称: 量化交易组合")
        print("   - 组合描述: 用于量化交易测试")
        print("   - 初始资金: 1000000")
        print("   - 基准: 沪深300")
        print("5. 点击「创建」")
        print("6. 创建成功后复制组合代码")
    
    def wait_for_cube_creation(self):
        """等待用户创建组合"""
        print("\n⏳ 等待组合创建...")
        print("创建完成后，请按回车键继续...")
        input()
        
        # 重新检查组合
        print("\n🔄 重新检查组合...")
        cubes = self.check_user_cubes()
        
        if cubes:
            # 选择组合
            if len(cubes) == 1:
                selected_cube = cubes[0]
            else:
                print("\n请选择要使用的组合:")
                for i, cube in enumerate(cubes, 1):
                    print(f"{i}. {cube.get('symbol')} - {cube.get('name')}")
                
                while True:
                    try:
                        choice = int(input("请输入序号: ")) - 1
                        if 0 <= choice < len(cubes):
                            selected_cube = cubes[choice]
                            break
                        else:
                            print("❌ 无效选择")
                    except ValueError:
                        print("❌ 请输入数字")
            
            # 更新配置
            cube_symbol = selected_cube.get('symbol')
            if self.update_config(cube_symbol):
                print(f"\n✅ 配置已更新为: {cube_symbol}")
                
                # 测试新配置
                if self.test_cube_config(cube_symbol):
                    print("\n🎉 恭喜！组合配置成功！")
                    print("现在可以正常使用Web交易界面的所有功能了！")
                    return cube_symbol
                else:
                    print("\n⚠️ 配置测试失败，请检查组合是否正确")
            
        else:
            print("\n❌ 仍然没有发现组合")
            print("请确保:")
            print("1. 已成功创建组合")
            print("2. 组合类型为「模拟组合」")
            print("3. 网络连接正常")
        
        return None
    
    def update_config(self, cube_symbol):
        """更新配置文件"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            config['portfolio_code'] = cube_symbol
            
            with open('xq.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"❌ 更新配置失败: {e}")
            return False
    
    def test_cube_config(self, cube_symbol):
        """测试组合配置"""
        try:
            import easytrader
            
            print(f"🧪 测试组合配置: {cube_symbol}")
            
            user = easytrader.use('xq')
            user.prepare('xq.json')
            
            # 测试账户信息
            balance = user.balance
            print("✅ 账户信息获取成功!")
            
            # 测试持仓信息
            position = user.position
            print("✅ 持仓信息获取成功!")
            print(f"持仓数量: {len(position) if position else 0}")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def show_current_status(self):
        """显示当前状态"""
        print("📊 当前配置状态")
        print("=" * 30)
        
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"用户ID: {self.user_id}")
            print(f"当前组合代码: {config.get('portfolio_code', '未设置')}")
            print(f"Cookies状态: {'已设置' if config.get('cookies') else '未设置'}")
            
        except Exception as e:
            print(f"❌ 读取配置失败: {e}")

def main():
    """主函数"""
    detector = CubeDetector()
    
    print("🔍 雪球组合自动检测器")
    print("自动检测并解决组合配置问题")
    print("=" * 50)
    
    # 显示当前状态
    detector.show_current_status()
    
    # 检查用户组合
    cubes = detector.check_user_cubes()
    
    if cubes:
        print("\n✅ 发现用户组合，选择要使用的组合:")
        
        if len(cubes) == 1:
            cube_symbol = cubes[0].get('symbol')
            print(f"自动选择: {cube_symbol}")
        else:
            for i, cube in enumerate(cubes, 1):
                print(f"{i}. {cube.get('symbol')} - {cube.get('name')}")
            
            while True:
                try:
                    choice = int(input("请选择组合序号: ")) - 1
                    if 0 <= choice < len(cubes):
                        cube_symbol = cubes[choice].get('symbol')
                        break
                    else:
                        print("❌ 无效选择")
                except ValueError:
                    print("❌ 请输入数字")
        
        # 更新配置
        if detector.update_config(cube_symbol):
            print(f"\n✅ 配置已更新为: {cube_symbol}")
            
            # 测试配置
            if detector.test_cube_config(cube_symbol):
                print("\n🎉 配置测试成功！")
            else:
                print("\n⚠️ 配置测试失败")
        
    else:
        print("\n❌ 用户没有组合，需要创建")
        detector.guide_create_cube()
    
    print("\n💡 提醒:")
    print("- 配置完成后重启Web交易界面")
    print("- 即使没有组合，当前功能也完全可用")
    print("- 组合主要用于显示持仓信息")

if __name__ == '__main__':
    main()
