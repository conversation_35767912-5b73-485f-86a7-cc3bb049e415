<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-code">404</div>
      <div class="error-message">页面未找到</div>
      <div class="error-description">
        抱歉，您访问的页面不存在或已被移除
      </div>

      <!-- 调试信息 -->
      <div class="debug-info" v-if="showDebugInfo">
        <el-divider>调试信息</el-divider>
        <div class="debug-item">
          <strong>当前路径:</strong> {{ currentPath }}
        </div>
        <div class="debug-item">
          <strong>用户登录状态:</strong> {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
        </div>
        <div class="debug-item">
          <strong>管理员登录状态:</strong> {{ adminStore.isAdminLoggedIn ? '已登录' : '未登录' }}
        </div>
        <div class="debug-item" v-if="adminStore.isAdminLoggedIn">
          <strong>管理员权限:</strong> {{ adminStore.adminPermissions.join(', ') }}
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="action-buttons">
        <el-button type="primary" @click="goHome">
          返回首页
        </el-button>
        <el-button v-if="userStore.isLoggedIn" @click="$router.push('/dashboard')">
          用户仪表板
        </el-button>
        <el-button v-if="adminStore.isAdminLoggedIn" @click="$router.push('/numendavid中国/dashboard')">
          管理员仪表板
        </el-button>
        <el-button @click="showDebugInfo = !showDebugInfo">
          {{ showDebugInfo ? '隐藏' : '显示' }}调试信息
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/shangdian/user'
import { useAdminStore } from '@/shangdian/admin'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const adminStore = useAdminStore()

const showDebugInfo = ref(false)

const currentPath = computed(() => route.fullPath)

const goHome = () => {
  if (adminStore.isAdminLoggedIn) {
    router.push('/numendavid中国/dashboard')
  } else if (userStore.isLoggedIn) {
    router.push('/dashboard')
  } else {
    router.push('/')
  }
}

// 自动显示调试信息（开发环境）
if (import.meta.env.DEV) {
  showDebugInfo.value = true
  console.log('404页面调试信息:')
  console.log('当前路径:', route.fullPath)
  console.log('用户登录状态:', userStore.isLoggedIn)
  console.log('管理员登录状态:', adminStore.isAdminLoggedIn)
  console.log('管理员权限:', adminStore.adminPermissions)
}
</script>

<style lang="scss" scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color-page);
}

.error-content {
  text-align: center;
  max-width: 600px;

  .error-code {
    font-size: 120px;
    font-weight: bold;
    color: var(--el-color-primary);
    line-height: 1;
    margin-bottom: 20px;
  }

  .error-message {
    font-size: 24px;
    color: var(--el-text-color-primary);
    margin-bottom: 12px;
  }

  .error-description {
    font-size: 16px;
    color: var(--el-text-color-regular);
    margin-bottom: 32px;
  }

  .debug-info {
    text-align: left;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;

    .debug-item {
      margin-bottom: 8px;
      font-size: 14px;

      strong {
        color: var(--el-text-color-primary);
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>
