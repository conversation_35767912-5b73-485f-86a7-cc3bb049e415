#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接查找用户的模拟资金账户
尝试更多可能的API端点和方法
"""

import json
import logging
import requests
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DirectSimulationFinder:
    """直接模拟账户查找器"""
    
    def __init__(self):
        self.user_id = "4380321271"
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ 使用用户 {u} 的Token")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            return False
    
    def try_mobile_apis(self):
        """尝试移动端API"""
        logger.info("📱 尝试移动端API")
        logger.info("=" * 60)
        
        mobile_apis = [
            # 移动端可能的API
            f"https://xueqiu.com/mobile/v4/portfolio/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/mobile/portfolio/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/mobile/account/list.json",
            
            # 可能的内部API
            f"https://xueqiu.com/api/portfolio/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/api/account/list.json",
            f"https://xueqiu.com/api/simulation/list.json",
            
            # 尝试不同版本的API
            f"https://xueqiu.com/v1/portfolio/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/v2/portfolio/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/v3/portfolio/list.json?user_id={self.user_id}",
            
            # 尝试股票相关的API（可能包含模拟账户）
            f"https://xueqiu.com/stock/portfolio/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/stock/account/list.json?user_id={self.user_id}",
        ]
        
        found_data = []
        
        for api_url in mobile_apis:
            try:
                logger.info(f"\n🔗 测试: {api_url}")
                response = self.session.get(api_url, timeout=10)
                
                logger.info(f"   状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   ✅ 成功获取JSON数据")
                        
                        # 检查是否包含模拟账户信息
                        data_str = json.dumps(data, ensure_ascii=False)
                        if '测试' in data_str or 'test' in data_str.lower():
                            logger.info(f"   🎯 包含目标关键词！")
                            logger.info(f"   数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                            found_data.append({
                                'api': api_url,
                                'data': data,
                                'has_target': True
                            })
                        else:
                            logger.info(f"   数据预览: {data_str[:200]}...")
                            found_data.append({
                                'api': api_url,
                                'data': data,
                                'has_target': False
                            })
                            
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON响应")
                        
                else:
                    logger.info(f"   ❌ 失败")
                    
            except Exception as e:
                logger.info(f"   ❌ 异常: {e}")
        
        return found_data
    
    def try_easytrader_simulation_codes(self):
        """尝试easytrader可能的模拟账户代码"""
        logger.info("🔧 尝试easytrader模拟账户代码")
        logger.info("=" * 60)
        
        # 基于用户ID生成可能的模拟账户代码
        possible_codes = []
        
        # 方法1: 基于用户ID的变体
        user_id_int = int(self.user_id)
        possible_codes.extend([
            f"SIM{self.user_id}",  # SIM前缀
            f"TEST{self.user_id}",  # TEST前缀
            f"DEMO{self.user_id}",  # DEMO前缀
            f"MOCK{self.user_id}",  # MOCK前缀
            f"SIMU{self.user_id}",  # SIMU前缀
            
            # 不同长度的用户ID
            f"SIM{user_id_int:06d}",
            f"TEST{user_id_int:06d}",
            f"SIM{user_id_int:08d}",
            f"TEST{user_id_int:08d}",
            
            # 简单的数字组合
            f"SIM{user_id_int % 1000000:06d}",
            f"TEST{user_id_int % 1000000:06d}",
        ])
        
        # 方法2: 常见的模拟账户格式
        possible_codes.extend([
            "SIM000001", "SIM000002", "SIM000003",
            "TEST0001", "TEST0002", "TEST0003",
            "DEMO0001", "DEMO0002", "DEMO0003",
            "MOCK0001", "MOCK0002", "MOCK0003",
            
            # 基于用户ID的哈希
            f"SIM{hash(self.user_id) % 1000000:06d}",
            f"TEST{hash(self.user_id) % 1000000:06d}",
        ])
        
        # 方法3: 尝试直接使用"测试"和"test"
        possible_codes.extend([
            "测试", "test", "Test", "TEST",
            "模拟", "simulation", "Simulation", "SIMULATION",
            "演示", "demo", "Demo", "DEMO",
        ])
        
        logger.info(f"生成了 {len(possible_codes)} 个可能的模拟账户代码")
        
        successful_codes = []
        
        # 测试每个可能的代码
        for code in possible_codes:
            try:
                logger.info(f"\n🧪 测试模拟账户代码: {code}")
                
                # 这里我们不实际测试easytrader，因为可能会有副作用
                # 而是记录这些代码供后续测试
                successful_codes.append(code)
                
            except Exception as e:
                logger.info(f"   ❌ 失败: {e}")
        
        return successful_codes
    
    def search_in_existing_data(self):
        """在已有数据中搜索"""
        logger.info("🔍 在已有数据中搜索")
        logger.info("=" * 60)
        
        found_items = []
        
        # 搜索之前获取的数据
        try:
            with open('my_real_portfolio_data.json', 'r', encoding='utf-8') as f:
                real_data = json.load(f)
            
            # 深度搜索所有数据
            def search_recursive(obj, path=""):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        
                        # 检查键名
                        if any(keyword in key.lower() for keyword in ['test', '测试', 'simulation', '模拟', 'demo', '演示']):
                            logger.info(f"   🎯 找到可疑键: {current_path} = {value}")
                            found_items.append({
                                'type': 'key',
                                'path': current_path,
                                'key': key,
                                'value': value
                            })
                        
                        # 检查值
                        if isinstance(value, str) and any(keyword in value.lower() for keyword in ['test', '测试', 'simulation', '模拟', 'demo', '演示']):
                            logger.info(f"   🎯 找到可疑值: {current_path} = {value}")
                            found_items.append({
                                'type': 'value',
                                'path': current_path,
                                'key': key,
                                'value': value
                            })
                        
                        # 递归搜索
                        search_recursive(value, current_path)
                        
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        current_path = f"{path}[{i}]" if path else f"[{i}]"
                        search_recursive(item, current_path)
            
            search_recursive(real_data)
            
        except FileNotFoundError:
            logger.info("   没有找到已有数据文件")
        except Exception as e:
            logger.info(f"   搜索失败: {e}")
        
        return found_items
    
    def try_portfolio_detail_apis(self):
        """尝试组合详情API"""
        logger.info("📊 尝试组合详情API")
        logger.info("=" * 60)
        
        # 从之前的数据中我们知道有这些组合ID
        portfolio_ids = [-4, -8, -1, -5, -7, -6]
        
        found_details = []
        
        for portfolio_id in portfolio_ids:
            try:
                logger.info(f"\n📋 获取组合 {portfolio_id} 的详情")
                
                # 尝试不同的详情API
                detail_apis = [
                    f"https://xueqiu.com/v4/stock/portfolio/detail.json?id={portfolio_id}",
                    f"https://xueqiu.com/v4/stock/portfolio/stocks.json?id={portfolio_id}",
                    f"https://xueqiu.com/v4/stock/portfolio/info.json?id={portfolio_id}",
                    f"https://xueqiu.com/portfolio/detail.json?id={portfolio_id}",
                    f"https://xueqiu.com/portfolio/stocks.json?id={portfolio_id}",
                ]
                
                for api_url in detail_apis:
                    try:
                        logger.info(f"   🔗 {api_url}")
                        response = self.session.get(api_url, timeout=10)
                        
                        if response.status_code == 200:
                            try:
                                data = response.json()
                                logger.info(f"   ✅ 成功获取数据")
                                
                                # 检查是否包含模拟账户信息
                                data_str = json.dumps(data, ensure_ascii=False)
                                if '测试' in data_str or 'test' in data_str.lower():
                                    logger.info(f"   🎯 包含目标关键词！")
                                    found_details.append({
                                        'portfolio_id': portfolio_id,
                                        'api': api_url,
                                        'data': data,
                                        'has_target': True
                                    })
                                else:
                                    found_details.append({
                                        'portfolio_id': portfolio_id,
                                        'api': api_url,
                                        'data': data,
                                        'has_target': False
                                    })
                                    
                            except json.JSONDecodeError:
                                logger.info(f"   ⚠️ 非JSON响应")
                        else:
                            logger.info(f"   ❌ 状态码: {response.status_code}")
                            
                    except Exception as e:
                        logger.info(f"   ❌ 异常: {e}")
                        
            except Exception as e:
                logger.info(f"   ❌ 组合 {portfolio_id} 处理失败: {e}")
        
        return found_details

def main():
    """主函数"""
    print("🎯 直接查找模拟资金账户")
    print("专注寻找'测试'和'test'账户")
    print("=" * 80)
    
    finder = DirectSimulationFinder()
    
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'user_id': finder.user_id,
        'mobile_api_results': [],
        'simulation_codes': [],
        'existing_data_search': [],
        'portfolio_details': []
    }
    
    # 1. 尝试移动端API
    logger.info("\n📱 1. 尝试移动端API")
    mobile_results = finder.try_mobile_apis()
    all_results['mobile_api_results'] = mobile_results
    
    # 2. 生成可能的模拟账户代码
    logger.info("\n🔧 2. 生成可能的模拟账户代码")
    simulation_codes = finder.try_easytrader_simulation_codes()
    all_results['simulation_codes'] = simulation_codes
    
    # 3. 在已有数据中搜索
    logger.info("\n🔍 3. 在已有数据中搜索")
    existing_search = finder.search_in_existing_data()
    all_results['existing_data_search'] = existing_search
    
    # 4. 尝试组合详情API
    logger.info("\n📊 4. 尝试组合详情API")
    portfolio_details = finder.try_portfolio_detail_apis()
    all_results['portfolio_details'] = portfolio_details
    
    # 5. 汇总结果
    logger.info("\n📈 5. 搜索结果汇总")
    logger.info("=" * 60)
    
    found_targets = False
    
    # 检查移动端API结果
    target_mobile = [r for r in mobile_results if r.get('has_target', False)]
    if target_mobile:
        logger.info(f"✅ 移动端API: 找到 {len(target_mobile)} 个包含目标的API")
        for result in target_mobile:
            logger.info(f"   🎯 {result['api']}")
        found_targets = True
    
    # 检查已有数据搜索结果
    if existing_search:
        logger.info(f"✅ 已有数据: 找到 {len(existing_search)} 个可疑项目")
        for item in existing_search:
            logger.info(f"   🎯 {item['path']}: {item['value']}")
        found_targets = True
    
    # 检查组合详情结果
    target_portfolios = [r for r in portfolio_details if r.get('has_target', False)]
    if target_portfolios:
        logger.info(f"✅ 组合详情: 找到 {len(target_portfolios)} 个包含目标的组合")
        for result in target_portfolios:
            logger.info(f"   🎯 组合 {result['portfolio_id']}: {result['api']}")
        found_targets = True
    
    # 显示生成的模拟账户代码
    logger.info(f"💡 生成了 {len(simulation_codes)} 个可能的模拟账户代码")
    logger.info("   前10个代码:")
    for code in simulation_codes[:10]:
        logger.info(f"     • {code}")
    
    if not found_targets:
        logger.warning("⚠️ 没有直接找到'测试'和'test'模拟账户")
        logger.info("💡 建议:")
        logger.info("   1. 检查雪球网页版是否有模拟交易入口")
        logger.info("   2. 确认模拟账户的确切名称")
        logger.info("   3. 尝试使用生成的模拟账户代码")
        logger.info("   4. 联系雪球客服了解模拟账户API")
    
    # 6. 保存结果
    try:
        with open('direct_simulation_search_results.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        logger.info(f"\n💾 完整搜索结果已保存到: direct_simulation_search_results.json")
    except Exception as e:
        logger.error(f"\n❌ 保存结果失败: {e}")
    
    print(f"\n🎉 直接搜索完成！")
    
    if found_targets:
        print(f"🎯 找到了可能的目标账户信息！")
    else:
        print(f"⚠️ 未找到明确的目标账户，但生成了 {len(simulation_codes)} 个可能的代码供测试")

if __name__ == '__main__':
    main()
