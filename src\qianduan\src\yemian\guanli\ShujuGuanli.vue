<template>
  <div class="data-management-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">数据管理</span>
          <div class="header-actions">
            <el-button type="success" @click="handleDataSync">
              <el-icon><Refresh /></el-icon>
              同步数据
            </el-button>
            <el-button type="primary" @click="handleDataBackup">
              <el-icon><Download /></el-icon>
              备份数据
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="data-content">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 数据概览 -->
          <el-tab-pane label="数据概览" name="overview">
            <div class="data-overview">
              <div class="overview-stats">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-statistic title="股票数量" :value="dataStats.stockCount" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="日线数据" :value="dataStats.dailyDataCount" suffix="条" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="分钟数据" :value="dataStats.minuteDataCount" suffix="条" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="最后更新" :value="dataStats.lastUpdate" />
                  </el-col>
                </el-row>
              </div>
              
              <div class="data-status">
                <h3>数据源状态</h3>
                <el-table :data="datasourceStatus" style="width: 100%">
                  <el-table-column prop="name" label="数据源" width="150" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="row.status === 'online' ? 'success' : 'danger'">
                        {{ row.status === 'online' ? '在线' : '离线' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="lastSync" label="最后同步" width="180" />
                  <el-table-column prop="dataCount" label="数据量" />
                  <el-table-column label="操作" width="150">
                    <template #default="{ row }">
                      <el-button type="primary" size="small" @click="syncDatasource(row)">
                        同步
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 数据备份 -->
          <el-tab-pane label="数据备份" name="backup">
            <div class="backup-management">
              <div class="backup-actions">
                <el-button type="primary" @click="createBackup">
                  <el-icon><FolderAdd /></el-icon>
                  创建备份
                </el-button>
                <el-button @click="refreshBackupList">
                  <el-icon><Refresh /></el-icon>
                  刷新列表
                </el-button>
              </div>
              
              <el-table :data="backupList" style="width: 100%" v-loading="backupLoading">
                <el-table-column prop="name" label="备份名称" />
                <el-table-column prop="size" label="文件大小" width="120" />
                <el-table-column prop="createTime" label="创建时间" width="180" />
                <el-table-column prop="type" label="备份类型" width="120">
                  <template #default="{ row }">
                    <el-tag :type="row.type === 'full' ? 'success' : 'warning'">
                      {{ row.type === 'full' ? '完整备份' : '增量备份' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button type="success" size="small" @click="downloadBackup(row)">
                      下载
                    </el-button>
                    <el-button type="warning" size="small" @click="restoreBackup(row)">
                      恢复
                    </el-button>
                    <el-button type="danger" size="small" @click="deleteBackup(row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <!-- 数据清理 -->
          <el-tab-pane label="数据清理" name="cleanup">
            <div class="data-cleanup">
              <el-alert
                title="数据清理说明"
                type="warning"
                :closable="false"
                show-icon
              >
                <template #default>
                  数据清理操作不可逆，请谨慎操作。建议在清理前先创建数据备份。
                </template>
              </el-alert>
              
              <div class="cleanup-options">
                <el-form :model="cleanupConfig" label-width="150px">
                  <el-form-item label="清理过期日志">
                    <el-switch v-model="cleanupConfig.cleanLogs" />
                    <el-input-number 
                      v-model="cleanupConfig.logRetentionDays" 
                      :min="7" 
                      :max="365"
                      :disabled="!cleanupConfig.cleanLogs"
                    />
                    <span class="form-tip">天前的日志</span>
                  </el-form-item>
                  
                  <el-form-item label="清理临时文件">
                    <el-switch v-model="cleanupConfig.cleanTempFiles" />
                  </el-form-item>
                  
                  <el-form-item label="清理缓存数据">
                    <el-switch v-model="cleanupConfig.cleanCache" />
                  </el-form-item>
                  
                  <el-form-item label="清理过期会话">
                    <el-switch v-model="cleanupConfig.cleanSessions" />
                  </el-form-item>
                  
                  <el-form-item>
                    <el-button type="danger" @click="executeCleanup" :loading="cleanupLoading">
                      <el-icon><Delete /></el-icon>
                      执行清理
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

// 响应式数据
const activeTab = ref('overview')
const backupLoading = ref(false)
const cleanupLoading = ref(false)

// 数据统计
const dataStats = ref({
  stockCount: 4500,
  dailyDataCount: 2500000,
  minuteDataCount: 150000000,
  lastUpdate: dayjs().format('YYYY-MM-DD HH:mm:ss')
})

// 数据源状态
const datasourceStatus = ref([
  {
    name: 'TuShare',
    status: 'online',
    lastSync: dayjs().subtract(5, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    dataCount: '2,500,000条'
  },
  {
    name: '东方财富',
    status: 'online',
    lastSync: dayjs().subtract(10, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    dataCount: '1,800,000条'
  },
  {
    name: '新浪财经',
    status: 'offline',
    lastSync: dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss'),
    dataCount: '500,000条'
  }
])

// 备份列表
const backupList = ref([
  {
    id: 1,
    name: 'backup_20240821_full.sql',
    size: '2.5GB',
    createTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
    type: 'full'
  },
  {
    id: 2,
    name: 'backup_20240820_incremental.sql',
    size: '150MB',
    createTime: dayjs().subtract(2, 'day').format('YYYY-MM-DD HH:mm:ss'),
    type: 'incremental'
  }
])

// 清理配置
const cleanupConfig = reactive({
  cleanLogs: true,
  logRetentionDays: 30,
  cleanTempFiles: true,
  cleanCache: false,
  cleanSessions: true
})

// 方法
const handleDataSync = async () => {
  try {
    await ElMessageBox.confirm('确定要同步所有数据源吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    ElMessage.success('数据同步已开始，请稍后查看同步状态')
  } catch {
    // 用户取消操作
  }
}

const handleDataBackup = () => {
  createBackup()
}

const syncDatasource = async (datasource: any) => {
  ElMessage.info(`正在同步 ${datasource.name} 数据源...`)
  // 模拟同步过程
  setTimeout(() => {
    datasource.lastSync = dayjs().format('YYYY-MM-DD HH:mm:ss')
    ElMessage.success(`${datasource.name} 同步完成`)
  }, 2000)
}

const createBackup = async () => {
  try {
    await ElMessageBox.confirm('创建备份可能需要较长时间，确定继续吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    backupLoading.value = true
    
    // 模拟备份过程
    setTimeout(() => {
      const newBackup = {
        id: Date.now(),
        name: `backup_${dayjs().format('YYYYMMDD_HHmmss')}_full.sql`,
        size: '2.8GB',
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        type: 'full'
      }
      
      backupList.value.unshift(newBackup)
      backupLoading.value = false
      ElMessage.success('备份创建成功')
    }, 3000)
  } catch {
    // 用户取消操作
  }
}

const refreshBackupList = () => {
  ElMessage.info('备份列表已刷新')
}

const downloadBackup = (backup: any) => {
  ElMessage.info(`正在下载备份文件: ${backup.name}`)
}

const restoreBackup = async (backup: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复备份 ${backup.name} 吗？此操作将覆盖当前数据！`,
      '危险操作',
      {
        confirmButtonText: '确定恢复',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    ElMessage.success('备份恢复已开始')
  } catch {
    // 用户取消操作
  }
}

const deleteBackup = async (backup: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除备份 ${backup.name} 吗？`,
      '提示',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = backupList.value.findIndex(b => b.id === backup.id)
    if (index > -1) {
      backupList.value.splice(index, 1)
      ElMessage.success('备份已删除')
    }
  } catch {
    // 用户取消操作
  }
}

const executeCleanup = async () => {
  try {
    await ElMessageBox.confirm(
      '数据清理操作不可逆，确定要继续吗？',
      '危险操作',
      {
        confirmButtonText: '确定清理',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    cleanupLoading.value = true
    
    // 模拟清理过程
    setTimeout(() => {
      cleanupLoading.value = false
      ElMessage.success('数据清理完成')
    }, 2000)
  } catch {
    // 用户取消操作
  }
}

// 生命周期
onMounted(() => {
  console.log('数据管理页面已加载')
})
</script>

<style lang="scss" scoped>
.data-management-container {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .card-title {
    font-size: 18px;
    font-weight: 600;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.data-content {
  :deep(.el-tabs__content) {
    padding: 20px;
  }
}

.data-overview {
  .overview-stats {
    margin-bottom: 30px;
  }
  
  .data-status {
    h3 {
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }
  }
}

.backup-management {
  .backup-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }
}

.data-cleanup {
  .cleanup-options {
    margin-top: 20px;
    
    .form-tip {
      margin-left: 10px;
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }
}
</style>
