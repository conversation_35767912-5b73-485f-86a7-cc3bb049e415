<template>
  <div class="data-management-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">数据管理</span>
          <div class="header-actions">
            <el-button type="warning" @click="showConfigDialog()">
              <el-icon><Setting /></el-icon>
              数据源配置
            </el-button>
            <el-button type="info" @click="checkDataSourceStatus()" :loading="statusLoading">
              <el-icon><Search /></el-icon>
              检查状态
            </el-button>
            <el-button type="success" @click="handleDataSync()" :loading="syncLoading">
              <el-icon><Refresh /></el-icon>
              同步数据
            </el-button>
            <el-button type="primary" @click="handleDataBackup">
              <el-icon><Download /></el-icon>
              备份数据
            </el-button>
            <el-button @click="refreshAllData" :loading="statusLoading">
              <el-icon><RefreshRight /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="data-content">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 数据概览 -->
          <el-tab-pane label="数据概览" name="overview">
            <div class="data-overview">
              <div class="overview-stats">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-statistic title="股票数量" :value="dataStats.stockCount" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="日线数据" :value="dataStats.dailyDataCount" suffix="条" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="分钟数据" :value="dataStats.minuteDataCount" suffix="条" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="最后更新" :value="dataStats.lastUpdate" />
                  </el-col>
                </el-row>
              </div>
              
              <div class="data-status">
                <h3>数据源状态</h3>
                <el-table :data="datasourceStatus" style="width: 100%" v-loading="statusLoading">
                  <el-table-column prop="name" label="数据源" width="120" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag
                        :type="getStatusTagType(row.status)"
                        :loading="row.status === 'checking'"
                      >
                        {{ getStatusText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="responseTime" label="响应时间" width="100">
                    <template #default="{ row }">
                      <span v-if="row.responseTime">{{ row.responseTime }}ms</span>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="successRate" label="成功率" width="100">
                    <template #default="{ row }">
                      <span :class="{ 'success-rate-high': row.successRate >= 90, 'success-rate-low': row.successRate < 70 }">
                        {{ row.successRate.toFixed(1) }}%
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="lastSyncTime" label="最后同步" width="160" />
                  <el-table-column prop="dataCount" label="数据量" width="100">
                    <template #default="{ row }">
                      {{ row.dataCount.toLocaleString() }}条
                    </template>
                  </el-table-column>
                  <el-table-column prop="errorMessage" label="错误信息" width="200" show-overflow-tooltip>
                    <template #default="{ row }">
                      <span v-if="row.errorMessage" class="error-message">{{ row.errorMessage }}</span>
                      <span v-else class="success-message">正常</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="280">
                    <template #default="{ row }">
                      <el-button
                        type="warning"
                        size="small"
                        @click="editDataSource(row)"
                      >
                        配置
                      </el-button>
                      <el-button
                        type="primary"
                        size="small"
                        @click="checkDataSourceStatus(row.id)"
                        :loading="statusLoading"
                      >
                        检查
                      </el-button>
                      <el-button
                        type="success"
                        size="small"
                        @click="handleDataSync(row.id)"
                        :loading="syncLoading"
                        :disabled="row.status !== 'online'"
                      >
                        同步
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        @click="deleteDataSource(row)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 数据备份 -->
          <el-tab-pane label="数据备份" name="backup">
            <div class="backup-management">
              <div class="backup-actions">
                <el-button type="primary" @click="createBackup">
                  <el-icon><FolderAdd /></el-icon>
                  创建备份
                </el-button>
                <el-button @click="refreshBackupList">
                  <el-icon><Refresh /></el-icon>
                  刷新列表
                </el-button>
              </div>
              
              <el-table :data="backupList" style="width: 100%" v-loading="backupLoading">
                <el-table-column prop="name" label="备份名称" />
                <el-table-column prop="size" label="文件大小" width="120" />
                <el-table-column prop="createTime" label="创建时间" width="180" />
                <el-table-column prop="type" label="备份类型" width="120">
                  <template #default="{ row }">
                    <el-tag :type="row.type === 'full' ? 'success' : 'warning'">
                      {{ row.type === 'full' ? '完整备份' : '增量备份' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button type="success" size="small" @click="downloadBackup(row)">
                      下载
                    </el-button>
                    <el-button type="warning" size="small" @click="restoreBackup(row)">
                      恢复
                    </el-button>
                    <el-button type="danger" size="small" @click="deleteBackup(row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <!-- 数据清理 -->
          <el-tab-pane label="数据清理" name="cleanup">
            <div class="data-cleanup">
              <el-alert
                title="数据清理说明"
                type="warning"
                :closable="false"
                show-icon
              >
                <template #default>
                  数据清理操作不可逆，请谨慎操作。建议在清理前先创建数据备份。
                </template>
              </el-alert>
              
              <div class="cleanup-options">
                <el-form :model="cleanupConfig" label-width="150px">
                  <el-form-item label="清理过期日志">
                    <el-switch v-model="cleanupConfig.cleanLogs" />
                    <el-input-number 
                      v-model="cleanupConfig.logRetentionDays" 
                      :min="7" 
                      :max="365"
                      :disabled="!cleanupConfig.cleanLogs"
                    />
                    <span class="form-tip">天前的日志</span>
                  </el-form-item>
                  
                  <el-form-item label="清理临时文件">
                    <el-switch v-model="cleanupConfig.cleanTempFiles" />
                  </el-form-item>
                  
                  <el-form-item label="清理缓存数据">
                    <el-switch v-model="cleanupConfig.cleanCache" />
                  </el-form-item>
                  
                  <el-form-item label="清理过期会话">
                    <el-switch v-model="cleanupConfig.cleanSessions" />
                  </el-form-item>
                  
                  <el-form-item>
                    <el-button type="danger" @click="executeCleanup" :loading="cleanupLoading">
                      <el-icon><Delete /></el-icon>
                      执行清理
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 数据源配置对话框 -->
    <DataSourceConfigDialog
      v-model="showConfigDialogFlag"
      :config="currentConfig"
      @refresh="refreshAllData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { datasourceService, type DataSourceStatus, type DataSourceConfig, type SyncTask } from '@/fuwu/datasourceService'
import DataSourceConfigDialog from './zujian/DataSourceConfig.vue'

// 响应式数据
const activeTab = ref('overview')
const backupLoading = ref(false)
const cleanupLoading = ref(false)
const statusLoading = ref(false)
const syncLoading = ref(false)

// 真实数据统计
const dataStats = ref({
  stockCount: 0,
  dailyDataCount: 0,
  minuteDataCount: 0,
  onlineDataSources: 0,
  totalDataSources: 0,
  lastUpdate: ''
})

// 真实数据源状态
const datasourceStatus = ref<DataSourceStatus[]>([])
const datasourceConfigs = ref<DataSourceConfig[]>([])
const syncTasks = ref<SyncTask[]>([])

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 配置对话框相关
const showConfigDialogFlag = ref(false)
const currentConfig = ref<DataSourceConfig | null>(null)

// 加载数据统计
const loadDataStats = async () => {
  try {
    const stats = await datasourceService.getDataStats()
    dataStats.value = {
      ...stats,
      lastUpdate: dayjs(stats.lastUpdate).format('YYYY-MM-DD HH:mm:ss')
    }
  } catch (error) {
    console.error('加载数据统计失败:', error)
    ElMessage.error('加载数据统计失败')
  }
}

// 加载数据源状态
const loadDataSourceStatus = async () => {
  try {
    statusLoading.value = true
    const [statuses, configs] = await Promise.all([
      datasourceService.getDataSourceStatuses(),
      datasourceService.getDataSourceConfigs()
    ])

    datasourceStatus.value = statuses.map(status => ({
      ...status,
      lastCheckTime: dayjs(status.lastCheckTime).format('YYYY-MM-DD HH:mm:ss'),
      lastSyncTime: status.lastSyncTime ? dayjs(status.lastSyncTime).format('YYYY-MM-DD HH:mm:ss') : '从未同步'
    }))

    datasourceConfigs.value = configs
  } catch (error) {
    console.error('加载数据源状态失败:', error)
    ElMessage.error('加载数据源状态失败')
  } finally {
    statusLoading.value = false
  }
}

// 加载同步任务
const loadSyncTasks = async () => {
  try {
    const tasks = await datasourceService.getSyncTasks()
    syncTasks.value = tasks.map(task => ({
      ...task,
      startTime: task.startTime ? dayjs(task.startTime).format('YYYY-MM-DD HH:mm:ss') : '',
      endTime: task.endTime ? dayjs(task.endTime).format('YYYY-MM-DD HH:mm:ss') : ''
    }))
  } catch (error) {
    console.error('加载同步任务失败:', error)
  }
}

// 刷新所有数据
const refreshAllData = async () => {
  await Promise.all([
    loadDataStats(),
    loadDataSourceStatus(),
    loadSyncTasks()
  ])
}

// 检查数据源状态
const checkDataSourceStatus = async (datasourceId?: string) => {
  try {
    statusLoading.value = true

    if (datasourceId) {
      // 检查单个数据源
      await datasourceService.checkDataSourceStatus(datasourceId)
      ElMessage.success('数据源状态检查完成')
    } else {
      // 检查所有数据源
      await datasourceService.checkAllDataSources()
      ElMessage.success('所有数据源状态检查完成')
    }

    // 重新加载状态
    await loadDataSourceStatus()
    await loadDataStats()
  } catch (error) {
    console.error('检查数据源状态失败:', error)
    ElMessage.error('检查数据源状态失败')
  } finally {
    statusLoading.value = false
  }
}

// 备份列表
const backupList = ref([
  {
    id: 1,
    name: 'backup_20240821_full.sql',
    size: '2.5GB',
    createTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
    type: 'full'
  },
  {
    id: 2,
    name: 'backup_20240820_incremental.sql',
    size: '150MB',
    createTime: dayjs().subtract(2, 'day').format('YYYY-MM-DD HH:mm:ss'),
    type: 'incremental'
  }
])

// 清理配置
const cleanupConfig = reactive({
  cleanLogs: true,
  logRetentionDays: 30,
  cleanTempFiles: true,
  cleanCache: false,
  cleanSessions: true
})

// 工具函数
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    online: '在线',
    offline: '离线',
    checking: '检查中',
    error: '错误'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    online: 'success',
    offline: 'danger',
    checking: 'warning',
    error: 'danger'
  }
  return statusMap[status] || 'info'
}

// 方法
const handleDataSync = async (datasourceId?: string, taskType: SyncTask['taskType'] = 'daily_data') => {
  try {
    const message = datasourceId
      ? `确定要同步 ${datasourceStatus.value.find(d => d.id === datasourceId)?.name} 的数据吗？`
      : '确定要同步所有数据源的数据吗？这可能需要一些时间。'

    await ElMessageBox.confirm(message, '数据同步', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    syncLoading.value = true

    if (datasourceId) {
      // 同步单个数据源
      const taskId = await datasourceService.startDataSync(datasourceId, taskType)
      ElMessage.success(`数据同步任务已启动，任务ID: ${taskId}`)
    } else {
      // 同步所有在线数据源
      const onlineDataSources = datasourceStatus.value.filter(d => d.status === 'online')
      const promises = onlineDataSources.map(d =>
        datasourceService.startDataSync(d.id, taskType)
      )

      await Promise.all(promises)
      ElMessage.success(`已启动 ${onlineDataSources.length} 个数据同步任务`)
    }

    // 重新加载同步任务和统计数据
    await Promise.all([loadSyncTasks(), loadDataStats()])
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('启动数据同步失败:', error)
      ElMessage.error('启动数据同步失败')
    }
  } finally {
    syncLoading.value = false
  }
}

// 配置管理方法
const showConfigDialog = (config?: DataSourceConfig) => {
  currentConfig.value = config || null
  showConfigDialogFlag.value = true
}

const editDataSource = (status: DataSourceStatus) => {
  const config = datasourceConfigs.value.find(c => c.id === status.id)
  if (config) {
    showConfigDialog(config)
  } else {
    ElMessage.error('找不到数据源配置')
  }
}

const deleteDataSource = async (status: DataSourceStatus) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除数据源 "${status.name}" 吗？此操作不可恢复。`,
      '删除数据源',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const success = await datasourceService.deleteDataSourceConfig(status.id)
    if (success) {
      ElMessage.success('数据源删除成功')
      await refreshAllData()
    } else {
      ElMessage.error('删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除数据源失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleDataBackup = () => {
  createBackup()
}

// 已删除旧的 syncDatasource 方法，使用新的 handleDataSync 方法

const createBackup = async () => {
  try {
    await ElMessageBox.confirm('创建备份可能需要较长时间，确定继续吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })
    
    backupLoading.value = true
    
    // 模拟备份过程
    setTimeout(() => {
      const newBackup = {
        id: Date.now(),
        name: `backup_${dayjs().format('YYYYMMDD_HHmmss')}_full.sql`,
        size: '2.8GB',
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        type: 'full'
      }
      
      backupList.value.unshift(newBackup)
      backupLoading.value = false
      ElMessage.success('备份创建成功')
    }, 3000)
  } catch {
    // 用户取消操作
  }
}

const refreshBackupList = () => {
  ElMessage.info('备份列表已刷新')
}

const downloadBackup = (backup: any) => {
  ElMessage.info(`正在下载备份文件: ${backup.name}`)
}

const restoreBackup = async (backup: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要恢复备份 ${backup.name} 吗？此操作将覆盖当前数据！`,
      '危险操作',
      {
        confirmButtonText: '确定恢复',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    ElMessage.success('备份恢复已开始')
  } catch {
    // 用户取消操作
  }
}

const deleteBackup = async (backup: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除备份 ${backup.name} 吗？`,
      '提示',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = backupList.value.findIndex(b => b.id === backup.id)
    if (index > -1) {
      backupList.value.splice(index, 1)
      ElMessage.success('备份已删除')
    }
  } catch {
    // 用户取消操作
  }
}

const executeCleanup = async () => {
  try {
    await ElMessageBox.confirm(
      '数据清理操作不可逆，确定要继续吗？',
      '危险操作',
      {
        confirmButtonText: '确定清理',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    cleanupLoading.value = true
    
    // 模拟清理过程
    setTimeout(() => {
      cleanupLoading.value = false
      ElMessage.success('数据清理完成')
    }, 2000)
  } catch {
    // 用户取消操作
  }
}

// 生命周期
onMounted(async () => {
  console.log('数据管理页面已加载')

  // 初始化加载所有数据
  await refreshAllData()

  // 设置定时刷新（每30秒刷新一次状态）
  refreshTimer = setInterval(async () => {
    await loadDataSourceStatus()
    await loadDataStats()
  }, 30000)
})

onUnmounted(() => {
  // 清理定时器
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})
</script>

<style lang="scss" scoped>
.data-management-container {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .card-title {
    font-size: 18px;
    font-weight: 600;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.data-content {
  :deep(.el-tabs__content) {
    padding: 20px;
  }
}

.data-overview {
  .overview-stats {
    margin-bottom: 30px;
  }
  
  .data-status {
    h3 {
      margin-bottom: 15px;
      color: var(--el-text-color-primary);
    }
  }
}

.backup-management {
  .backup-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }
}

.data-cleanup {
  .cleanup-options {
    margin-top: 20px;
    
    .form-tip {
      margin-left: 10px;
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }

  // 状态相关样式
  .success-rate-high {
    color: #67c23a;
    font-weight: 600;
  }

  .success-rate-low {
    color: #f56c6c;
    font-weight: 600;
  }

  .error-message {
    color: #f56c6c;
    font-size: 12px;
  }

  .success-message {
    color: #67c23a;
    font-size: 12px;
  }

  // 数据源状态表格样式
  .data-status {
    .el-table {
      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }

      .el-tag {
        &.el-tag--success {
          background-color: #f0f9ff;
          border-color: #67c23a;
          color: #67c23a;
        }

        &.el-tag--danger {
          background-color: #fef0f0;
          border-color: #f56c6c;
          color: #f56c6c;
        }

        &.el-tag--warning {
          background-color: #fdf6ec;
          border-color: #e6a23c;
          color: #e6a23c;
        }
      }
    }
  }
}
</style>
