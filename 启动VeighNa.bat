@echo off
chcp 65001 >nul
title VeighNa Studio 启动器

echo.
echo 🚀 VeighNa Studio 社区版启动器
echo ================================
echo.

:: 检查常见安装路径
echo 🔍 正在查找VeighNa Studio...

if exist "C:\Program Files\VeighNa Studio\VeighNa Studio.exe" (
    echo ✅ 找到: C:\Program Files\VeighNa Studio\
    start "" "C:\Program Files\VeighNa Studio\VeighNa Studio.exe"
    goto :success
)

if exist "C:\Program Files (x86)\VeighNa Studio\VeighNa Studio.exe" (
    echo ✅ 找到: C:\Program Files (x86)\VeighNa Studio\
    start "" "C:\Program Files (x86)\VeighNa Studio\VeighNa Studio.exe"
    goto :success
)

if exist "%USERPROFILE%\AppData\Local\VeighNa Studio\VeighNa Studio.exe" (
    echo ✅ 找到: %USERPROFILE%\AppData\Local\VeighNa Studio\
    start "" "%USERPROFILE%\AppData\Local\VeighNa Studio\VeighNa Studio.exe"
    goto :success
)

:: 尝试Python版本
echo.
echo 🐍 尝试Python版本...
if exist "run_vnpy_trader.py" (
    echo ✅ 找到Python启动脚本
    python run_vnpy_trader.py
    goto :success
)

if exist "启动VeighNa社区版.py" (
    echo ✅ 使用Python启动器
    python 启动VeighNa社区版.py
    goto :success
)

:: 都没找到
echo.
echo ❌ 未找到VeighNa Studio
echo.
echo 💡 请尝试以下方法:
echo    1. 按Win键搜索 "VeighNa Studio"
echo    2. 检查桌面是否有VeighNa图标
echo    3. 从官网下载: https://www.vnpy.com/
echo.
goto :end

:success
echo.
echo ✅ VeighNa Studio 启动成功!
echo.
echo 💡 使用提示:
echo    - 功能 → CTA回测 (推荐用于策略测试)
echo    - 功能 → CTA策略 (实时策略运行)
echo    - 功能 → 模拟账户 (虚拟资金交易)
echo.
timeout /t 3 >nul

:end
pause
