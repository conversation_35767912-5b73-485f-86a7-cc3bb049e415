# A股量化交易平台 - UI设计规范

## 🎨 设计原则

### 一致性原则
- 所有页面在用户登录前保持统一的视觉风格
- 只有在用户登录后进入主应用才允许主题切换
- 管理员系统使用独立的设计风格以示区分

## 🎯 色彩规范

### 主色调
- **主色**: `#409eff` (Element Plus 默认主色)
- **渐变背景**: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **成功色**: `#67c23a`
- **警告色**: `#e6a23c`
- **危险色**: `#f56c6c`
- **信息色**: `#909399`

### 管理员系统色调
- **主色**: `#f59e0b` (橙色，区别于普通用户)
- **渐变背景**: `linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)`
- **侧边栏**: `#1f2937` (深色主题)

## 📐 布局规范

### 登录注册页面
```scss
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.card {
  width: 100%;
  max-width: 400px; // 注册页面可适当增加到450px
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}
```

### Logo设计
```scss
.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
  
  .logo-icon {
    font-size: 32px;
    color: var(--el-color-primary);
  }
  
  .logo-text {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}
```

### 按钮规范
```scss
.primary-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  // 使用Element Plus默认样式，不自定义背景色
}
```

## 🔤 字体规范

### 字体大小
- **主标题**: 24px, font-weight: 600
- **副标题**: 14px, color: var(--el-text-color-regular)
- **正文**: 14px, color: var(--el-text-color-primary)
- **辅助文字**: 12-13px, color: var(--el-text-color-regular)

### 字体族
- 使用系统默认字体栈
- 中文优先使用系统中文字体

## 📱 响应式规范

### 断点设置
- **移动端**: < 768px
- **平板端**: 768px - 1200px  
- **桌面端**: > 1200px

### 移动端适配
```scss
@media (max-width: 480px) {
  .card {
    padding: 24px;
  }
  
  .logo-text {
    font-size: 20px;
  }
}
```

## 🎭 页面类型规范

### 1. 认证页面 (登录/注册/忘记密码)
- **背景**: 蓝紫渐变
- **主色**: Element Plus 默认蓝色
- **布局**: 居中卡片式
- **图标**: TrendCharts (趋势图标)

### 2. 管理员页面
- **背景**: 红橙渐变 (登录页)
- **主色**: 橙色 `#f59e0b`
- **布局**: 深色侧边栏 + 白色内容区
- **图标**: Setting (设置图标)

### 3. 用户主应用
- **主题**: 支持浅色/深色/自动切换
- **布局**: 侧边栏 + 顶部导航 + 内容区
- **主色**: 可通过主题切换

### 4. 错误页面
- **背景**: 与当前上下文保持一致
- **布局**: 居中展示
- **色调**: 中性色调

## 🧩 组件规范

### 表单组件
- **尺寸**: 统一使用 `large`
- **输入框**: 带前缀图标，支持清除
- **按钮**: 主要操作使用 `type="primary"`
- **验证**: 失焦时验证，实时反馈

### 卡片组件
- **圆角**: 12px
- **阴影**: `0 8px 32px rgba(0, 0, 0, 0.1)`
- **内边距**: 40px (移动端 24px)

### 图标使用
- **登录注册**: TrendCharts
- **管理员**: Setting
- **用户**: User
- **邮箱**: Message
- **密码**: Lock

## 📋 开发检查清单

### 新页面开发前
- [ ] 确认页面类型 (认证/管理员/用户主应用/错误)
- [ ] 选择对应的色彩方案
- [ ] 确认布局模式
- [ ] 选择合适的图标

### 开发过程中
- [ ] 使用统一的CSS变量
- [ ] 遵循响应式断点
- [ ] 保持组件尺寸一致
- [ ] 使用规范的字体大小

### 开发完成后
- [ ] 检查色彩是否符合规范
- [ ] 测试响应式效果
- [ ] 验证交互一致性
- [ ] 确认无障碍访问

## 🔄 主题切换说明

### 切换时机
- **登录前**: 不支持主题切换，统一使用默认主题
- **登录后**: 在用户主应用中支持主题切换
- **管理员**: 使用固定的管理员主题，不支持切换

### 切换范围
- 仅影响用户主应用内的页面
- 不影响登录、注册等认证页面
- 不影响管理员系统页面

## 📝 更新日志

### 2024-08-21
- 创建UI设计规范文档
- 统一登录注册页面设计风格
- 确立管理员系统独立设计风格
- 定义主题切换规则

---

> **重要提醒**: 所有开发人员在创建新页面前，请务必参考此规范文档，确保UI一致性。如需修改规范，请先讨论并更新此文档。
