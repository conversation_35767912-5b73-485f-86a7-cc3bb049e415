import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import App from './App.vue'
import router from './router'
import { useUserStore } from './shangdian/user'
import { useAppStore } from './shangdian/app'
import { useAdminStore } from './shangdian/admin'

// 导入全局样式
import './styles/index.scss'

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default'
})

// 初始化应用状态
const userStore = useUserStore()
const appStore = useAppStore()
const adminStore = useAdminStore()

// 初始化应用状态
const initializeApp = async () => {
  try {
    // 初始化应用设置
    appStore.initApp()

    // 初始化用户状态
    userStore.initUser()

    // 初始化管理员状态
    adminStore.initAdmin()

    // 验证管理员状态完整性
    const adminToken = localStorage.getItem('admin-token')
    const adminInfo = localStorage.getItem('admin-info')
    const adminPermissions = localStorage.getItem('admin-permissions')

    if (adminToken && adminInfo && adminPermissions) {
      try {
        const permissions = JSON.parse(adminPermissions)
        console.log('应用初始化：管理员权限已恢复', permissions)

        // 确保权限列表完整
        const requiredPermissions = [
          'user_management',
          'system_config',
          'data_management',
          'security_audit',
          'order_management',
          'strategy_management',
          'subscription_management',
          'finance_management'
        ]

        // 检查是否缺少权限
        const missingPermissions = requiredPermissions.filter(p => !permissions.includes(p))
        if (missingPermissions.length > 0) {
          console.warn('检测到缺少权限，正在补充:', missingPermissions)
          const updatedPermissions = [...new Set([...permissions, ...requiredPermissions])]
          adminStore.setAdminPermissions(updatedPermissions)
        }
      } catch (error) {
        console.error('管理员权限恢复失败:', error)
      }
    }

    console.log('✅ 应用状态初始化完成')
  } catch (error) {
    console.error('❌ 应用状态初始化失败:', error)
  }
}

// 执行初始化
initializeApp()

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  // 这里可以添加错误上报逻辑
}

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('全局警告:', msg, trace)
}

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)

  // 如果是组件加载失败，尝试重新加载
  if (error.message.includes('Loading chunk') || error.message.includes('Failed to fetch')) {
    console.log('检测到组件加载失败，尝试重新加载页面...')
    window.location.reload()
  }
})

// 处理未捕获的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('未捕获的Promise错误:', event.reason)

  // 如果是路由相关错误，不阻止默认行为
  if (event.reason?.message?.includes('Navigation')) {
    event.preventDefault()
  }
})

// 挂载应用
app.mount('#app')

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🚀 A股量化交易平台启动成功')
  console.log('📊 当前环境:', import.meta.env.MODE)
  console.log('🔗 API地址:', import.meta.env.VITE_API_BASE_URL || '/api')
}
