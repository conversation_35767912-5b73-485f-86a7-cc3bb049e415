<template>
  <el-dialog
    v-model="visible"
    title="策略详情"
    width="1200px"
    :before-close="handleClose"
  >
    <div class="strategy-detail-container" v-loading="loading">
      <div v-if="strategyDetail" class="strategy-content">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="basic-info">
              <el-row :gutter="20">
                <el-col :span="16">
                  <h2 class="strategy-title">{{ strategyDetail.name }}</h2>
                  <p class="strategy-description">{{ strategyDetail.description }}</p>

                  <div class="strategy-meta">
                    <el-tag class="meta-tag">{{ strategyDetail.category }}</el-tag>
                    <el-tag
                      :type="getTradeTypeTagType(strategyDetail.trade_type)"
                      class="meta-tag"
                    >
                      {{ getTradeTypeText(strategyDetail.trade_type) }}
                    </el-tag>
                    <el-tag
                      v-for="tag in strategyDetail.tags"
                      :key="tag"
                      class="meta-tag"
                      type="info"
                      size="small"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>

                  <!-- 上架资格检查 -->
                  <div v-if="strategyDetail.status === 'pending'" class="eligibility-check">
                    <h4>上架资格检查</h4>
                    <div v-if="eligibilityResult" class="check-results">
                      <div v-if="eligibilityResult.eligible" class="check-pass">
                        <el-icon class="check-icon"><SuccessFilled /></el-icon>
                        <span>该策略符合所有上架条件</span>
                      </div>
                      <div v-else class="check-fail">
                        <el-icon class="check-icon"><CircleCloseFilled /></el-icon>
                        <span>该策略不符合上架条件：</span>
                        <ul>
                          <li v-for="reason in eligibilityResult.reasons" :key="reason">
                            {{ reason }}
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </el-col>

                <el-col :span="8">
                  <div class="strategy-stats">
                    <div class="stat-item">
                      <span class="stat-label">价格</span>
                      <div class="price-info">
                        <span class="current-price">¥{{ strategyDetail.price }}</span>
                        <span v-if="strategyDetail.original_price" class="original-price">
                          ¥{{ strategyDetail.original_price }}
                        </span>
                      </div>
                    </div>

                    <div class="stat-item">
                      <span class="stat-label">销量</span>
                      <span class="stat-value">{{ strategyDetail.sales_count }}</span>
                    </div>

                    <div class="stat-item">
                      <span class="stat-label">评分</span>
                      <div class="rating-info">
                        <el-rate
                          v-model="strategyDetail.rating"
                          disabled
                          show-score
                          text-color="#ff9900"
                          score-template="{value}"
                        />
                      </div>
                    </div>

                    <div class="stat-item">
                      <span class="stat-label">状态</span>
                      <el-tag :type="getStatusTagType(strategyDetail.status)">
                        {{ getStatusText(strategyDetail.status) }}
                      </el-tag>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 作者信息 -->
            <div class="author-info">
              <h3>作者信息</h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <label>作者ID：</label>
                    <span>{{ strategyDetail.creator_id }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>作者名称：</label>
                    <span>{{ strategyDetail.creator_name }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 时间信息 -->
            <div class="time-info">
              <h3>时间信息</h3>
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="info-item">
                    <label>创建时间：</label>
                    <span>{{ formatDateTime(strategyDetail.create_time) }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <label>提交审核：</label>
                    <span>{{ strategyDetail.submit_time ? formatDateTime(strategyDetail.submit_time) : '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <label>上架时间：</label>
                    <span>{{ strategyDetail.online_time ? formatDateTime(strategyDetail.online_time) : '-' }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <!-- 策略表现 -->
          <el-tab-pane label="策略表现" name="performance">
            <div class="performance-content">
              <!-- 关键指标 -->
              <div class="key-metrics">
                <el-row :gutter="16">
                  <el-col :span="6">
                    <div class="metric-item">
                      <div class="metric-value positive">{{ strategyDetail.total_return.toFixed(1) }}%</div>
                      <div class="metric-label">总收益率</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="metric-item">
                      <div class="metric-value positive">{{ strategyDetail.annual_return.toFixed(1) }}%</div>
                      <div class="metric-label">年化收益率</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="metric-item">
                      <div class="metric-value negative">{{ strategyDetail.max_drawdown.toFixed(1) }}%</div>
                      <div class="metric-label">最大回撤</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="metric-item">
                      <div class="metric-value">{{ strategyDetail.sharpe_ratio.toFixed(2) }}</div>
                      <div class="metric-label">夏普比率</div>
                    </div>
                  </el-col>
                </el-row>

                <el-row :gutter="16" style="margin-top: 16px;">
                  <el-col :span="6">
                    <div class="metric-item">
                      <div class="metric-value">{{ strategyDetail.win_rate.toFixed(1) }}%</div>
                      <div class="metric-label">胜率</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="metric-item">
                      <div class="metric-value">{{ strategyDetail.trade_count }}</div>
                      <div class="metric-label">交易次数</div>
                    </div>
                  </el-col>
                  <el-col :span="6" v-if="strategyDetail.online_return !== undefined">
                    <div class="metric-item">
                      <div class="metric-value positive">{{ strategyDetail.online_return.toFixed(1) }}%</div>
                      <div class="metric-label">上架后收益</div>
                    </div>
                  </el-col>
                  <el-col :span="6" v-if="strategyDetail.online_max_drawdown !== undefined">
                    <div class="metric-item">
                      <div class="metric-value negative">{{ strategyDetail.online_max_drawdown.toFixed(1) }}%</div>
                      <div class="metric-label">上架后回撤</div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 收益曲线图 -->
              <div class="performance-chart">
                <h4>收益曲线</h4>
                <div class="chart-placeholder">
                  <p>图表功能开发中...</p>
                </div>
                <!-- 暂时注释掉图表组件
                <v-chart
                  :option="performanceChartOption"
                  :loading="chartLoading"
                  class="chart"
                  autoresize
                />
                -->
              </div>
            </div>
          </el-tab-pane>

          <!-- 交易记录 -->
          <el-tab-pane label="交易记录" name="trades">
            <div class="trades-content">
              <p>交易记录功能开发中...</p>
            </div>
          </el-tab-pane>

          <!-- 购买记录 -->
          <el-tab-pane label="购买记录" name="purchases">
            <div class="purchases-content">
              <p>购买记录功能开发中...</p>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="strategyDetail?.status === 'pending'"
          type="success"
          @click="handleApprove"
          :disabled="!eligibilityResult?.eligible"
        >
          审核通过
        </el-button>
        <el-button
          v-if="strategyDetail?.status === 'pending'"
          type="danger"
          @click="handleReject"
        >
          拒绝上架
        </el-button>
        <el-button
          v-if="strategyDetail?.status === 'active'"
          type="warning"
          @click="handleOffline"
        >
          下架策略
        </el-button>
        <el-button
          v-if="strategyDetail?.status === 'inactive'"
          type="success"
          @click="handleOnline"
        >
          重新上架
        </el-button>
        <el-button
          v-if="['draft', 'rejected'].includes(strategyDetail?.status)"
          type="danger"
          @click="handleDelete"
        >
          删除策略
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { strategyService } from '@/fuwu/strategyService'
// 暂时注释掉ECharts相关导入，避免可能的错误
// import { use } from 'echarts/core'
// import { LineChart } from 'echarts/charts'
// import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
// import { CanvasRenderer } from 'echarts/renderers'
// import VChart from 'vue-echarts'

// 注册ECharts组件
// use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

// Props
interface Props {
  modelValue: boolean
  strategyId?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  strategyId: ''
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const chartLoading = ref(false)
const activeTab = ref('basic')
const strategyDetail = ref<any>(null)
const eligibilityResult = ref<any>(null)
const strategyTrades = ref<any[]>([])
const strategyPurchases = ref<any[]>([])
const performanceData = ref<any[]>([])

// 计算属性
const winningTrades = computed(() => {
  return strategyTrades.value.filter(trade => trade.profit && trade.profit > 0).length
})

const losingTrades = computed(() => {
  return strategyTrades.value.filter(trade => trade.profit && trade.profit < 0).length
})

const totalRevenue = computed(() => {
  return strategyPurchases.value.reduce((sum, purchase) => sum + purchase.purchase_price, 0)
})

const profitablePurchases = computed(() => {
  return strategyPurchases.value.filter(purchase => purchase.purchase_return && purchase.purchase_return > 0).length
})

const averagePurchaseReturn = computed(() => {
  const validReturns = strategyPurchases.value.filter(p => p.purchase_return !== undefined)
  if (validReturns.length === 0) return 0
  return validReturns.reduce((sum, p) => sum + p.purchase_return, 0) / validReturns.length
})

// 收益图表配置 - 暂时注释掉
/*
const performanceChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const data = params[0]
      return `${data.name}<br/>累计收益率: ${data.value.toFixed(2)}%`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: performanceData.value.map(item => dayjs(item.date).format('MM-DD')),
    axisLine: {
      lineStyle: {
        color: '#e4e7ed'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => `${value.toFixed(1)}%`
    },
    axisLine: {
      lineStyle: {
        color: '#e4e7ed'
      }
    },
    splitLine: {
      lineStyle: {
        color: '#f5f7fa'
      }
    }
  },
  series: [
    {
      name: '累计收益率',
      type: 'line',
      data: performanceData.value.map(item => item.cumulative_return),
      smooth: true,
      lineStyle: {
        color: '#67c23a',
        width: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.05)' }
          ]
        }
      }
    }
  ]
}))
*/

// 工具函数
const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending: '待审核',
    active: '已上架',
    inactive: '已下架',
    rejected: '已拒绝'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'info',
    pending: 'warning',
    active: 'success',
    inactive: 'info',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

const getTradeTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    simulation: '模拟盘',
    real: '实盘',
    both: '双盘'
  }
  return typeMap[type] || '未知'
}

const getTradeTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    simulation: 'info',
    real: 'success',
    both: 'primary'
  }
  return typeMap[type] || 'info'
}

const getPurchaseStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '有效',
    expired: '已过期',
    refunded: '已退款'
  }
  return statusMap[status] || '未知'
}

const getPurchaseStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    expired: 'warning',
    refunded: 'danger'
  }
  return statusMap[status] || 'info'
}

// 方法
const loadStrategyDetail = async () => {
  if (!props.strategyId) return

  try {
    loading.value = true

    // 加载策略基本信息
    const detail = await strategyService.getStrategyDetail(props.strategyId)
    strategyDetail.value = detail

    if (!detail) {
      ElMessage.error('策略不存在')
      return
    }

    // 如果是待审核状态，检查上架资格
    if (detail.status === 'pending') {
      eligibilityResult.value = await strategyService.checkListingEligibility(props.strategyId)
    }

    // 加载策略表现数据
    chartLoading.value = true
    const performance = await strategyService.getStrategyPerformance(props.strategyId)
    performanceData.value = performance
    chartLoading.value = false

    // 加载交易记录
    const trades = await strategyService.getStrategyTrades(props.strategyId)
    strategyTrades.value = trades

    // 加载购买记录
    const purchases = await strategyService.getStrategyPurchases(props.strategyId)
    strategyPurchases.value = purchases

  } catch (error) {
    console.error('加载策略详情失败:', error)
    ElMessage.error('加载策略详情失败')
  } finally {
    loading.value = false
    chartLoading.value = false
  }
}

const handleApprove = async () => {
  if (!strategyDetail.value) return

  try {
    // 先检查上架资格
    if (!eligibilityResult.value?.eligible) {
      ElMessage.error('策略不符合上架条件，无法审核通过')
      return
    }

    await ElMessageBox.confirm(
      `确定要审核通过策略 "${strategyDetail.value.name}" 吗？`,
      '审核策略',
      {
        confirmButtonText: '审核通过',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    const success = await strategyService.approveStrategy(strategyDetail.value.id, true)
    if (success) {
      strategyDetail.value.status = 'active'
      strategyDetail.value.approve_time = new Date().toISOString()
      strategyDetail.value.online_time = new Date().toISOString()
      ElMessage.success('策略审核通过，已自动上架')
      emit('refresh')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('审核策略失败:', error)
      ElMessage.error('审核策略失败')
    }
  }
}

const handleReject = async () => {
  if (!strategyDetail.value) return

  try {
    const { value: reason } = await ElMessageBox.prompt(
      `请输入拒绝理由：`,
      '拒绝策略',
      {
        confirmButtonText: '确定拒绝',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入拒绝理由...'
      }
    )

    const success = await strategyService.approveStrategy(strategyDetail.value.id, false, reason)
    if (success) {
      strategyDetail.value.status = 'rejected'
      ElMessage.success('策略已拒绝')
      emit('refresh')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('拒绝策略失败:', error)
      ElMessage.error('拒绝策略失败')
    }
  }
}

const handleOnline = async () => {
  if (!strategyDetail.value) return

  try {
    await ElMessageBox.confirm(
      `确定要重新上架策略 "${strategyDetail.value.name}" 吗？`,
      '上架策略',
      {
        confirmButtonText: '确定上架',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    strategyDetail.value.status = 'active'
    ElMessage.success('策略已上架')
    emit('refresh')
  } catch {
    // 用户取消操作
  }
}

const handleOffline = async () => {
  if (!strategyDetail.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要下架策略 "${strategyDetail.value.name}" 吗？`,
      '下架策略',
      {
        confirmButtonText: '确定下架',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    strategyDetail.value.status = 'inactive'
    ElMessage.success('策略已下架')
    emit('refresh')
  } catch {
    // 用户取消操作
  }
}

const handleDelete = async () => {
  if (!strategyDetail.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除策略 "${strategyDetail.value.name}" 吗？此操作不可恢复！`,
      '删除策略',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    strategyDetail.value.status = 'deleted'
    ElMessage.success('策略已删除')
    emit('refresh')
    handleClose()
  } catch {
    // 用户取消操作
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(() => props.strategyId, (newStrategyId) => {
  if (newStrategyId && visible.value) {
    loadStrategyDetail()
  }
})

watch(visible, (newVisible) => {
  if (newVisible && props.strategyId) {
    loadStrategyDetail()
  }
})
</script>

<style lang="scss" scoped>
.strategy-detail-container {
  min-height: 600px;

  :deep(.el-tabs__content) {
    padding: 20px;
  }

  .strategy-content {
    .basic-info,
    .performance-content,
    .trades-content,
    .purchases-content {
      > div {
        margin-bottom: 24px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        h3, h4 {
          margin: 0 0 16px 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          border-bottom: 1px solid var(--el-border-color-lighter);
          padding-bottom: 8px;
        }
      }
    }
  }

  .basic-info {
    .strategy-title {
      margin: 0 0 12px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .strategy-description {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: var(--el-text-color-regular);
      line-height: 1.6;
    }

    .strategy-meta {
      .meta-tag {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    .eligibility-check {
      margin-top: 20px;

      h4 {
        margin-bottom: 12px;
        font-size: 14px;
        color: var(--el-text-color-primary);
      }

      .check-results {
        .check-pass {
          display: flex;
          align-items: center;
          color: #67c23a;
          font-weight: 500;

          .check-icon {
            margin-right: 8px;
            font-size: 16px;
          }
        }

        .check-fail {
          color: #f56c6c;

          .check-icon {
            margin-right: 8px;
            font-size: 16px;
          }

          ul {
            margin: 8px 0 0 24px;
            padding: 0;

            li {
              margin-bottom: 4px;
            }
          }
        }
      }
    }

    .strategy-stats {
      .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding: 12px;
        background: white;
        border-radius: 6px;

        &:last-child {
          margin-bottom: 0;
        }

        .stat-label {
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        .stat-value {
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .price-info {
          .current-price {
            font-size: 18px;
            font-weight: 600;
            color: #f56c6c;
            margin-right: 8px;
          }

          .original-price {
            font-size: 14px;
            color: var(--el-text-color-placeholder);
            text-decoration: line-through;
          }
        }

        .rating-info {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  // 关键指标样式
  .key-metrics {
    .metric-item {
      text-align: center;
      padding: 16px;
      background: white;
      border-radius: 6px;

      .metric-value {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 4px;

        &.positive {
          color: #67c23a;
        }

        &.negative {
          color: #f56c6c;
        }
      }

      .metric-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }

  // 图表样式
  .performance-chart {
    .chart {
      width: 100%;
      height: 300px;
    }
  }

  // 交易和购买记录样式
  .trades-summary,
  .purchases-summary {
    margin-bottom: 20px;

    .summary-item {
      text-align: center;
      padding: 12px;
      background: white;
      border-radius: 6px;

      .summary-value {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;

        &.positive {
          color: #67c23a;
        }

        &.negative {
          color: #f56c6c;
        }
      }

      .summary-label {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
  }

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      font-weight: 500;
      color: var(--el-text-color-regular);
      min-width: 100px;
      margin-right: 8px;
    }

    span {
      color: var(--el-text-color-primary);
    }
  }

  .sales-data {
    .sales-chart {
      .sales-item {
        text-align: center;
        padding: 16px;
        background: white;
        border-radius: 6px;

        .sales-value {
          font-size: 20px;
          font-weight: 600;
          color: var(--el-color-primary);
          margin-bottom: 4px;
        }

        .sales-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

  // 通用样式
  .profit-positive {
    color: #67c23a;
    font-weight: 500;
  }

  .profit-negative {
    color: #f56c6c;
    font-weight: 500;
  }

  .drawdown-text {
    color: #e6a23c;
    font-weight: 500;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
