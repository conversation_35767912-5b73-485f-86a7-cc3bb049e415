<template>
  <el-dialog
    v-model="visible"
    title="策略详情"
    width="900px"
    :before-close="handleClose"
  >
    <div class="strategy-detail-container" v-loading="loading">
      <div v-if="strategyDetail" class="strategy-content">
        <!-- 基本信息 -->
        <div class="basic-info">
          <el-row :gutter="20">
            <el-col :span="16">
              <h2 class="strategy-title">{{ strategyDetail.name }}</h2>
              <p class="strategy-description">{{ strategyDetail.description }}</p>
              
              <div class="strategy-meta">
                <el-tag class="meta-tag">{{ strategyDetail.category }}</el-tag>
                <el-tag 
                  v-for="tag in strategyDetail.tags" 
                  :key="tag" 
                  class="meta-tag"
                  type="info"
                  size="small"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="strategy-stats">
                <div class="stat-item">
                  <span class="stat-label">价格</span>
                  <div class="price-info">
                    <span class="current-price">¥{{ strategyDetail.price }}</span>
                    <span v-if="strategyDetail.original_price" class="original-price">
                      ¥{{ strategyDetail.original_price }}
                    </span>
                  </div>
                </div>
                
                <div class="stat-item">
                  <span class="stat-label">销量</span>
                  <span class="stat-value">{{ strategyDetail.sales_count }}</span>
                </div>
                
                <div class="stat-item">
                  <span class="stat-label">评分</span>
                  <div class="rating-info">
                    <el-rate
                      v-model="strategyDetail.rating"
                      disabled
                      show-score
                      text-color="#ff9900"
                      score-template="{value}"
                    />
                  </div>
                </div>
                
                <div class="stat-item">
                  <span class="stat-label">状态</span>
                  <el-tag :type="getStatusTagType(strategyDetail.status)">
                    {{ getStatusText(strategyDetail.status) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 作者信息 -->
        <div class="author-info">
          <h3>作者信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>作者ID：</label>
                <span>{{ strategyDetail.creator_id }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>作者名称：</label>
                <span>{{ strategyDetail.creator_name }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 时间信息 -->
        <div class="time-info">
          <h3>时间信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatDateTime(strategyDetail.create_time) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>更新时间：</label>
                <span>{{ formatDateTime(strategyDetail.update_time) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 销售数据 -->
        <div class="sales-data">
          <h3>销售数据</h3>
          <div class="sales-chart">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="sales-item">
                  <div class="sales-value">{{ strategyDetail.sales_count }}</div>
                  <div class="sales-label">总销量</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="sales-item">
                  <div class="sales-value">¥{{ (strategyDetail.price * strategyDetail.sales_count).toLocaleString() }}</div>
                  <div class="sales-label">总销售额</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="sales-item">
                  <div class="sales-value">¥{{ strategyDetail.price }}</div>
                  <div class="sales-label">单价</div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        
        <!-- 最近购买记录 -->
        <div class="recent-purchases">
          <h3>最近购买记录</h3>
          <el-table :data="recentPurchases" style="width: 100%">
            <el-table-column prop="user_name" label="购买用户" width="120" />
            <el-table-column prop="purchase_time" label="购买时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.purchase_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="支付金额" width="100">
              <template #default="{ row }">
                ¥{{ row.amount.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="payment_method" label="支付方式" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'paid' ? 'success' : 'warning'" size="small">
                  {{ row.status === 'paid' ? '已支付' : '待支付' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="strategyDetail?.status === 'active'"
          type="warning" 
          @click="handleOffline"
        >
          下架策略
        </el-button>
        <el-button 
          v-if="strategyDetail?.status === 'inactive'"
          type="success" 
          @click="handleOnline"
        >
          上架策略
        </el-button>
        <el-button 
          type="danger" 
          @click="handleDelete"
        >
          删除策略
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { orderService } from '@/fuwu/orderService'

// Props
interface Props {
  modelValue: boolean
  strategyId?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  strategyId: ''
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const strategyDetail = ref<any>(null)

// 模拟最近购买记录
const recentPurchases = ref([
  {
    user_name: 'user001',
    purchase_time: '2024-08-20T15:30:00Z',
    amount: 299,
    payment_method: '支付宝',
    status: 'paid'
  },
  {
    user_name: 'user002',
    purchase_time: '2024-08-19T10:20:00Z',
    amount: 299,
    payment_method: '微信支付',
    status: 'paid'
  },
  {
    user_name: 'user003',
    purchase_time: '2024-08-18T14:15:00Z',
    amount: 299,
    payment_method: '余额支付',
    status: 'paid'
  }
])

// 工具函数
const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '上架中',
    inactive: '已下架',
    deleted: '已删除'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    inactive: 'warning',
    deleted: 'danger'
  }
  return statusMap[status] || 'info'
}

// 方法
const loadStrategyDetail = async () => {
  if (!props.strategyId) return
  
  try {
    loading.value = true
    const products = await orderService.getStrategyProducts()
    strategyDetail.value = products.find(p => p.id === props.strategyId)
  } catch (error) {
    console.error('加载策略详情失败:', error)
    ElMessage.error('加载策略详情失败')
  } finally {
    loading.value = false
  }
}

const handleOnline = async () => {
  if (!strategyDetail.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要上架策略 "${strategyDetail.value.name}" 吗？`,
      '上架策略',
      {
        confirmButtonText: '确定上架',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    strategyDetail.value.status = 'active'
    ElMessage.success('策略已上架')
    emit('refresh')
  } catch {
    // 用户取消操作
  }
}

const handleOffline = async () => {
  if (!strategyDetail.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要下架策略 "${strategyDetail.value.name}" 吗？`,
      '下架策略',
      {
        confirmButtonText: '确定下架',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    strategyDetail.value.status = 'inactive'
    ElMessage.success('策略已下架')
    emit('refresh')
  } catch {
    // 用户取消操作
  }
}

const handleDelete = async () => {
  if (!strategyDetail.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除策略 "${strategyDetail.value.name}" 吗？此操作不可恢复！`,
      '删除策略',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    strategyDetail.value.status = 'deleted'
    ElMessage.success('策略已删除')
    emit('refresh')
    handleClose()
  } catch {
    // 用户取消操作
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(() => props.strategyId, (newStrategyId) => {
  if (newStrategyId && visible.value) {
    loadStrategyDetail()
  }
})

watch(visible, (newVisible) => {
  if (newVisible && props.strategyId) {
    loadStrategyDetail()
  }
})
</script>

<style lang="scss" scoped>
.strategy-detail-container {
  min-height: 500px;

  .strategy-content {
    > div {
      margin-bottom: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }
    }
  }

  .basic-info {
    .strategy-title {
      margin: 0 0 12px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .strategy-description {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: var(--el-text-color-regular);
      line-height: 1.6;
    }

    .strategy-meta {
      .meta-tag {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    .strategy-stats {
      .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding: 12px;
        background: white;
        border-radius: 6px;

        &:last-child {
          margin-bottom: 0;
        }

        .stat-label {
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        .stat-value {
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .price-info {
          .current-price {
            font-size: 18px;
            font-weight: 600;
            color: #f56c6c;
            margin-right: 8px;
          }

          .original-price {
            font-size: 14px;
            color: var(--el-text-color-placeholder);
            text-decoration: line-through;
          }
        }

        .rating-info {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      font-weight: 500;
      color: var(--el-text-color-regular);
      min-width: 100px;
      margin-right: 8px;
    }

    span {
      color: var(--el-text-color-primary);
    }
  }

  .sales-data {
    .sales-chart {
      .sales-item {
        text-align: center;
        padding: 16px;
        background: white;
        border-radius: 6px;

        .sales-value {
          font-size: 20px;
          font-weight: 600;
          color: var(--el-color-primary);
          margin-bottom: 4px;
        }

        .sales-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
