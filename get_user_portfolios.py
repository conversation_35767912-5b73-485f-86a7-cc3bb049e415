#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取用户个人雪球组合
连接到用户的模拟盈亏组合并刷新持仓
"""

import json
import logging
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserPortfolioManager:
    """用户组合管理器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.user_cubes = []
        self.load_token()
    
    def load_token(self):
        """加载token"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ Token设置成功: u={u}")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 加载token失败: {e}")
            return False
    
    def get_user_cubes(self):
        """获取用户创建的组合"""
        try:
            logger.info("🔍 获取用户组合列表...")
            result = ball.watch_list()
            
            if result and 'data' in result:
                data = result['data']
                
                # 获取用户创建的组合
                cubes = data.get('cubes', [])
                
                logger.info(f"✅ 找到 {len(cubes)} 个组合:")
                
                user_cubes = []
                for cube in cubes:
                    cube_info = {
                        'id': cube.get('id'),
                        'name': cube.get('name', 'N/A'),
                        'symbol': f"ZH{cube.get('id', 0):06d}" if cube.get('id') else 'N/A',
                        'symbol_count': cube.get('symbol_count', 0),
                        'created_at': cube.get('created_at', 0),
                        'updated_at': cube.get('updated_at', 0)
                    }
                    user_cubes.append(cube_info)
                    
                    # 格式化时间
                    created_time = datetime.fromtimestamp(cube_info['created_at']/1000).strftime('%Y-%m-%d %H:%M') if cube_info['created_at'] else 'N/A'
                    updated_time = datetime.fromtimestamp(cube_info['updated_at']/1000).strftime('%Y-%m-%d %H:%M') if cube_info['updated_at'] else 'N/A'
                    
                    logger.info(f"   📊 组合: {cube_info['name']}")
                    logger.info(f"      ID: {cube_info['symbol']}")
                    logger.info(f"      股票数: {cube_info['symbol_count']}")
                    logger.info(f"      创建时间: {created_time}")
                    logger.info(f"      更新时间: {updated_time}")
                    logger.info("-" * 40)
                
                self.user_cubes = user_cubes
                return user_cubes
            else:
                logger.warning("⚠️ 没有找到组合数据")
                return []
                
        except Exception as e:
            logger.error(f"❌ 获取用户组合失败: {e}")
            return []
    
    def get_cube_holdings(self, cube_symbol):
        """获取组合持仓"""
        try:
            logger.info(f"📋 获取组合 {cube_symbol} 的持仓...")
            
            result = ball.rebalancing_current(cube_symbol)
            
            if result:
                last_rb = result.get('last_rb', {})
                holdings = last_rb.get('holdings', [])
                
                logger.info(f"✅ 组合 {cube_symbol} 持仓信息:")
                logger.info(f"   持仓股票数: {len(holdings)}")
                
                if holdings:
                    logger.info("   详细持仓:")
                    total_weight = 0
                    for i, holding in enumerate(holdings, 1):
                        stock_symbol = holding.get('stock_symbol', 'N/A')
                        stock_name = holding.get('stock_name', 'N/A')
                        weight = holding.get('weight', 0)
                        volume = holding.get('volume', 0)
                        
                        total_weight += weight
                        
                        logger.info(f"     {i}. {stock_name} ({stock_symbol})")
                        logger.info(f"        权重: {weight:.2f}%")
                        logger.info(f"        数量: {volume}")
                    
                    logger.info(f"   总权重: {total_weight:.2f}%")
                    logger.info(f"   现金比例: {100 - total_weight:.2f}%")
                else:
                    logger.info("   📝 组合为空，无持仓")
                
                return holdings
            else:
                logger.warning(f"⚠️ 无法获取组合 {cube_symbol} 的持仓数据")
                return []
                
        except Exception as e:
            logger.error(f"❌ 获取组合持仓失败: {e}")
            return []
    
    def get_cube_performance(self, cube_symbol):
        """获取组合表现"""
        try:
            logger.info(f"📈 获取组合 {cube_symbol} 的表现...")
            
            # 获取实时净值
            quote_result = ball.quote_current(cube_symbol)
            
            if quote_result and cube_symbol in quote_result:
                quote_data = quote_result[cube_symbol]
                
                logger.info(f"✅ 组合 {cube_symbol} 表现:")
                logger.info(f"   组合名称: {quote_data.get('name', 'N/A')}")
                logger.info(f"   当前净值: {quote_data.get('net_value', 'N/A')}")
                logger.info(f"   日收益率: {quote_data.get('daily_gain', 'N/A')}%")
                logger.info(f"   总收益率: {quote_data.get('total_gain', 'N/A')}%")
                logger.info(f"   年化收益率: {quote_data.get('annualized_gain', 'N/A')}%")
                
                return quote_data
            else:
                logger.warning(f"⚠️ 无法获取组合 {cube_symbol} 的表现数据")
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取组合表现失败: {e}")
            return None
    
    def refresh_target_portfolios(self, target_names=['测试', 'test']):
        """刷新目标组合的持仓"""
        logger.info(f"🎯 刷新目标组合: {target_names}")
        logger.info("=" * 60)
        
        # 获取用户组合
        cubes = self.get_user_cubes()
        
        if not cubes:
            logger.error("❌ 没有找到用户组合")
            return
        
        # 查找目标组合
        target_cubes = []
        for cube in cubes:
            if cube['name'] in target_names:
                target_cubes.append(cube)
        
        if not target_cubes:
            logger.warning(f"⚠️ 没有找到名为 {target_names} 的组合")
            logger.info("📋 可用组合列表:")
            for cube in cubes:
                logger.info(f"   - {cube['name']} ({cube['symbol']})")
            return
        
        # 刷新每个目标组合
        for cube in target_cubes:
            logger.info(f"\n🔄 刷新组合: {cube['name']} ({cube['symbol']})")
            logger.info("=" * 50)
            
            # 获取持仓
            holdings = self.get_cube_holdings(cube['symbol'])
            
            # 获取表现
            performance = self.get_cube_performance(cube['symbol'])
            
            logger.info("✅ 刷新完成")
            logger.info("=" * 50)
    
    def get_all_portfolios_summary(self):
        """获取所有组合的摘要信息"""
        logger.info("📊 获取所有组合摘要")
        logger.info("=" * 60)
        
        cubes = self.get_user_cubes()
        
        if not cubes:
            logger.error("❌ 没有找到用户组合")
            return
        
        summary = {
            'total_cubes': len(cubes),
            'cubes_detail': []
        }
        
        for cube in cubes:
            logger.info(f"\n📊 组合: {cube['name']} ({cube['symbol']})")
            
            # 获取持仓数量
            holdings = self.get_cube_holdings(cube['symbol'])
            
            # 获取表现
            performance = self.get_cube_performance(cube['symbol'])
            
            cube_summary = {
                'name': cube['name'],
                'symbol': cube['symbol'],
                'holdings_count': len(holdings),
                'performance': performance
            }
            
            summary['cubes_detail'].append(cube_summary)
        
        logger.info(f"\n📋 总结:")
        logger.info(f"   总组合数: {summary['total_cubes']}")
        for detail in summary['cubes_detail']:
            perf = detail['performance']
            total_gain = perf.get('total_gain', 'N/A') if perf else 'N/A'
            logger.info(f"   {detail['name']}: {detail['holdings_count']}只股票, 收益率: {total_gain}%")
        
        return summary

def main():
    """主函数"""
    print("🚀 雪球用户组合管理器")
    print("连接到用户的模拟盈亏组合并刷新持仓")
    print("=" * 60)
    
    manager = UserPortfolioManager()
    
    # 刷新目标组合（"测试"和"test"）
    manager.refresh_target_portfolios(['测试', 'test'])
    
    print("\n" + "=" * 60)
    print("🎉 刷新完成！")

if __name__ == '__main__':
    main()
