<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <el-icon class="logo-icon"><TrendCharts /></el-icon>
          <h1 class="logo-text">A股量化交易平台</h1>
        </div>
        <p class="subtitle">专业的量化投资工具</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
      >
        <el-form-item prop="yonghuming">
          <el-input
            v-model="loginForm.yonghuming"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="mima">
          <el-input
            v-model="loginForm.mima"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.jizhu_denglu">
              记住登录
            </el-checkbox>
            <el-link type="primary" :underline="false">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>

        <el-form-item>
          <div class="register-link">
            还没有账号？
            <el-link type="primary" @click="$router.push('/register')">
              立即注册
            </el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <div class="admin-link">
            <el-link type="danger" @click="$router.push('/numendavid中国/login')">
              <el-icon><Lock /></el-icon>
              管理员登录
            </el-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { LoginForm } from '@/types/user'
import { useUserStore } from '@/shangdian/user'
import { userService } from '@/fuwu/userService'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const loginForm = reactive<LoginForm>({
  yonghuming: '',
  mima: '',
  jizhu_denglu: false
})

// 表单验证规则
const loginRules: FormRules = {
  yonghuming: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  mima: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 从注册页面跳转时预填用户名
onMounted(() => {
  if (route.query.username && typeof route.query.username === 'string') {
    loginForm.yonghuming = route.query.username
  }
})

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    loading.value = true

    // 调用用户登录服务
    const result = await userService.login({
      yonghuming: loginForm.yonghuming,
      mima: loginForm.mima
    })

    if (!result.success) {
      ElMessage.error(result.message)
      return
    }

    if (!result.user) {
      ElMessage.error('登录失败，用户信息获取失败')
      return
    }

    // 设置用户登录状态
    const token = 'jwt-token-' + Date.now()
    const userInfo = {
      id: result.user.id,
      yonghuming: result.user.yonghuming,
      youxiang: result.user.youxiang,
      dingyue_leixing: result.user.dingyue_leixing,
      dingyue_kaishi: result.user.dingyue_kaishi,
      dingyue_jieshu: result.user.dingyue_jieshu,
      chuangjian_shijian: result.user.chuangjian_shijian,
      gengxin_shijian: result.user.gengxin_shijian,
      shi_huoyue: result.user.shi_huoyue
    }

    // 更新用户状态
    userStore.setToken(token)
    userStore.setUserInfo(userInfo)

    // 根据订阅类型设置权限
    let permissions: string[] = []
    switch (result.user.dingyue_leixing) {
      case 'zhuce':
        permissions = ['dashboard', 'fund-pool'] // 注册用户版：仪表板 + 资金池观摩
        break
      case 'jiben':
        permissions = ['dashboard', 'strategy', 'backtest', 'trading'] // 基础版
        break
      case 'gaoji':
        permissions = ['dashboard', 'strategy', 'backtest', 'trading', 'data', 'settings'] // 高级版
        break
      default:
        permissions = ['dashboard']
    }

    userStore.setPermissions(permissions)

    ElMessage.success(`欢迎回来，${result.user.yonghuming}！`)

    // 跳转到仪表板
    await router.push('/dashboard')
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;

    .logo-icon {
      font-size: 32px;
      color: var(--el-color-primary);
    }

    .logo-text {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
    }
  }

  .subtitle {
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin: 0;
  }
}

.login-form {
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .login-button {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
  }

  .register-link,
  .admin-link {
    text-align: center;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  .admin-link {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--el-border-color-extra-light);
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-card {
    padding: 24px;
  }

  .login-header .logo .logo-text {
    font-size: 20px;
  }
}
</style>