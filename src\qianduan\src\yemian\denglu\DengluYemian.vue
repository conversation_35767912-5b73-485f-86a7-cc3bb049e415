<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <el-icon class="logo-icon"><TrendCharts /></el-icon>
          <h1 class="logo-text">A股量化交易平台</h1>
        </div>
        <p class="subtitle">专业的量化投资工具</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
      >
        <el-form-item prop="yonghuming">
          <el-input
            v-model="loginForm.yonghuming"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="mima">
          <el-input
            v-model="loginForm.mima"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.jizhu_denglu">
              记住登录
            </el-checkbox>
            <el-link type="primary" :underline="false">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>

        <el-form-item>
          <div class="register-link">
            还没有账号？
            <el-link type="primary" @click="$router.push('/register')">
              立即注册
            </el-link>
          </div>
        </el-form-item>
      </el-form>

      <!-- 测试账号提示 -->
      <el-card class="test-accounts" shadow="never">
        <template #header>
          <div class="test-header">
            <el-icon><InfoFilled /></el-icon>
            <span>测试账号</span>
          </div>
        </template>
        <div class="account-list">
          <div class="account-item">
            <strong>管理员账号:</strong> admin / admin123 (高级版)
          </div>
          <div class="account-item">
            <strong>普通用户:</strong> user / user123 (基础版)
          </div>
          <div class="account-item">
            <strong>测试账号:</strong> test / test123 (基础版)
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { LoginForm } from '@/types/user'
import { useUserStore } from '@/shangdian/user'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const loginForm = reactive<LoginForm>({
  yonghuming: 'admin',
  mima: 'admin123',
  jizhu_denglu: false
})

// 表单验证规则
const loginRules: FormRules = {
  yonghuming: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  mima: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 预设的测试账号
const validAccounts = [
  { yonghuming: 'admin', mima: 'admin123', dingyue_leixing: 'gaoji' as const },
  { yonghuming: 'user', mima: 'user123', dingyue_leixing: 'jiben' as const },
  { yonghuming: 'test', mima: 'test123', dingyue_leixing: 'jiben' as const }
]

// 验证账号密码
const validateCredentials = (yonghuming: string, mima: string) => {
  return validAccounts.find(account =>
    account.yonghuming === yonghuming && account.mima === mima
  )
}

// 登录处理
const handleLogin = async () => {
  console.log('🔐 开始登录流程')

  if (!loginFormRef.value) {
    console.error('❌ 表单引用不存在')
    return
  }

  try {
    console.log('📝 验证表单数据')
    const valid = await loginFormRef.value.validate()
    if (!valid) {
      console.log('❌ 表单验证失败')
      return
    }

    loading.value = true
    console.log('⏳ 开始验证账号密码')

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    // 验证账号密码
    const validAccount = validateCredentials(loginForm.yonghuming, loginForm.mima)

    if (!validAccount) {
      console.log('❌ 账号密码验证失败')
      ElMessage.error('用户名或密码错误')
      return
    }

    console.log('✅ 账号密码验证成功')

    // 设置用户登录状态
    const mockToken = 'mock-jwt-token-' + Date.now()
    const mockUserInfo = {
      id: 1,
      yonghuming: validAccount.yonghuming,
      youxiang: `${validAccount.yonghuming}@example.com`,
      dingyue_leixing: validAccount.dingyue_leixing,
      dingyue_kaishi: '2024-01-01',
      dingyue_jieshu: '2024-12-31',
      chuangjian_shijian: '2024-01-01T00:00:00Z',
      gengxin_shijian: new Date().toISOString(),
      shi_huoyue: true
    }

    console.log('👤 设置用户状态:', { token: mockToken, userInfo: mockUserInfo })

    // 更新用户状态
    userStore.setToken(mockToken)
    userStore.setUserInfo(mockUserInfo)

    // 根据订阅类型设置权限
    const permissions = validAccount.dingyue_leixing === 'gaoji'
      ? ['dashboard', 'strategy', 'backtest', 'trading', 'data', 'settings']
      : ['dashboard', 'strategy', 'backtest', 'trading']

    userStore.setPermissions(permissions)

    console.log('✅ 用户状态已更新')
    console.log('🔍 当前登录状态:', userStore.isLoggedIn)
    console.log('👤 当前用户信息:', userStore.userInfo)

    ElMessage.success(`欢迎回来，${validAccount.yonghuming}！`)

    console.log('🚀 准备跳转到仪表板')
    // 跳转到仪表板
    await router.push('/dashboard')
    console.log('✅ 路由跳转完成')
  } catch (error) {
    console.error('❌ 登录失败:', error)
    ElMessage.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
    console.log('🏁 登录流程结束')
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;

    .logo-icon {
      font-size: 32px;
      color: var(--el-color-primary);
    }

    .logo-text {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
    }
  }

  .subtitle {
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin: 0;
  }
}

.login-form {
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .login-button {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
  }

  .register-link {
    text-align: center;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
}

.test-accounts {
  margin-top: 24px;
  border: 1px solid var(--el-border-color-lighter);

  .test-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-color-primary);
  }

  .account-list {
    .account-item {
      padding: 8px 0;
      font-size: 13px;
      color: var(--el-text-color-regular);
      border-bottom: 1px solid var(--el-border-color-extra-light);

      &:last-child {
        border-bottom: none;
      }

      strong {
        color: var(--el-text-color-primary);
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-card {
    padding: 24px;
  }

  .login-header .logo .logo-text {
    font-size: 20px;
  }

  .test-accounts {
    margin-top: 16px;

    .account-list .account-item {
      font-size: 12px;
    }
  }
}
</style>