<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <el-icon class="logo-icon"><TrendCharts /></el-icon>
          <h1 class="logo-text">A股量化交易平台</h1>
        </div>
        <p class="subtitle">专业的量化投资工具</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
      >
        <el-form-item prop="yonghuming">
          <el-input
            v-model="loginForm.yonghuming"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="mima">
          <el-input
            v-model="loginForm.mima"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.jizhu_denglu">
              记住登录
            </el-checkbox>
            <el-link type="primary" :underline="false">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>

        <el-form-item>
          <div class="register-link">
            还没有账号？
            <el-link type="primary" @click="$router.push('/register')">
              立即注册
            </el-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { LoginForm } from '@/types/user'
import { useUserStore } from '@/shangdian/user'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const loginForm = reactive<LoginForm>({
  yonghuming: '',
  mima: '',
  jizhu_denglu: false
})

// 表单验证规则
const loginRules: FormRules = {
  yonghuming: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  mima: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 管理员账号配置 (生产环境应该从环境变量或配置文件读取)
const ADMIN_CREDENTIALS = {
  yonghuming: 'admin',
  mima: 'admin123',
  dingyue_leixing: 'gaoji' as const
}

// 验证账号密码
const validateCredentials = (yonghuming: string, mima: string) => {
  if (yonghuming === ADMIN_CREDENTIALS.yonghuming && mima === ADMIN_CREDENTIALS.mima) {
    return ADMIN_CREDENTIALS
  }
  return null
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    loading.value = true

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 验证账号密码
    const validAccount = validateCredentials(loginForm.yonghuming, loginForm.mima)

    if (!validAccount) {
      ElMessage.error('用户名或密码错误')
      return
    }

    // 设置用户登录状态
    const token = 'jwt-token-' + Date.now()
    const userInfo = {
      id: 1,
      yonghuming: validAccount.yonghuming,
      youxiang: `${validAccount.yonghuming}@quantitative-trading.com`,
      dingyue_leixing: validAccount.dingyue_leixing,
      dingyue_kaishi: '2024-01-01',
      dingyue_jieshu: '2024-12-31',
      chuangjian_shijian: '2024-01-01T00:00:00Z',
      gengxin_shijian: new Date().toISOString(),
      shi_huoyue: true
    }

    // 更新用户状态
    userStore.setToken(token)
    userStore.setUserInfo(userInfo)
    userStore.setPermissions(['dashboard', 'strategy', 'backtest', 'trading', 'data', 'settings', 'admin'])

    ElMessage.success('登录成功')

    // 跳转到仪表板
    await router.push('/dashboard')
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;

    .logo-icon {
      font-size: 32px;
      color: var(--el-color-primary);
    }

    .logo-text {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
    }
  }

  .subtitle {
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin: 0;
  }
}

.login-form {
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .login-button {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
  }

  .register-link {
    text-align: center;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-card {
    padding: 24px;
  }

  .login-header .logo .logo-text {
    font-size: 20px;
  }
}
</style>