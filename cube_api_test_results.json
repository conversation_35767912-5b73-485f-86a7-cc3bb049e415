{"timestamp": "2025-08-18T14:58:54.609941", "test_results": {"组合列表": {"status": "success", "data": {"cubes": [{"id": -120, "name": "全部", "order_id": -50, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -27, "name": "沪深", "order_id": -40, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -26, "name": "港股", "order_id": -30, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -25, "name": "美股", "order_id": -20, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -24, "name": "我的", "order_id": -10, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -23, "name": "基金", "order_id": -7, "category": 3, "include": false, "symbol_count": 0, "type": -1}], "funds": [{"id": -110, "name": "全部", "order_id": -30, "category": 2, "include": false, "symbol_count": 0, "type": -1}, {"id": -17, "name": "基金", "order_id": -20, "category": 2, "include": false, "symbol_count": 0, "type": -1}, {"id": -16, "name": "私募", "order_id": -10, "category": 2, "include": false, "symbol_count": 0, "type": -1}], "stocks": [{"id": -10, "name": "实盘", "order_id": -60, "category": 1, "include": false, "symbol_count": 0, "type": -1}, {"id": -1, "name": "全部", "order_id": -50, "category": 1, "include": false, "symbol_count": 0, "type": -1}, {"id": -5, "name": "沪深", "order_id": -40, "category": 1, "include": false, "symbol_count": 0, "type": -1}, {"id": -7, "name": "港股", "order_id": -30, "category": 1, "include": false, "symbol_count": 0, "type": -1}, {"id": -6, "name": "美股", "order_id": -20, "category": 1, "include": false, "symbol_count": 0, "type": -1}, {"id": -4, "name": "模拟", "order_id": -10, "category": 1, "include": false, "symbol_count": 0, "type": -1}]}, "cubes": [{"id": -120, "name": "全部", "order_id": -50, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -27, "name": "沪深", "order_id": -40, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -26, "name": "港股", "order_id": -30, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -25, "name": "美股", "order_id": -20, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -24, "name": "我的", "order_id": -10, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -23, "name": "基金", "order_id": -7, "category": 3, "include": false, "symbol_count": 0, "type": -1}]}, "组合持仓-ZH000001": {"status": "failed", "error": "b'{\"error_description\":\"\\xe8\\xaf\\xa5\\xe7\\xbb\\x84\\xe5\\x90\\x88\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8\",\"error_uri\":\"/cubes/rebalancing/current.json\",\"error_code\":\"20809\"}'"}, "调仓历史-ZH000001": {"status": "failed", "error": "b'{\"error_description\":\"\\xe8\\xaf\\xa5\\xe7\\xbb\\x84\\xe5\\x90\\x88\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8\",\"error_uri\":\"/cubes/rebalancing/history.json\",\"error_code\":\"20809\"}'"}, "组合净值-ZH000001": {"status": "failed", "error": "b'{\"error_description\":\"\\xe8\\xaf\\xa5\\xe7\\xbb\\x84\\xe5\\x90\\x88\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8\",\"error_uri\":\"/cubes/nav_daily/all.json\",\"error_code\":\"20809\"}'"}, "实时净值-ZH000001": {"status": "failed", "error": "b'{\"error_description\":\"\\xe8\\xaf\\xa5\\xe7\\xbb\\x84\\xe5\\x90\\x88\\xe4\\xb8\\x8d\\xe5\\xad\\x98\\xe5\\x9c\\xa8\",\"error_uri\":\"/cubes/quote.json\",\"error_code\":\"20809\"}'"}}, "summary": {"total_tests": 5, "successful_tests": 1, "failed_tests": 4}}