# VeighNa量化交易系统

## 🎯 系统介绍

VeighNa是一个基于Python的开源量化交易平台开发框架，支持：

- 📊 **A股交易**: 中泰XTP、华鑫奇点、东方证券、东方财富等
- 📈 **期货交易**: CTP、CTP Mini等
- 🎯 **期权交易**: ETF期权、股票期权
- 🤖 **策略开发**: CTA策略、组合策略、算法交易
- 📋 **数据管理**: 历史数据下载、实时数据记录
- ⚠️ **风险管理**: 实时风控监控
- 🌐 **Web交易**: 浏览器交易界面

## 🚀 快速启动

### 方法1: 使用批处理文件 (推荐)
```bash
双击 start_vnpy.bat
```

### 方法2: 命令行启动
```bash
python run_vnpy_trader.py
```

## 📦 安装额外模块

运行模块安装器来添加更多功能：

```bash
python install_vnpy_modules.py
```

这将安装：
- ✅ **策略应用**: CTA策略、组合策略、算法交易等
- ✅ **交易接口**: A股、期货、期权接口
- ✅ **数据源**: 米筐、TuShare、Wind等
- ✅ **数据库**: SQLite、MySQL、PostgreSQL等

## 🔧 系统配置

### 1. 交易接口配置
1. 启动VeighNa后，点击菜单 `系统` -> `连接管理`
2. 选择要使用的交易接口（如CTP、XTP等）
3. 填入账户信息和服务器地址
4. 点击连接

### 2. 数据源配置
1. 点击菜单 `系统` -> `数据源`
2. 选择数据源类型（如RQData、TuShare等）
3. 填入API密钥或账户信息
4. 测试连接

### 3. 数据库配置
1. 点击菜单 `系统` -> `数据库`
2. 选择数据库类型（SQLite、MySQL等）
3. 配置连接参数
4. 初始化数据库

## 📊 主要功能

### 1. CTA策略 📈
- **策略开发**: 基于模板快速开发趋势策略
- **策略回测**: K线和Tick级别回测
- **参数优化**: 多进程和遗传算法优化
- **实盘交易**: 自动化策略执行

### 2. 组合策略 📊
- **多标的交易**: 同时交易多个合约
- **Alpha策略**: 股票多因子策略
- **套利策略**: 跨品种套利
- **风险管理**: 组合风险控制

### 3. 算法交易 🤖
- **TWAP**: 时间加权平均价格算法
- **Sniper**: 狙击手算法
- **Iceberg**: 冰山算法
- **BestLimit**: 最优限价算法

### 4. 数据管理 💾
- **历史数据**: 下载和管理历史K线数据
- **实时数据**: 记录实时Tick和K线数据
- **数据查看**: 图形化数据浏览和分析

### 5. 风险管理 ⚠️
- **交易流控**: 限制下单频率
- **资金管理**: 控制单笔和总持仓
- **止损止盈**: 自动风险控制
- **实时监控**: 风险指标实时监控

## 🎯 A股模拟交易

### 使用模拟账户
1. 安装模拟账户模块: `vnpy-paperaccount`
2. 在VeighNa中启用"模拟账户"应用
3. 配置初始资金和手续费
4. 开始模拟交易

### 连接券商模拟环境
1. 选择支持的券商接口（XTP、TORA等）
2. 申请券商模拟账户
3. 配置模拟环境服务器地址
4. 连接并开始交易

## 📚 学习资源

- **官方网站**: https://www.vnpy.com
- **使用文档**: https://www.vnpy.com/docs/
- **社区论坛**: https://www.vnpy.com/forum/
- **Github**: https://github.com/vnpy/vnpy
- **示例代码**: ./examples/ 目录

## 🔍 常见问题

### Q: 如何添加新的交易接口？
A: 运行 `python install_vnpy_modules.py` 安装接口模块，然后在系统菜单中配置。

### Q: 如何开发自己的策略？
A: 参考 `examples/` 目录下的示例策略，基于CTA或组合策略模板开发。

### Q: 如何获取历史数据？
A: 配置数据源（如RQData），然后使用数据管理模块下载历史数据。

### Q: 如何进行策略回测？
A: 使用CTA回测或组合回测模块，加载历史数据进行回测分析。

## ⚠️ 风险提示

1. **模拟交易**: 建议先使用模拟账户充分测试
2. **策略验证**: 策略上线前必须经过充分回测验证
3. **风险控制**: 设置合理的止损和仓位管理
4. **资金安全**: 妥善保管账户信息和API密钥

## 📞 技术支持

- **官方论坛**: https://www.vnpy.com/forum/
- **QQ群**: 564385877
- **微信群**: 添加微信 vnpy_helper

## 📄 许可证

MIT License - 开源免费使用
