// 订阅类型管理服务 - 超级管理员专用

export interface SubscriptionType {
  id: string
  name: string
  displayName: string
  description: string
  price: number
  originalPrice?: number
  duration: number // 天数
  features: string[]
  limitations: {
    maxStrategies: number
    maxBacktestDays: number
    maxRealTradingAccounts: number
    dataRefreshInterval: number // 分钟
    supportLevel: 'basic' | 'standard' | 'premium' | 'vip'
  }
  isActive: boolean
  isRecommended: boolean
  sortOrder: number
  createTime: string
  updateTime: string
}

export interface UserSubscription {
  id: string
  userId: string
  userName: string
  subscriptionTypeId: string
  subscriptionTypeName: string
  startDate: string
  endDate: string
  status: 'active' | 'expired' | 'cancelled' | 'pending'
  paymentStatus: 'paid' | 'pending' | 'failed' | 'refunded'
  paymentAmount: number
  paymentMethod: string
  autoRenew: boolean
  createTime: string
  updateTime: string
}

export interface SubscriptionStats {
  totalSubscriptions: number
  activeSubscriptions: number
  expiredSubscriptions: number
  totalRevenue: number
  monthlyRevenue: number
  conversionRate: number
  churnRate: number
  averageLifetime: number
}

// 订阅类型管理器
class SubscriptionManager {
  private subscriptionTypes: Map<string, SubscriptionType> = new Map()
  private userSubscriptions: Map<string, UserSubscription> = new Map()

  constructor() {
    this.initializeDefaultTypes()
  }

  private initializeDefaultTypes() {
    // 从localStorage加载或使用默认配置
    const savedTypes = localStorage.getItem('subscription_types')
    
    const defaultTypes: SubscriptionType[] = [
      {
        id: 'free',
        name: 'free',
        displayName: '免费版',
        description: '基础功能，适合初学者',
        price: 0,
        duration: 30,
        features: [
          '基础策略回测',
          '5个策略额度',
          '30天历史数据',
          '社区支持'
        ],
        limitations: {
          maxStrategies: 5,
          maxBacktestDays: 30,
          maxRealTradingAccounts: 0,
          dataRefreshInterval: 60,
          supportLevel: 'basic'
        },
        isActive: true,
        isRecommended: false,
        sortOrder: 1,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        id: 'basic',
        name: 'basic',
        displayName: '基础版',
        description: '适合个人投资者',
        price: 99,
        originalPrice: 129,
        duration: 30,
        features: [
          '完整策略回测',
          '20个策略额度',
          '1年历史数据',
          '实时数据推送',
          '邮件支持'
        ],
        limitations: {
          maxStrategies: 20,
          maxBacktestDays: 365,
          maxRealTradingAccounts: 1,
          dataRefreshInterval: 15,
          supportLevel: 'standard'
        },
        isActive: true,
        isRecommended: true,
        sortOrder: 2,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        id: 'professional',
        name: 'professional',
        displayName: '专业版',
        description: '适合专业交易者',
        price: 299,
        originalPrice: 399,
        duration: 30,
        features: [
          '高级策略回测',
          '100个策略额度',
          '5年历史数据',
          '实时数据推送',
          '多账户管理',
          '专属客服',
          '策略分享'
        ],
        limitations: {
          maxStrategies: 100,
          maxBacktestDays: 1825,
          maxRealTradingAccounts: 5,
          dataRefreshInterval: 5,
          supportLevel: 'premium'
        },
        isActive: true,
        isRecommended: false,
        sortOrder: 3,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        id: 'enterprise',
        name: 'enterprise',
        displayName: '企业版',
        description: '适合机构和团队',
        price: 999,
        originalPrice: 1299,
        duration: 30,
        features: [
          '无限策略回测',
          '无限策略额度',
          '完整历史数据',
          '实时数据推送',
          '无限账户管理',
          '7x24专属客服',
          '策略商店',
          'API接口',
          '定制开发'
        ],
        limitations: {
          maxStrategies: -1, // -1表示无限制
          maxBacktestDays: -1,
          maxRealTradingAccounts: -1,
          dataRefreshInterval: 1,
          supportLevel: 'vip'
        },
        isActive: true,
        isRecommended: false,
        sortOrder: 4,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
    ]

    const types = savedTypes ? JSON.parse(savedTypes) : defaultTypes
    
    types.forEach((type: SubscriptionType) => {
      this.subscriptionTypes.set(type.id, type)
    })

    this.saveTypes()
    this.initializeMockUserSubscriptions()
  }

  private initializeMockUserSubscriptions() {
    // 模拟一些用户订阅数据
    const mockSubscriptions: UserSubscription[] = [
      {
        id: 'sub_001',
        userId: 'user_001',
        userName: '张三',
        subscriptionTypeId: 'basic',
        subscriptionTypeName: '基础版',
        startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'active',
        paymentStatus: 'paid',
        paymentAmount: 99,
        paymentMethod: '支付宝',
        autoRenew: true,
        createTime: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        id: 'sub_002',
        userId: 'user_002',
        userName: '李四',
        subscriptionTypeId: 'professional',
        subscriptionTypeName: '专业版',
        startDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'expired',
        paymentStatus: 'paid',
        paymentAmount: 299,
        paymentMethod: '微信支付',
        autoRenew: false,
        createTime: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        id: 'sub_003',
        userId: 'user_003',
        userName: '王五',
        subscriptionTypeId: 'enterprise',
        subscriptionTypeName: '企业版',
        startDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'active',
        paymentStatus: 'paid',
        paymentAmount: 999,
        paymentMethod: '银行转账',
        autoRenew: true,
        createTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updateTime: new Date().toISOString()
      }
    ]

    mockSubscriptions.forEach(sub => {
      this.userSubscriptions.set(sub.id, sub)
    })
  }

  private saveTypes() {
    const types = Array.from(this.subscriptionTypes.values())
    localStorage.setItem('subscription_types', JSON.stringify(types))
  }

  private saveSubscriptions() {
    const subscriptions = Array.from(this.userSubscriptions.values())
    localStorage.setItem('user_subscriptions', JSON.stringify(subscriptions))
  }

  // 获取所有订阅类型
  getSubscriptionTypes(): SubscriptionType[] {
    return Array.from(this.subscriptionTypes.values()).sort((a, b) => a.sortOrder - b.sortOrder)
  }

  // 获取单个订阅类型
  getSubscriptionType(id: string): SubscriptionType | null {
    return this.subscriptionTypes.get(id) || null
  }

  // 添加订阅类型
  addSubscriptionType(type: Omit<SubscriptionType, 'id' | 'createTime' | 'updateTime'>): string {
    const id = `sub_type_${Date.now()}`
    const newType: SubscriptionType = {
      ...type,
      id,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    
    this.subscriptionTypes.set(id, newType)
    this.saveTypes()
    
    return id
  }

  // 更新订阅类型
  updateSubscriptionType(id: string, updates: Partial<SubscriptionType>): boolean {
    const existingType = this.subscriptionTypes.get(id)
    if (!existingType) {
      return false
    }

    const updatedType: SubscriptionType = {
      ...existingType,
      ...updates,
      id, // 确保ID不被修改
      updateTime: new Date().toISOString()
    }

    this.subscriptionTypes.set(id, updatedType)
    this.saveTypes()
    
    return true
  }

  // 删除订阅类型
  deleteSubscriptionType(id: string): boolean {
    // 检查是否有用户正在使用此订阅类型
    const hasActiveUsers = Array.from(this.userSubscriptions.values())
      .some(sub => sub.subscriptionTypeId === id && sub.status === 'active')
    
    if (hasActiveUsers) {
      throw new Error('无法删除正在使用的订阅类型')
    }

    const deleted = this.subscriptionTypes.delete(id)
    if (deleted) {
      this.saveTypes()
    }
    
    return deleted
  }

  // 获取用户订阅列表
  getUserSubscriptions(): UserSubscription[] {
    return Array.from(this.userSubscriptions.values())
  }

  // 获取订阅统计
  getSubscriptionStats(): SubscriptionStats {
    const subscriptions = Array.from(this.userSubscriptions.values())
    const now = new Date()
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
    
    const totalSubscriptions = subscriptions.length
    const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active').length
    const expiredSubscriptions = subscriptions.filter(sub => sub.status === 'expired').length
    
    const totalRevenue = subscriptions
      .filter(sub => sub.paymentStatus === 'paid')
      .reduce((sum, sub) => sum + sub.paymentAmount, 0)
    
    const monthlyRevenue = subscriptions
      .filter(sub => 
        sub.paymentStatus === 'paid' && 
        new Date(sub.createTime) >= monthStart
      )
      .reduce((sum, sub) => sum + sub.paymentAmount, 0)
    
    // 简化的转化率和流失率计算
    const conversionRate = totalSubscriptions > 0 ? (activeSubscriptions / totalSubscriptions) * 100 : 0
    const churnRate = totalSubscriptions > 0 ? (expiredSubscriptions / totalSubscriptions) * 100 : 0
    
    // 平均生命周期（天）
    const averageLifetime = subscriptions.length > 0 
      ? subscriptions.reduce((sum, sub) => {
          const start = new Date(sub.startDate)
          const end = sub.status === 'active' ? now : new Date(sub.endDate)
          return sum + (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
        }, 0) / subscriptions.length
      : 0

    return {
      totalSubscriptions,
      activeSubscriptions,
      expiredSubscriptions,
      totalRevenue,
      monthlyRevenue,
      conversionRate,
      churnRate,
      averageLifetime
    }
  }

  // 更新用户订阅状态
  updateUserSubscriptionStatus(subscriptionId: string, status: UserSubscription['status']): boolean {
    const subscription = this.userSubscriptions.get(subscriptionId)
    if (!subscription) {
      return false
    }

    subscription.status = status
    subscription.updateTime = new Date().toISOString()
    
    this.saveSubscriptions()
    return true
  }

  // 为用户创建订阅
  createUserSubscription(userId: string, userName: string, subscriptionTypeId: string): string {
    const subscriptionType = this.subscriptionTypes.get(subscriptionTypeId)
    if (!subscriptionType) {
      throw new Error('订阅类型不存在')
    }

    const id = `sub_${Date.now()}`
    const startDate = new Date()
    const endDate = new Date(startDate.getTime() + subscriptionType.duration * 24 * 60 * 60 * 1000)

    const newSubscription: UserSubscription = {
      id,
      userId,
      userName,
      subscriptionTypeId,
      subscriptionTypeName: subscriptionType.displayName,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      status: 'pending',
      paymentStatus: 'pending',
      paymentAmount: subscriptionType.price,
      paymentMethod: '',
      autoRenew: false,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }

    this.userSubscriptions.set(id, newSubscription)
    this.saveSubscriptions()

    return id
  }
}

// 创建全局订阅管理器实例
const subscriptionManager = new SubscriptionManager()

// 导出服务接口
export const subscriptionService = {
  // 订阅类型管理
  getSubscriptionTypes: async (): Promise<SubscriptionType[]> => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return subscriptionManager.getSubscriptionTypes()
  },

  getSubscriptionType: async (id: string): Promise<SubscriptionType | null> => {
    await new Promise(resolve => setTimeout(resolve, 100))
    return subscriptionManager.getSubscriptionType(id)
  },

  addSubscriptionType: async (type: Omit<SubscriptionType, 'id' | 'createTime' | 'updateTime'>): Promise<string> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return subscriptionManager.addSubscriptionType(type)
  },

  updateSubscriptionType: async (id: string, updates: Partial<SubscriptionType>): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return subscriptionManager.updateSubscriptionType(id, updates)
  },

  deleteSubscriptionType: async (id: string): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return subscriptionManager.deleteSubscriptionType(id)
  },

  // 用户订阅管理
  getUserSubscriptions: async (): Promise<UserSubscription[]> => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return subscriptionManager.getUserSubscriptions()
  },

  getSubscriptionStats: async (): Promise<SubscriptionStats> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return subscriptionManager.getSubscriptionStats()
  },

  updateUserSubscriptionStatus: async (subscriptionId: string, status: UserSubscription['status']): Promise<boolean> => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return subscriptionManager.updateUserSubscriptionStatus(subscriptionId, status)
  },

  createUserSubscription: async (userId: string, userName: string, subscriptionTypeId: string): Promise<string> => {
    await new Promise(resolve => setTimeout(resolve, 400))
    return subscriptionManager.createUserSubscription(userId, userName, subscriptionTypeId)
  }
}

export default subscriptionService
