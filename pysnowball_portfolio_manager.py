#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于pysnowball的正确雪球组合管理方案
参考: https://github.com/uname-yang/pysnowball
"""

import json
import requests
import logging
from flask import Flask, render_template, request, jsonify

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

class PySnowballPortfolioManager:
    def __init__(self):
        self.token = None
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.cookies = {}
        self.load_token()
    
    def load_token(self):
        """从配置文件加载token"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.cookies[key] = value
            
            # 设置token格式 (pysnowball需要的格式)
            xq_a_token = self.cookies.get('xq_a_token', '')
            u = self.cookies.get('u', '')
            if xq_a_token and u:
                self.token = f"xq_a_token={xq_a_token};u={u}"
                logging.info("Token加载成功")
            else:
                logging.error("Token格式不正确")
                
        except Exception as e:
            logging.error(f"加载token失败: {e}")
    
    def set_token(self, token):
        """设置token (pysnowball格式)"""
        self.token = token
        # 解析token到cookies
        for cookie in token.split(';'):
            if '=' in cookie:
                key, value = cookie.strip().split('=', 1)
                self.cookies[key] = value
    
    def fetch_api(self, url, host="xueqiu.com"):
        """通用API请求方法 (模仿pysnowball的utls.fetch)"""
        try:
            full_url = f"https://{host}/{url}" if not url.startswith('http') else url
            
            response = requests.get(
                full_url,
                headers=self.headers,
                cookies=self.cookies,
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logging.error(f"API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logging.error(f"API请求异常: {e}")
            return None
    
    def get_watch_list(self):
        """获取自选股列表 (相当于pysnowball.watch_list)"""
        url = "v5/stock/portfolio/list.json?system=true"
        return self.fetch_api(url)
    
    def get_watch_stock(self, portfolio_id):
        """获取自选股详情 (相当于pysnowball.watch_stock)"""
        url = f"v5/stock/portfolio/stock/list.json?size=1000&category=1&pid={portfolio_id}"
        return self.fetch_api(url)
    
    def get_cube_nav_daily(self, cube_symbol):
        """获取组合净值 (相当于pysnowball.nav_daily)"""
        url = f"cubes/nav_daily/all.json?cube_symbol={cube_symbol}"
        return self.fetch_api(url)
    
    def get_cube_rebalancing_current(self, cube_symbol):
        """获取组合当前持仓 (相当于pysnowball.rebalancing_current)"""
        url = f"cubes/rebalancing/current.json?cube_symbol={cube_symbol}"
        return self.fetch_api(url)
    
    def get_cube_quote_current(self, cube_symbol):
        """获取组合实时净值 (相当于pysnowball.quote_current)"""
        url = f"cubes/quote.json?code={cube_symbol}"
        return self.fetch_api(url)
    
    def search_cubes(self, user_id=None):
        """搜索用户的组合"""
        if not user_id:
            user_id = self.cookies.get('u', '')
        
        if not user_id:
            return None
        
        # 尝试获取用户组合列表
        url = f"cubes/list.json?user_id={user_id}"
        return self.fetch_api(url)
    
    def create_portfolio_with_pysnowball(self):
        """使用pysnowball创建组合的指导"""
        return {
            'method': 'pysnowball',
            'steps': [
                '安装pysnowball: pip install pysnowball',
                '获取雪球token (xq_a_token和u)',
                '设置token: ball.set_token("xq_a_token=xxx;u=xxx")',
                '获取组合列表: ball.watch_list()',
                '获取组合详情: ball.watch_stock(portfolio_id)',
                '获取组合净值: ball.nav_daily(cube_symbol)',
                '获取组合持仓: ball.rebalancing_current(cube_symbol)'
            ],
            'example_code': '''
import pysnowball as ball

# 设置token
ball.set_token("xq_a_token=your_token;u=your_user_id")

# 获取自选股列表
portfolios = ball.watch_list()

# 获取组合净值
nav_data = ball.nav_daily("ZH123456")

# 获取组合持仓
holdings = ball.rebalancing_current("ZH123456")
            '''
        }
    
    def test_connection(self):
        """测试连接"""
        try:
            # 测试获取自选股列表
            result = self.get_watch_list()
            if result:
                return True, f"连接成功，找到 {len(result.get('list', []))} 个投资组合"
            else:
                return False, "连接失败，无法获取数据"
        except Exception as e:
            return False, f"连接测试失败: {str(e)}"

# 创建管理器实例
portfolio_manager = PySnowballPortfolioManager()

@app.route('/')
def index():
    """主页"""
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PySnowball 雪球组合管理器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); overflow: hidden; }
        .header { background: linear-gradient(45deg, #FF6B6B, #4ECDC4); color: white; padding: 20px; text-align: center; }
        .content { padding: 30px; }
        .card { background: #f8f9fa; border-radius: 10px; padding: 20px; margin-bottom: 20px; border: 1px solid #e0e0e0; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; transition: all 0.3s; margin: 5px; }
        .btn-primary { background: #FF6B6B; color: white; }
        .btn-success { background: #4ECDC4; color: white; }
        .btn:hover { opacity: 0.8; }
        .alert { padding: 15px; margin: 15px 0; border-radius: 5px; display: none; }
        .alert.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .code-block { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 5px; padding: 15px; font-family: monospace; font-size: 14px; overflow-x: auto; }
        .loading { text-align: center; padding: 20px; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #FF6B6B; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 10px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐍 PySnowball 雪球组合管理器</h1>
            <p>基于 pysnowball 项目的正确雪球API使用方法</p>
            <p>参考: <a href="https://github.com/uname-yang/pysnowball" target="_blank" style="color: white;">github.com/uname-yang/pysnowball</a></p>
        </div>

        <div class="content">
            <div class="card">
                <h3>🔍 问题分析</h3>
                <p><strong>原问题:</strong> <code>'portfolio_market'</code> 错误</p>
                <p><strong>根本原因:</strong> easytrader的雪球接口实现不完整</p>
                <p><strong>正确方案:</strong> 使用 pysnowball 项目提供的完整雪球API</p>
            </div>

            <div class="card">
                <h3>🚀 PySnowball 解决方案</h3>
                <p>PySnowball 是一个专门的雪球API Python库，提供了完整的雪球数据接口。</p>

                <h4>📦 安装 PySnowball</h4>
                <div class="code-block">pip install pysnowball</div>
                <button class="btn btn-primary" onclick="installPySnowball()">🔧 自动安装</button>

                <h4>🎯 主要功能</h4>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>✅ 获取股票实时行情</li>
                    <li>✅ 获取用户自选股列表</li>
                    <li>✅ 获取组合净值和持仓</li>
                    <li>✅ 获取财务数据</li>
                    <li>✅ 获取K线数据</li>
                </ul>
            </div>

            <div class="card">
                <h3>📋 使用示例</h3>
                <div id="pysnowballGuide">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>正在加载使用指导...</p>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>🧪 连接测试</h3>
                <button class="btn btn-success" onclick="testConnection()">🔗 测试雪球连接</button>
                <button class="btn btn-primary" onclick="getWatchList()">📋 获取自选股</button>
                <button class="btn btn-primary" onclick="searchCubes()">🔍 搜索组合</button>

                <div id="testResult" class="alert"></div>
            </div>

            <div class="card">
                <h3>📊 数据展示</h3>
                <div id="dataDisplay">
                    <p>点击上方按钮获取数据...</p>
                </div>
            </div>

            <div class="card">
                <h3>💡 集成建议</h3>
                <p><strong>替换方案:</strong> 将 easytrader 的雪球部分替换为 pysnowball</p>
                <p><strong>优势:</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>✅ 更完整的API覆盖</li>
                    <li>✅ 更稳定的接口实现</li>
                    <li>✅ 更好的错误处理</li>
                    <li>✅ 持续维护和更新</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function showAlert(message, type = 'info') {
            const alertDiv = document.getElementById('testResult');
            alertDiv.className = `alert ${type}`;
            alertDiv.textContent = message;
            alertDiv.style.display = 'block';

            setTimeout(() => {
                alertDiv.style.display = 'none';
            }, 5000);
        }

        function installPySnowball() {
            showAlert('正在安装 pysnowball...', 'info');

            fetch('/api/install_pysnowball', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('pysnowball 安装成功！', 'success');
                } else {
                    showAlert(`安装失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showAlert(`安装失败: ${error}`, 'error');
            });
        }

        function testConnection() {
            showAlert('正在测试连接...', 'info');

            fetch('/api/test_connection')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert(`连接成功: ${data.message}`, 'success');
                    } else {
                        showAlert(`连接失败: ${data.message}`, 'error');
                    }
                })
                .catch(error => {
                    showAlert(`连接失败: ${error}`, 'error');
                });
        }

        function getWatchList() {
            showAlert('正在获取自选股列表...', 'info');

            fetch('/api/watch_list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const displayDiv = document.getElementById('dataDisplay');
                        displayDiv.innerHTML = `
                            <h4>📋 自选股列表</h4>
                            <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        `;
                        showAlert('自选股列表获取成功', 'success');
                    } else {
                        showAlert(`获取失败: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    showAlert(`获取失败: ${error}`, 'error');
                });
        }

        function searchCubes() {
            showAlert('正在搜索组合...', 'info');

            fetch('/api/search_cubes')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const displayDiv = document.getElementById('dataDisplay');
                        displayDiv.innerHTML = `
                            <h4>🔍 组合搜索结果</h4>
                            <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        `;
                        showAlert('组合搜索成功', 'success');
                    } else {
                        showAlert(`搜索失败: ${data.error}`, 'error');
                    }
                })
                .catch(error => {
                    showAlert(`搜索失败: ${error}`, 'error');
                });
        }

        function loadGuide() {
            fetch('/api/pysnowball_guide')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const guide = data.guide;
                        const guideDiv = document.getElementById('pysnowballGuide');

                        let html = '<h4>📝 使用步骤</h4><ol>';
                        guide.steps.forEach(step => {
                            html += `<li>${step}</li>`;
                        });
                        html += '</ol>';

                        html += '<h4>💻 示例代码</h4>';
                        html += `<div class="code-block">${guide.example_code}</div>`;

                        guideDiv.innerHTML = html;
                    }
                })
                .catch(error => {
                    document.getElementById('pysnowballGuide').innerHTML = `<p class="error">加载失败: ${error}</p>`;
                });
        }

        // 页面加载时初始化
        window.onload = function() {
            loadGuide();
        };
    </script>
</body>
</html>
    '''

@app.route('/api/test_connection')
def test_connection():
    """测试连接"""
    success, message = portfolio_manager.test_connection()
    return jsonify({
        'success': success,
        'message': message
    })

@app.route('/api/watch_list')
def get_watch_list():
    """获取自选股列表"""
    try:
        result = portfolio_manager.get_watch_list()
        if result:
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取自选股列表失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/watch_stock/<portfolio_id>')
def get_watch_stock(portfolio_id):
    """获取自选股详情"""
    try:
        result = portfolio_manager.get_watch_stock(portfolio_id)
        if result:
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取自选股详情失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/cube_nav/<cube_symbol>')
def get_cube_nav(cube_symbol):
    """获取组合净值"""
    try:
        result = portfolio_manager.get_cube_nav_daily(cube_symbol)
        if result:
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取组合净值失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/cube_holdings/<cube_symbol>')
def get_cube_holdings(cube_symbol):
    """获取组合持仓"""
    try:
        result = portfolio_manager.get_cube_rebalancing_current(cube_symbol)
        if result:
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取组合持仓失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/search_cubes')
def search_cubes():
    """搜索用户组合"""
    try:
        result = portfolio_manager.search_cubes()
        if result:
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'error': '搜索组合失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/pysnowball_guide')
def get_pysnowball_guide():
    """获取pysnowball使用指导"""
    try:
        guide = portfolio_manager.create_portfolio_with_pysnowball()
        return jsonify({
            'success': True,
            'guide': guide
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/install_pysnowball', methods=['POST'])
def install_pysnowball():
    """安装pysnowball"""
    try:
        import subprocess
        result = subprocess.run(['pip', 'install', 'pysnowball'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            return jsonify({
                'success': True,
                'message': 'pysnowball安装成功',
                'output': result.stdout
            })
        else:
            return jsonify({
                'success': False,
                'error': result.stderr
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    print("🚀 基于PySnowball的雪球组合管理器")
    print("访问地址: http://localhost:5003")
    print("功能: 正确的雪球API使用方法")
    app.run(host='0.0.0.0', port=5003, debug=True)
