#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建项目结构脚本
按照拼音命名规范创建所有必要的目录和文件
"""

import os
from pathlib import Path
from typing import List, Dict


def create_directory_structure() -> None:
    """创建项目目录结构."""
    
    # 项目根目录结构
    directories = [
        # 源代码目录
        "src",
        "src/houduan",
        "src/houduan/api",
        "src/houduan/api/yonghu",
        "src/houduan/api/celue", 
        "src/houduan/api/shuju",
        "src/houduan/api/jiaoyi",
        "src/houduan/api/huice",
        
        "src/houduan/hexin",
        "src/houduan/hexin/celue_yinqing",
        "src/houduan/hexin/huice_yinqing",
        "src/houduan/hexin/jiaoyi_yinqing",
        "src/houduan/hexin/shuju_chuli",
        "src/houduan/hexin/fengxian_kongzhi",
        
        "src/houduan/shujuku",
        "src/houduan/shujuku/lianjie",
        "src/houduan/shujuku/qianyi",
        "src/houduan/shujuku/cangku",
        
        "src/houduan/moxing",
        "src/houduan/moxing/yonghu",
        "src/houduan/moxing/gupiao",
        "src/houduan/moxing/celue",
        "src/houduan/moxing/jiaoyi",
        "src/houduan/moxing/huice",
        
        "src/houduan/fuwu",
        "src/houduan/fuwu/tushare_fuwu",
        "src/houduan/fuwu/yonghu_fuwu",
        "src/houduan/fuwu/celue_fuwu",
        "src/houduan/fuwu/jiaoyi_fuwu",
        "src/houduan/fuwu/tongzhi_fuwu",
        
        "src/houduan/gongju",
        "src/houduan/gongju/jiami",
        "src/houduan/gongju/riqi",
        "src/houduan/gongju/yanzheng",
        "src/houduan/gongju/rizhi",
        
        # 前端目录
        "src/qianduan",
        "src/qianduan/src",
        "src/qianduan/src/zujian",
        "src/qianduan/src/yemian",
        "src/qianduan/src/shangdian",
        "src/qianduan/src/fuwu",
        "src/qianduan/src/gongju",
        "src/qianduan/public",
        
        # Electron目录
        "src/electron",
        
        # 测试目录
        "ceshi",
        "ceshi/danwei",
        "ceshi/jicheng", 
        "ceshi/duanduan",
        
        # 文档目录
        "wendang",
        "wendang/api",
        "wendang/yonghu",
        "wendang/kaifa",
        
        # 脚本目录
        "jiaoben",
        
        # 资源目录
        "ziyuan",
        "ziyuan/tubiao",
        "ziyuan/tupian",
        "ziyuan/shuju",
        
        # 配置目录
        "config",
        
        # 数据目录
        "data",
        "data/timescaledb",
        "data/redis",
        "data/pgadmin",
        
        # 日志目录
        "logs",
    ]
    
    print("🏗️ 创建项目目录结构...")
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")


def create_init_files() -> None:
    """创建__init__.py文件."""
    
    init_files = [
        "src/__init__.py",
        "src/houduan/__init__.py",
        "src/houduan/api/__init__.py",
        "src/houduan/api/yonghu/__init__.py",
        "src/houduan/api/celue/__init__.py",
        "src/houduan/api/shuju/__init__.py",
        "src/houduan/api/jiaoyi/__init__.py",
        "src/houduan/api/huice/__init__.py",
        "src/houduan/hexin/__init__.py",
        "src/houduan/hexin/celue_yinqing/__init__.py",
        "src/houduan/hexin/huice_yinqing/__init__.py",
        "src/houduan/hexin/jiaoyi_yinqing/__init__.py",
        "src/houduan/hexin/shuju_chuli/__init__.py",
        "src/houduan/hexin/fengxian_kongzhi/__init__.py",
        "src/houduan/shujuku/__init__.py",
        "src/houduan/shujuku/lianjie/__init__.py",
        "src/houduan/shujuku/qianyi/__init__.py",
        "src/houduan/shujuku/cangku/__init__.py",
        "src/houduan/moxing/__init__.py",
        "src/houduan/moxing/yonghu/__init__.py",
        "src/houduan/moxing/gupiao/__init__.py",
        "src/houduan/moxing/celue/__init__.py",
        "src/houduan/moxing/jiaoyi/__init__.py",
        "src/houduan/moxing/huice/__init__.py",
        "src/houduan/fuwu/__init__.py",
        "src/houduan/fuwu/tushare_fuwu/__init__.py",
        "src/houduan/fuwu/yonghu_fuwu/__init__.py",
        "src/houduan/fuwu/celue_fuwu/__init__.py",
        "src/houduan/fuwu/jiaoyi_fuwu/__init__.py",
        "src/houduan/fuwu/tongzhi_fuwu/__init__.py",
        "src/houduan/gongju/__init__.py",
        "src/houduan/gongju/jiami/__init__.py",
        "src/houduan/gongju/riqi/__init__.py",
        "src/houduan/gongju/yanzheng/__init__.py",
        "src/houduan/gongju/rizhi/__init__.py",
        "ceshi/__init__.py",
        "ceshi/danwei/__init__.py",
        "ceshi/jicheng/__init__.py",
        "ceshi/duanduan/__init__.py",
    ]
    
    print("\n📝 创建__init__.py文件...")
    
    for init_file in init_files:
        with open(init_file, 'w', encoding='utf-8') as f:
            module_name = Path(init_file).parent.name
            f.write(f'"""{module_name}模块."""\n')
        print(f"✅ 创建文件: {init_file}")


def create_config_files() -> None:
    """创建配置文件."""
    
    # PostgreSQL配置
    postgresql_conf = """# PostgreSQL配置文件
# 针对TimescaleDB优化

# 连接设置
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# TimescaleDB设置
shared_preload_libraries = 'timescaledb'
timescaledb.max_background_workers = 8

# 日志设置
log_destination = 'stderr'
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_statement = 'all'
log_min_duration_statement = 1000

# 性能优化
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
"""
    
    with open('config/postgresql.conf', 'w', encoding='utf-8') as f:
        f.write(postgresql_conf)
    
    # pg_hba.conf配置
    pg_hba_conf = """# PostgreSQL访问控制配置
# TYPE  DATABASE        USER            ADDRESS                 METHOD

# 本地连接
local   all             all                                     trust
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5

# Docker网络连接
host    all             all             **********/16           md5
"""
    
    with open('config/pg_hba.conf', 'w', encoding='utf-8') as f:
        f.write(pg_hba_conf)
    
    print("✅ 创建配置文件: config/postgresql.conf")
    print("✅ 创建配置文件: config/pg_hba.conf")


def create_gitignore() -> None:
    """创建.gitignore文件."""
    
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 数据库
*.db
*.sqlite3
data/timescaledb/
data/redis/
data/pgadmin/

# 日志
logs/
*.log

# 配置文件中的敏感信息
.env.local
.env.production
config/secrets.yaml

# 测试
.coverage
htmlcov/
.pytest_cache/
.tox/

# 前端
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.DS_Store

# Electron
dist-electron/
release/

# 临时文件
*.tmp
*.temp
.cache/

# 系统文件
Thumbs.db
.DS_Store
"""
    
    with open('.gitignore', 'w', encoding='utf-8') as f:
        f.write(gitignore_content)
    
    print("✅ 创建文件: .gitignore")


def create_readme() -> None:
    """创建README.md文件."""
    
    readme_content = """# A股量化交易平台

## 📊 项目简介

专为个人投资者设计的A股量化交易平台，提供策略开发、回测分析、模拟交易等功能。

### 🎯 核心特性

- **策略开发**: 可视化策略编辑器 + 代码编辑
- **历史回测**: 基于TimescaleDB的高性能回测
- **模拟交易**: 虚拟资金模拟真实交易环境
- **数据管理**: TuShare数据源，自动更新
- **风险控制**: 多层次风险管理机制

### 💻 技术架构

- **后端**: FastAPI + TimescaleDB + Redis
- **前端**: Vue.js 3 + TypeScript + Element Plus
- **桌面**: Electron跨平台桌面应用
- **部署**: Docker + Docker Compose

### 🚀 快速开始

1. **环境准备**
   ```bash
   # 安装Python依赖
   pip install -e .
   
   # 启动数据库
   docker-compose up -d timescaledb redis
   ```

2. **数据库初始化**
   ```bash
   # 运行初始化脚本
   python jiaoben/shujuku_qianyi.py
   ```

3. **启动服务**
   ```bash
   # 启动后端服务
   python src/houduan/main.py
   
   # 启动前端开发服务器
   cd src/qianduan && npm run dev
   ```

### 📁 项目结构

```
├── src/houduan/          # 后端代码
├── src/qianduan/         # 前端代码
├── src/electron/         # Electron主进程
├── ceshi/               # 测试代码
├── wendang/             # 项目文档
├── jiaoben/             # 构建脚本
└── ziyuan/              # 静态资源
```

### 📖 文档

- [API文档](wendang/api/)
- [用户手册](wendang/yonghu/)
- [开发文档](wendang/kaifa/)

### 📄 许可证

MIT License

### 🤝 贡献

欢迎提交Issue和Pull Request！
"""
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建文件: README.md")


def main() -> None:
    """主函数."""
    print("🎯 A股量化交易平台 - 项目结构创建工具")
    print("=" * 80)
    
    # 创建目录结构
    create_directory_structure()
    
    # 创建__init__.py文件
    create_init_files()
    
    # 创建配置文件
    create_config_files()
    
    # 创建.gitignore
    create_gitignore()
    
    # 创建README.md
    create_readme()
    
    print("\n🎉 项目结构创建完成！")
    print("\n📋 下一步操作:")
    print("1. 运行: docker-compose up -d  # 启动数据库")
    print("2. 运行: pip install -e .      # 安装Python依赖")
    print("3. 开始开发核心模块")
    
    print(f"\n📊 统计信息:")
    print(f"   创建目录: {len([d for d in Path('.').rglob('*') if d.is_dir()])} 个")
    print(f"   创建文件: {len([f for f in Path('.').rglob('*') if f.is_file()])} 个")


if __name__ == "__main__":
    main()
