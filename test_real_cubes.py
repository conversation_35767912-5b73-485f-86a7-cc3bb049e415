#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试真实的雪球组合
尝试一些公开的雪球组合编号
"""

import json
import logging
import pysnowball as ball

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_token():
    """加载token"""
    try:
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        cookies_str = config.get('cookies', '')
        xq_a_token = ''
        u = ''
        
        for cookie in cookies_str.split(';'):
            if '=' in cookie:
                key, value = cookie.strip().split('=', 1)
                if key == 'xq_a_token':
                    xq_a_token = value
                elif key == 'u':
                    u = value
        
        if xq_a_token and u:
            token = f"xq_a_token={xq_a_token};u={u}"
            ball.set_token(token)
            logger.info(f"✅ Token设置成功: u={u}")
            return True
        return False
    except Exception as e:
        logger.error(f"❌ 加载token失败: {e}")
        return False

def test_cube(cube_symbol):
    """测试单个组合"""
    logger.info(f"🧪 测试组合: {cube_symbol}")
    
    try:
        # 测试组合净值
        result = ball.nav_daily(cube_symbol)
        if result and len(result) > 0:
            cube_data = result[0]
            logger.info(f"✅ 组合净值成功: {cube_data.get('name', 'N/A')}")
            
            # 测试实时净值
            quote_result = ball.quote_current(cube_symbol)
            if quote_result and cube_symbol in quote_result:
                quote_data = quote_result[cube_symbol]
                logger.info(f"   实时净值: {quote_data.get('net_value', 'N/A')}")
                logger.info(f"   总收益: {quote_data.get('total_gain', 'N/A')}%")
            
            # 测试持仓
            position_result = ball.rebalancing_current(cube_symbol)
            if position_result:
                last_rb = position_result.get('last_rb', {})
                holdings = last_rb.get('holdings', [])
                logger.info(f"   持仓数量: {len(holdings)}")
                
                for holding in holdings[:3]:
                    logger.info(f"     {holding.get('stock_name', 'N/A')} "
                              f"权重: {holding.get('weight', 0):.1f}%")
            
            return True
        else:
            logger.warning(f"⚠️ 组合 {cube_symbol} 不存在或无权限访问")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试组合 {cube_symbol} 失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 测试真实的雪球组合")
    print("=" * 50)
    
    if not load_token():
        print("❌ Token加载失败")
        return
    
    # 尝试一些可能存在的组合编号
    test_cubes = [
        "ZH000001",  # 可能的第一个组合
        "ZH000002", 
        "ZH000010",
        "ZH001000",
        "ZH010000",
        "ZH100000",
        "ZH1000000",
        "ZH2000000",
        "ZH2567925",  # 从文档中看到的示例
        "ZH3000000",
        "ZH5000000"
    ]
    
    successful_cubes = []
    
    for cube_symbol in test_cubes:
        if test_cube(cube_symbol):
            successful_cubes.append(cube_symbol)
        print("-" * 30)
    
    print(f"\n📊 测试结果:")
    print(f"   测试组合数: {len(test_cubes)}")
    print(f"   成功组合数: {len(successful_cubes)}")
    
    if successful_cubes:
        print(f"   可用组合: {', '.join(successful_cubes)}")
    else:
        print("   ⚠️ 没有找到可访问的公开组合")
        print("   💡 建议:")
        print("     1. 在雪球网站创建自己的投资组合")
        print("     2. 或者寻找其他公开组合的编号")

if __name__ == '__main__':
    main()
