"""
示例单元测试文件
展示测试规范和最佳实践
"""

import pytest
from unittest.mock import Mock, patch
from typing import Dict, Any

# 这个文件将在测试完成后自动删除，仅用于展示测试规范


class TestExampleClass:
    """示例测试类 - 展示测试组织结构."""
    
    def test_basic_functionality_should_return_expected_result(self):
        """测试基础功能_应该返回期望结果.
        
        测试命名规范: test_功能_场景_期望结果
        """
        # Arrange (准备)
        input_value = 10
        expected_result = 20
        
        # Act (执行)
        result = input_value * 2
        
        # Assert (断言)
        assert result == expected_result
    
    def test_edge_case_with_zero_should_handle_gracefully(self):
        """测试边界情况_零值_应该优雅处理."""
        # Arrange
        input_value = 0
        expected_result = 0
        
        # Act
        result = input_value * 2
        
        # Assert
        assert result == expected_result
    
    def test_invalid_input_should_raise_exception(self):
        """测试无效输入_应该抛出异常."""
        # Arrange
        invalid_input = "not_a_number"
        
        # Act & Assert
        with pytest.raises(TypeError):
            result = invalid_input * 2
    
    @pytest.mark.parametrize("input_val,expected", [
        (1, 2),
        (5, 10),
        (-3, -6),
        (0, 0),
    ])
    def test_multiplication_with_various_inputs_should_return_correct_results(
        self, input_val: int, expected: int
    ):
        """测试乘法运算_多种输入_应该返回正确结果."""
        # Act
        result = input_val * 2
        
        # Assert
        assert result == expected


class TestMockingExamples:
    """模拟对象测试示例."""
    
    def test_external_api_call_should_return_mocked_data(self):
        """测试外部API调用_应该返回模拟数据."""
        # Arrange
        mock_response = {"status": "success", "data": [1, 2, 3]}
        
        with patch('requests.get') as mock_get:
            mock_get.return_value.json.return_value = mock_response
            mock_get.return_value.status_code = 200
            
            # Act
            import requests
            response = requests.get("https://api.example.com/data")
            
            # Assert
            assert response.status_code == 200
            assert response.json() == mock_response
            mock_get.assert_called_once_with("https://api.example.com/data")
    
    def test_database_operation_should_use_mocked_session(self, db_session):
        """测试数据库操作_应该使用模拟会话."""
        # 这里使用conftest.py中定义的db_session fixture
        # 实际测试中会操作测试数据库
        assert db_session is not None


class TestAsyncFunctionality:
    """异步功能测试示例."""
    
    @pytest.mark.asyncio
    async def test_async_function_should_complete_successfully(self):
        """测试异步函数_应该成功完成."""
        # Arrange
        async def async_multiply(x: int, y: int) -> int:
            return x * y
        
        # Act
        result = await async_multiply(3, 4)
        
        # Assert
        assert result == 12


class TestFixtureUsage:
    """Fixture使用示例."""
    
    def test_sample_data_fixture_should_provide_valid_data(self, sample_user_data):
        """测试示例数据fixture_应该提供有效数据."""
        # Act & Assert
        assert "username" in sample_user_data
        assert "email" in sample_user_data
        assert sample_user_data["username"] == "testuser"
    
    def test_temp_directory_fixture_should_exist(self, temp_dir):
        """测试临时目录fixture_应该存在."""
        # Assert
        assert temp_dir.exists()
        assert temp_dir.is_dir()


@pytest.mark.slow
class TestSlowOperations:
    """慢速操作测试 - 使用slow标记."""
    
    def test_time_consuming_operation_should_complete(self):
        """测试耗时操作_应该完成."""
        import time
        
        # Arrange
        start_time = time.time()
        
        # Act
        time.sleep(0.1)  # 模拟耗时操作
        
        # Assert
        elapsed_time = time.time() - start_time
        assert elapsed_time >= 0.1


@pytest.mark.integration
class TestIntegrationScenarios:
    """集成测试场景."""
    
    def test_multiple_components_integration_should_work_together(self):
        """测试多组件集成_应该协同工作."""
        # 这里会测试多个组件的集成
        pass


class TestErrorHandling:
    """错误处理测试."""
    
    def test_function_with_invalid_parameter_should_raise_value_error(self):
        """测试函数_无效参数_应该抛出值错误."""
        def validate_positive_number(num: int) -> int:
            if num <= 0:
                raise ValueError("Number must be positive")
            return num
        
        # Act & Assert
        with pytest.raises(ValueError, match="Number must be positive"):
            validate_positive_number(-1)
    
    def test_function_with_none_parameter_should_raise_type_error(self):
        """测试函数_None参数_应该抛出类型错误."""
        def process_string(text: str) -> str:
            if text is None:
                raise TypeError("Text cannot be None")
            return text.upper()
        
        # Act & Assert
        with pytest.raises(TypeError, match="Text cannot be None"):
            process_string(None)


class TestDataValidation:
    """数据验证测试."""
    
    @pytest.mark.parametrize("email,is_valid", [
        ("<EMAIL>", True),
        ("invalid-email", False),
        ("", False),
        ("test@", False),
        ("@example.com", False),
    ])
    def test_email_validation_should_return_correct_result(
        self, email: str, is_valid: bool
    ):
        """测试邮箱验证_应该返回正确结果."""
        import re
        
        def is_valid_email(email: str) -> bool:
            pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            return bool(re.match(pattern, email))
        
        # Act
        result = is_valid_email(email)
        
        # Assert
        assert result == is_valid


# 测试工具函数
def create_test_user(username: str = "testuser") -> Dict[str, Any]:
    """创建测试用户数据."""
    return {
        "username": username,
        "email": f"{username}@example.com",
        "password": "testpassword123",
        "subscription_type": "basic"
    }


def assert_response_success(response_data: Dict[str, Any]) -> None:
    """断言响应成功."""
    assert "status" in response_data
    assert response_data["status"] == "success"


# 清理函数 - 测试完成后会自动调用
def cleanup_test_data():
    """清理测试数据."""
    # 这里可以添加清理逻辑
    # 例如删除临时文件、清理数据库等
    pass


# 测试完成后自动清理
import atexit
atexit.register(cleanup_test_data)
