[flake8]
# 最大行长度
max-line-length = 88

# 排除的文件和目录
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .eggs,
    *.egg,
    build,
    dist,
    .tox,
    .mypy_cache,
    .pytest_cache,
    node_modules,
    migrations

# 忽略的错误代码
ignore = 
    # E203: whitespace before ':' (与black冲突)
    E203,
    # W503: line break before binary operator (与black冲突)
    W503,
    # E501: line too long (由black处理)
    E501

# 每个文件的最大复杂度
max-complexity = 10

# 导入顺序检查
import-order-style = google
application-import-names = src

# 文档字符串检查
docstring-convention = google

# 选择的检查插件
select = 
    E,  # pycodestyle errors
    W,  # pycodestyle warnings
    F,  # pyflakes
    C,  # mccabe complexity
    B,  # flake8-bugbear
    I,  # isort

# 每行最大字符数
max-line-length = 88

# 缩进大小
indent-size = 4

# 统计信息
statistics = True
count = True
