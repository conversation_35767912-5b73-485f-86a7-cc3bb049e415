<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1>用户注册</h1>
        <p>创建您的量化交易账户</p>
      </div>
      
      <el-form class="register-form">
        <el-form-item>
          <el-input placeholder="用户名" prefix-icon="User" />
        </el-form-item>
        <el-form-item>
          <el-input placeholder="邮箱" prefix-icon="Message" />
        </el-form-item>
        <el-form-item>
          <el-input type="password" placeholder="密码" prefix-icon="Lock" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="register-button">注册</el-button>
        </el-form-item>
        <el-form-item>
          <div class="login-link">
            已有账号？<el-link type="primary" @click="$router.push('/login')">立即登录</el-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
// 注册页面逻辑
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
  
  h1 {
    font-size: 24px;
    margin-bottom: 8px;
  }
  
  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.register-button {
  width: 100%;
  height: 44px;
}

.login-link {
  text-align: center;
  color: var(--el-text-color-regular);
}
</style>
