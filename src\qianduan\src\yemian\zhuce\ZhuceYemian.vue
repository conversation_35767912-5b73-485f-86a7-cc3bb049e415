<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <div class="logo">
          <el-icon class="logo-icon"><TrendCharts /></el-icon>
          <h1 class="logo-text">用户注册</h1>
        </div>
        <p class="subtitle">创建您的量化交易账户</p>
      </div>

      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
        size="large"
      >
        <el-form-item prop="yonghuming">
          <el-input
            v-model="registerForm.yonghuming"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="youxiang">
          <el-input
            v-model="registerForm.youxiang"
            placeholder="请输入邮箱地址"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item prop="mima">
          <el-input
            v-model="registerForm.mima"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item prop="queren_mima">
          <el-input
            v-model="registerForm.queren_mima"
            type="password"
            placeholder="请确认密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleRegister"
          />
        </el-form-item>

        <el-form-item prop="tongyi_xieyi">
          <el-checkbox v-model="registerForm.tongyi_xieyi">
            我已阅读并同意
            <el-link type="primary" :underline="false">《用户协议》</el-link>
            和
            <el-link type="primary" :underline="false">《隐私政策》</el-link>
          </el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="register-button"
            :loading="loading"
            @click="handleRegister"
          >
            {{ loading ? '注册中...' : '立即注册' }}
          </el-button>
        </el-form-item>

        <el-form-item>
          <div class="login-link">
            已有账号？
            <el-link type="primary" @click="$router.push('/login')">
              立即登录
            </el-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { RegisterForm } from '@/types/user'

const router = useRouter()
const registerFormRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const registerForm = reactive<RegisterForm>({
  yonghuming: '',
  youxiang: '',
  mima: '',
  queren_mima: '',
  tongyi_xieyi: false
})

// 自定义验证规则
const validatePassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'))
  } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
    callback(new Error('密码必须包含大小写字母和数字'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请确认密码'))
  } else if (value !== registerForm.mima) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const validateAgreement = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error('请阅读并同意用户协议和隐私政策'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules: FormRules = {
  yonghuming: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  youxiang: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  mima: [
    { validator: validatePassword, trigger: 'blur' }
  ],
  queren_mima: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  tongyi_xieyi: [
    { validator: validateAgreement, trigger: 'change' }
  ]
}

// 注册处理
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    const valid = await registerFormRef.value.validate()
    if (!valid) return

    loading.value = true

    // 模拟注册API调用
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 检查用户名是否已存在（模拟）
    if (registerForm.yonghuming === 'admin') {
      ElMessage.error('用户名已存在，请选择其他用户名')
      return
    }

    // 检查邮箱是否已存在（模拟）
    if (registerForm.youxiang === '<EMAIL>') {
      ElMessage.error('邮箱已被注册，请使用其他邮箱')
      return
    }

    ElMessage.success('注册成功！请登录您的账户')

    // 跳转到登录页面
    router.push('/login')
  } catch (error) {
    console.error('注册失败:', error)
    ElMessage.error('注册失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.register-header {
  text-align: center;
  margin-bottom: 32px;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;

    .logo-icon {
      font-size: 32px;
      color: #10b981;
    }

    .logo-text {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
    }
  }

  .subtitle {
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin: 0;
  }
}

.register-form {
  .register-button {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
    background: linear-gradient(135deg, #10b981, #059669);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #059669, #047857);
    }
  }

  .login-link {
    text-align: center;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }

  // 协议复选框样式
  :deep(.el-checkbox) {
    .el-checkbox__label {
      font-size: 13px;
      line-height: 1.4;
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .register-card {
    padding: 24px;
  }

  .register-header .logo .logo-text {
    font-size: 20px;
  }
}
</style>
