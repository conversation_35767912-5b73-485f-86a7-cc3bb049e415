{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/zujian/*": ["./src/zujian/*"], "@/yemian/*": ["./src/yemian/*"], "@/shangdian/*": ["./src/shangdian/*"], "@/fuwu/*": ["./src/fuwu/*"], "@/gongju/*": ["./src/gongju/*"], "@/types/*": ["./src/types/*"], "@/styles/*": ["./src/styles/*"]}, "types": ["vite/client", "element-plus/global"], "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "jsxImportSource": "vue"}}