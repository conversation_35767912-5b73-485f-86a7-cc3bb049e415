#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试easytrader的雪球功能
获取真实的持仓、余额等详细信息
"""

import json
import logging
import easytrader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_easytrader_xueqiu():
    """测试easytrader的雪球功能"""
    logger.info("🧪 测试easytrader雪球功能")
    logger.info("=" * 50)
    
    try:
        # 加载配置
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        logger.info("✅ 配置文件加载成功")
        
        # 创建雪球交易实例
        xq = easytrader.use('xq')
        
        # 准备登录信息
        login_info = {
            'cookies': config.get('cookies', ''),
            'portfolio_code': 'ZH004612',  # 示例组合代码，需要替换为实际的
            'portfolio_market': 'cn'
        }
        
        logger.info("🔑 尝试登录雪球...")
        
        # 登录
        xq.prepare(**login_info)
        
        logger.info("✅ 雪球登录成功")
        
        # 测试各种功能
        test_functions = [
            ('获取余额', lambda: xq.balance),
            ('获取持仓', lambda: xq.position),
            ('获取今日委托', lambda: xq.today_entrusts),
            ('获取今日成交', lambda: xq.today_trades),
            ('获取组合信息', lambda: xq.get_portfolio()),
        ]
        
        for func_name, func in test_functions:
            try:
                logger.info(f"\n📊 {func_name}:")
                result = func()
                logger.info(f"结果类型: {type(result)}")
                
                if isinstance(result, (dict, list)):
                    logger.info(f"详细结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
                else:
                    logger.info(f"结果: {result}")
                    
            except Exception as e:
                logger.error(f"❌ {func_name}失败: {e}")
        
        # 测试搜索股票
        logger.info(f"\n🔍 测试搜索股票:")
        try:
            search_result = xq.search_stock('方大特钢')
            logger.info(f"搜索结果: {json.dumps(search_result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            logger.error(f"❌ 搜索股票失败: {e}")
        
        # 测试获取股票信息
        logger.info(f"\n📈 测试获取股票信息:")
        try:
            stock_info = xq.get_stock_info('SH600507')
            logger.info(f"股票信息: {json.dumps(stock_info, indent=2, ensure_ascii=False)}")
        except Exception as e:
            logger.error(f"❌ 获取股票信息失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ easytrader测试失败: {e}")
        return False

def test_easytrader_methods():
    """测试easytrader的所有可用方法"""
    logger.info("🔍 探索easytrader的可用方法")
    logger.info("=" * 50)
    
    try:
        xq = easytrader.use('xq')
        
        # 获取所有可用方法
        methods = [method for method in dir(xq) if not method.startswith('_')]
        
        logger.info(f"📋 可用方法列表 ({len(methods)}个):")
        for i, method in enumerate(methods, 1):
            try:
                method_obj = getattr(xq, method)
                if callable(method_obj):
                    logger.info(f"  {i:2d}. {method}() - 可调用方法")
                else:
                    logger.info(f"  {i:2d}. {method} - 属性")
            except Exception as e:
                logger.info(f"  {i:2d}. {method} - 无法访问: {e}")
        
        # 查看文档字符串
        important_methods = ['balance', 'position', 'today_entrusts', 'today_trades', 'buy', 'sell']
        
        logger.info(f"\n📖 重要方法文档:")
        for method in important_methods:
            if hasattr(xq, method):
                method_obj = getattr(xq, method)
                doc = getattr(method_obj, '__doc__', '无文档')
                logger.info(f"  {method}: {doc}")
        
    except Exception as e:
        logger.error(f"❌ 方法探索失败: {e}")

def test_xueqiu_portfolio_apis():
    """测试雪球组合相关的API"""
    logger.info("🎯 测试雪球组合API")
    logger.info("=" * 50)
    
    try:
        # 加载配置
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 尝试不同的组合代码格式
        portfolio_codes = [
            'ZH004612',  # 示例格式
            'ZH000001',  # 另一种格式
            '4612',      # 纯数字
            'P/ZH004612' # 完整路径格式
        ]
        
        for portfolio_code in portfolio_codes:
            logger.info(f"\n🔍 测试组合代码: {portfolio_code}")
            
            try:
                xq = easytrader.use('xq')
                
                login_info = {
                    'cookies': config.get('cookies', ''),
                    'portfolio_code': portfolio_code,
                    'portfolio_market': 'cn'
                }
                
                xq.prepare(**login_info)
                
                # 尝试获取组合信息
                try:
                    portfolio_info = xq.get_portfolio()
                    logger.info(f"✅ 组合信息获取成功: {json.dumps(portfolio_info, indent=2, ensure_ascii=False)}")
                except Exception as e:
                    logger.warning(f"⚠️ 获取组合信息失败: {e}")
                
                # 尝试获取持仓
                try:
                    positions = xq.position
                    logger.info(f"✅ 持仓信息获取成功: {json.dumps(positions, indent=2, ensure_ascii=False)}")
                except Exception as e:
                    logger.warning(f"⚠️ 获取持仓失败: {e}")
                
                # 尝试获取余额
                try:
                    balance = xq.balance
                    logger.info(f"✅ 余额信息获取成功: {json.dumps(balance, indent=2, ensure_ascii=False)}")
                except Exception as e:
                    logger.warning(f"⚠️ 获取余额失败: {e}")
                
            except Exception as e:
                logger.warning(f"⚠️ 组合代码 {portfolio_code} 测试失败: {e}")
    
    except Exception as e:
        logger.error(f"❌ 组合API测试失败: {e}")

def find_user_portfolios():
    """尝试找到用户的真实组合"""
    logger.info("🔍 寻找用户真实组合")
    logger.info("=" * 50)
    
    try:
        # 加载配置
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        cookies_str = config.get('cookies', '')
        
        # 从cookies中提取用户ID
        user_id = None
        for cookie in cookies_str.split(';'):
            if '=' in cookie:
                key, value = cookie.strip().split('=', 1)
                if key == 'u':
                    user_id = value
                    break
        
        if user_id:
            logger.info(f"📋 用户ID: {user_id}")
            
            # 尝试构造可能的组合代码
            possible_codes = []
            
            # 基于用户ID的组合代码
            user_id_int = int(user_id)
            for i in range(1, 100):  # 尝试前100个组合
                possible_codes.append(f"ZH{user_id_int + i:06d}")
                possible_codes.append(f"ZH{i:06d}")
            
            logger.info(f"🎯 尝试 {len(possible_codes)} 个可能的组合代码...")
            
            found_portfolios = []
            
            for i, portfolio_code in enumerate(possible_codes[:20]):  # 限制测试数量
                try:
                    logger.info(f"  测试 {i+1}/20: {portfolio_code}")
                    
                    xq = easytrader.use('xq')
                    
                    login_info = {
                        'cookies': cookies_str,
                        'portfolio_code': portfolio_code,
                        'portfolio_market': 'cn'
                    }
                    
                    xq.prepare(**login_info)
                    
                    # 尝试获取组合信息
                    portfolio_info = xq.get_portfolio()
                    
                    if portfolio_info:
                        logger.info(f"✅ 找到有效组合: {portfolio_code}")
                        found_portfolios.append({
                            'code': portfolio_code,
                            'info': portfolio_info
                        })
                        
                        # 尝试获取持仓
                        try:
                            positions = xq.position
                            logger.info(f"   持仓: {json.dumps(positions, indent=2, ensure_ascii=False)}")
                        except:
                            pass
                
                except Exception as e:
                    # 忽略错误，继续尝试下一个
                    pass
            
            if found_portfolios:
                logger.info(f"\n🎉 找到 {len(found_portfolios)} 个有效组合:")
                for portfolio in found_portfolios:
                    logger.info(f"  📊 {portfolio['code']}: {portfolio['info']}")
            else:
                logger.warning("⚠️ 没有找到有效的组合")
        
        else:
            logger.error("❌ 无法从cookies中提取用户ID")
    
    except Exception as e:
        logger.error(f"❌ 寻找组合失败: {e}")

def main():
    """主函数"""
    print("🚀 easytrader雪球功能测试")
    print("获取真实的持仓、余额等详细信息")
    print("=" * 60)
    
    # 1. 探索可用方法
    test_easytrader_methods()
    
    # 2. 测试基本功能
    test_easytrader_xueqiu()
    
    # 3. 测试组合API
    test_xueqiu_portfolio_apis()
    
    # 4. 寻找用户组合
    find_user_portfolios()
    
    print("\n🎯 测试完成！")

if __name__ == '__main__':
    main()
