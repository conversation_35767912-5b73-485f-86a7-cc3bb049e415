<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑订阅类型' : '添加订阅类型'"
    width="800px"
    :before-close="handleClose"
  >
    <div class="subscription-type-dialog-container" v-loading="loading">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="类型名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入类型名称（英文）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示名称" prop="displayName">
              <el-input v-model="form.displayName" placeholder="请输入显示名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="2"
            placeholder="请输入订阅类型描述"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="价格" prop="price">
              <el-input-number 
                v-model="form.price" 
                :min="0" 
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原价" prop="originalPrice">
              <el-input-number 
                v-model="form.originalPrice" 
                :min="0" 
                :precision="2"
                style="width: 100%"
                placeholder="可选"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="时长(天)" prop="duration">
              <el-input-number 
                v-model="form.duration" 
                :min="1" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="功能特性" prop="features">
          <div class="features-input">
            <el-tag
              v-for="(feature, index) in form.features"
              :key="index"
              closable
              @close="removeFeature(index)"
              class="feature-tag"
            >
              {{ feature }}
            </el-tag>
            <el-input
              v-if="inputVisible"
              ref="inputRef"
              v-model="inputValue"
              class="feature-input"
              size="small"
              @keyup.enter="handleInputConfirm"
              @blur="handleInputConfirm"
            />
            <el-button v-else class="button-new-tag" size="small" @click="showInput">
              + 添加特性
            </el-button>
          </div>
        </el-form-item>
        
        <el-divider content-position="left">使用限制</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最大策略数" prop="limitations.maxStrategies">
              <el-input-number 
                v-model="form.limitations.maxStrategies" 
                :min="-1" 
                style="width: 100%"
                placeholder="-1表示无限制"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回测天数" prop="limitations.maxBacktestDays">
              <el-input-number 
                v-model="form.limitations.maxBacktestDays" 
                :min="-1" 
                style="width: 100%"
                placeholder="-1表示无限制"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实盘账户数" prop="limitations.maxRealTradingAccounts">
              <el-input-number 
                v-model="form.limitations.maxRealTradingAccounts" 
                :min="-1" 
                style="width: 100%"
                placeholder="-1表示无限制"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据刷新间隔" prop="limitations.dataRefreshInterval">
              <el-input-number 
                v-model="form.limitations.dataRefreshInterval" 
                :min="1" 
                style="width: 100%"
              />
              <span style="margin-left: 8px;">分钟</span>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="支持级别" prop="limitations.supportLevel">
          <el-select v-model="form.limitations.supportLevel" style="width: 200px">
            <el-option label="基础支持" value="basic" />
            <el-option label="标准支持" value="standard" />
            <el-option label="高级支持" value="premium" />
            <el-option label="VIP支持" value="vip" />
          </el-select>
        </el-form-item>
        
        <el-divider content-position="left">其他设置</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number 
                v-model="form.sortOrder" 
                :min="1" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启用状态" prop="isActive">
              <el-switch v-model="form.isActive" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="推荐标识" prop="isRecommended">
              <el-switch v-model="form.isRecommended" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { subscriptionService, type SubscriptionType } from '@/fuwu/subscriptionService'

// Props
interface Props {
  modelValue: boolean
  subscriptionType?: SubscriptionType | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  subscriptionType: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref()
const inputRef = ref()
const loading = ref(false)
const submitting = ref(false)
const inputVisible = ref(false)
const inputValue = ref('')

const form = ref({
  name: '',
  displayName: '',
  description: '',
  price: 0,
  originalPrice: undefined as number | undefined,
  duration: 30,
  features: [] as string[],
  limitations: {
    maxStrategies: 10,
    maxBacktestDays: 365,
    maxRealTradingAccounts: 1,
    dataRefreshInterval: 15,
    supportLevel: 'standard' as 'basic' | 'standard' | 'premium' | 'vip'
  },
  isActive: true,
  isRecommended: false,
  sortOrder: 1
})

const rules = {
  name: [
    { required: true, message: '请输入类型名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '类型名称只能包含字母、数字和下划线，且以字母或下划线开头', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' }
  ],
  duration: [
    { required: true, message: '请输入时长', trigger: 'blur' }
  ],
  features: [
    { required: true, message: '请至少添加一个功能特性', trigger: 'change' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.subscriptionType)

// 方法
const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  if (inputValue.value && !form.value.features.includes(inputValue.value)) {
    form.value.features.push(inputValue.value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const removeFeature = (index: number) => {
  form.value.features.splice(index, 1)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (isEdit.value && props.subscriptionType) {
      // 更新订阅类型
      const success = await subscriptionService.updateSubscriptionType(props.subscriptionType.id, form.value)
      
      if (success) {
        ElMessage.success('订阅类型更新成功')
        emit('refresh')
        handleClose()
      } else {
        ElMessage.error('更新失败')
      }
    } else {
      // 添加订阅类型
      const id = await subscriptionService.addSubscriptionType(form.value)
      
      if (id) {
        ElMessage.success('订阅类型添加成功')
        emit('refresh')
        handleClose()
      } else {
        ElMessage.error('添加失败')
      }
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  formRef.value?.resetFields()
  visible.value = false
}

// 监听器
watch(() => props.subscriptionType, (newType) => {
  if (newType) {
    form.value = {
      name: newType.name,
      displayName: newType.displayName,
      description: newType.description,
      price: newType.price,
      originalPrice: newType.originalPrice,
      duration: newType.duration,
      features: [...newType.features],
      limitations: { ...newType.limitations },
      isActive: newType.isActive,
      isRecommended: newType.isRecommended,
      sortOrder: newType.sortOrder
    }
  } else {
    form.value = {
      name: '',
      displayName: '',
      description: '',
      price: 0,
      originalPrice: undefined,
      duration: 30,
      features: [],
      limitations: {
        maxStrategies: 10,
        maxBacktestDays: 365,
        maxRealTradingAccounts: 1,
        dataRefreshInterval: 15,
        supportLevel: 'standard'
      },
      isActive: true,
      isRecommended: false,
      sortOrder: 1
    }
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.subscription-type-dialog-container {
  min-height: 400px;
  
  .features-input {
    .feature-tag {
      margin-right: 8px;
      margin-bottom: 8px;
    }
    
    .feature-input {
      width: 120px;
      margin-right: 8px;
      margin-bottom: 8px;
    }
    
    .button-new-tag {
      margin-bottom: 8px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
