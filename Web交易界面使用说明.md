# 🚀 EasyQuant Web交易界面使用说明

## 📋 功能概述

这是一个基于Flask的Web交易界面，集成了easyquant量化交易框架，提供了友好的图形化交易操作界面。

## 🌟 主要功能

### 1. **引擎控制**
- ✅ 启动/停止交易引擎
- ✅ 实时状态监控
- ✅ 绿色脉冲指示器显示运行状态

### 2. **账户管理**
- 📊 实时显示总资产
- 💰 显示可用余额
- 📈 显示市值
- 💱 显示币种信息

### 3. **实时行情**
- 📱 输入股票代码自动获取行情
- 📊 显示当前价格、涨跌幅
- 📈 五档买卖盘显示
- 🎯 点击五档价格自动填入交易价格

### 4. **股票交易**
- 🔥 买入操作
- 💸 卖出操作
- 💡 智能价格选择
- ✅ 实时交易反馈

### 5. **持仓监控**
- 📋 实时持仓信息
- 📊 成本价显示
- 📈 当前价格显示
- 📉 盈亏状况

### 6. **系统日志**
- 📝 实时系统日志
- ⚠️ 错误信息提示
- ✅ 操作成功确认
- 🔄 自动滚动显示

## 🚀 使用步骤

### 第一步：启动系统
1. 确保已安装所有依赖包
2. 运行 `python web_trading.py`
3. 浏览器访问 http://localhost:5000

### 第二步：启动引擎
1. 点击页面上的"启动引擎"按钮
2. 等待状态指示器变为绿色脉冲
3. 确认账户信息正常显示

### 第三步：查看行情
1. 在股票代码输入框输入代码（如：600507）
2. 失去焦点后自动获取行情
3. 查看五档买卖盘信息
4. 点击任意价格自动填入交易价格

### 第四步：进行交易
1. 确认股票代码和价格
2. 输入交易数量
3. 点击"买入"或"卖出"按钮
4. 查看交易结果反馈

## 🎨 界面特色

### 响应式设计
- 适配不同屏幕尺寸
- 现代化渐变背景
- 圆角卡片设计

### 实时更新
- 每2秒自动刷新状态
- 实时账户信息更新
- 动态日志显示

### 交互体验
- 点击五档价格快速选择
- 颜色区分涨跌状态
- 即时消息提示

## 🔧 技术架构

### 后端技术
- **Flask**: Web框架
- **easyquant**: 量化交易引擎
- **easyquotation**: 行情数据获取
- **threading**: 多线程处理

### 前端技术
- **HTML5**: 页面结构
- **CSS3**: 样式设计
- **JavaScript**: 交互逻辑
- **AJAX**: 异步数据交换

### 数据交换
- **RESTful API**: 后端接口
- **JSON**: 数据格式
- **WebSocket**: 实时通信（未来扩展）

## 📊 API接口说明

### 系统控制
- `POST /start_engine` - 启动交易引擎
- `POST /stop_engine` - 停止交易引擎
- `GET /get_status` - 获取系统状态

### 行情数据
- `GET /get_quote/<stock_code>` - 获取股票行情

### 交易操作
- `POST /buy_stock` - 买入股票
- `POST /sell_stock` - 卖出股票

## ⚠️ 注意事项

### 安全提醒
1. **模拟交易**: 当前连接雪球模拟账户，资金安全
2. **开发环境**: 仅用于开发测试，不可用于生产
3. **网络要求**: 需要稳定的网络连接获取行情

### 配置要求
1. **配置文件**: 确保 `xq.json` 配置正确
2. **依赖包**: 安装所有必需的Python包
3. **端口占用**: 确保5000端口未被占用

### 使用限制
1. **并发限制**: 单用户使用，不支持多用户
2. **数据延迟**: 行情数据可能有延迟
3. **交易限制**: 受券商接口限制

## 🐛 常见问题

### Q1: 引擎启动失败
**A**: 检查配置文件是否正确，确保网络连接正常

### Q2: 无法获取行情
**A**: 确认股票代码正确，检查行情接口是否正常

### Q3: 交易失败
**A**: 检查账户余额，确认交易参数正确

### Q4: 页面无响应
**A**: 刷新页面，检查后端服务是否正常运行

## 🔄 更新日志

### v1.0.0 (2025-08-18)
- ✅ 基础交易功能
- ✅ 实时行情显示
- ✅ 五档买卖盘
- ✅ 账户信息监控
- ✅ 系统日志显示

### 未来计划
- 📈 K线图显示
- 📊 技术指标
- 🔔 价格提醒
- 📱 移动端适配
- 🔐 用户认证系统

## 📞 技术支持

如有问题，请检查：
1. Python环境和依赖包
2. 配置文件设置
3. 网络连接状态
4. 系统日志信息

---

**免责声明**: 本系统仅用于学习和测试目的，不构成投资建议。使用者需自行承担投资风险。
