#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
尝试创建雪球模拟盈亏账户或寻找其他持仓数据源
"""

import json
import logging
import requests
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class XueqiuAccountCreator:
    """雪球账户创建器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/******** Firefox/61.0',
            'Referer': 'https://xueqiu.com/performance',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ Token设置成功: u={u}")
            
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
    
    def try_create_simulation_account(self):
        """尝试创建模拟盈亏账户"""
        logger.info("🔧 尝试创建模拟盈亏账户")
        logger.info("=" * 60)
        
        # 可能的创建API端点
        create_apis = [
            "https://xueqiu.com/performance/api/create",
            "https://xueqiu.com/api/performance/create",
            "https://xueqiu.com/v4/performance/create",
            "https://xueqiu.com/moni/create",
            "https://xueqiu.com/simulation/create",
        ]
        
        # 创建请求数据
        create_data_variants = [
            {"name": "测试", "initial_cash": 1000000},
            {"name": "test", "initial_cash": 1000000},
            {"account_name": "测试", "cash": 1000000},
            {"account_name": "test", "cash": 1000000},
            {"portfolio_name": "测试"},
            {"portfolio_name": "test"},
        ]
        
        results = []
        
        for api_url in create_apis:
            for data in create_data_variants:
                try:
                    logger.info(f"\n🔗 测试创建API: {api_url}")
                    logger.info(f"   数据: {data}")
                    
                    response = self.session.post(api_url, json=data, timeout=10)
                    logger.info(f"   状态: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            logger.info(f"   ✅ 创建成功: {result}")
                            results.append({
                                'api': api_url,
                                'data': data,
                                'result': result,
                                'success': True
                            })
                            break  # 成功后跳出内层循环
                        except json.JSONDecodeError:
                            logger.info(f"   ⚠️ 非JSON响应: {response.text[:200]}")
                    else:
                        logger.info(f"   ❌ 失败: {response.status_code}")
                        
                except Exception as e:
                    logger.info(f"   ❌ 异常: {e}")
        
        return results
    
    def search_existing_portfolios(self):
        """搜索现有的投资组合"""
        logger.info("🔍 搜索现有投资组合")
        logger.info("=" * 60)
        
        # 尝试不同的搜索方式
        search_methods = [
            # 方法1: 通过用户ID搜索
            lambda: self.search_by_user_id(),
            # 方法2: 通过自选股搜索
            lambda: self.search_watchlist(),
            # 方法3: 通过交易记录搜索
            lambda: self.search_trade_history(),
        ]
        
        all_results = {}
        
        for i, method in enumerate(search_methods, 1):
            try:
                logger.info(f"\n📊 方法{i}: 执行搜索")
                result = method()
                all_results[f'method_{i}'] = result
            except Exception as e:
                logger.error(f"   ❌ 方法{i}失败: {e}")
                all_results[f'method_{i}'] = {'error': str(e)}
        
        return all_results
    
    def search_by_user_id(self):
        """通过用户ID搜索"""
        user_id = "4380321271"
        
        # 尝试不同的用户相关API
        user_apis = [
            f"https://xueqiu.com/v4/stock/portfolio/list.json?user_id={user_id}",
            f"https://xueqiu.com/cubes/list.json?user_id={user_id}",
            f"https://xueqiu.com/performance/list.json?user_id={user_id}",
            f"https://xueqiu.com/u/{user_id}/portfolios.json",
            f"https://xueqiu.com/u/{user_id}/cubes.json",
        ]
        
        results = {}
        
        for api_url in user_apis:
            try:
                logger.info(f"   🔗 {api_url}")
                response = self.session.get(api_url, timeout=10)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   ✅ 成功: {len(str(data))} 字符")
                        results[api_url] = data
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON")
                else:
                    logger.info(f"   ❌ {response.status_code}")
                    
            except Exception as e:
                logger.info(f"   ❌ 异常: {e}")
        
        return results
    
    def search_watchlist(self):
        """搜索自选股"""
        try:
            # 获取自选股列表
            watch_list = ball.watch_list()
            if not watch_list:
                return {'error': '无法获取自选股列表'}
            
            logger.info(f"   📋 自选股分类: {list(watch_list.get('data', {}).keys())}")
            
            # 检查每个分类的股票
            results = {}
            
            for category, items in watch_list.get('data', {}).items():
                if isinstance(items, list):
                    for item in items:
                        item_id = item.get('id')
                        item_name = item.get('name', '')
                        
                        try:
                            # 获取该分类的股票
                            stocks = ball.watch_stock(item_id)
                            if stocks and 'data' in stocks:
                                stock_list = stocks['data'].get('stocks', [])
                                if stock_list:
                                    logger.info(f"   📊 {item_name} (ID: {item_id}): {len(stock_list)} 只股票")
                                    results[f"{category}_{item_name}"] = {
                                        'id': item_id,
                                        'stocks': stock_list
                                    }
                        except Exception as e:
                            logger.info(f"   ⚠️ 获取 {item_name} 失败: {e}")
            
            return results
            
        except Exception as e:
            return {'error': str(e)}
    
    def search_trade_history(self):
        """搜索交易记录"""
        # 尝试不同的交易记录API
        trade_apis = [
            "https://xueqiu.com/v4/stock/portfolio/trades.json",
            "https://xueqiu.com/performance/trades.json",
            "https://xueqiu.com/trading/history.json",
            "https://xueqiu.com/account/trades.json",
        ]
        
        results = {}
        
        for api_url in trade_apis:
            try:
                logger.info(f"   🔗 {api_url}")
                response = self.session.get(api_url, timeout=10)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   ✅ 成功: {data}")
                        results[api_url] = data
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON")
                else:
                    logger.info(f"   ❌ {response.status_code}")
                    
            except Exception as e:
                logger.info(f"   ❌ 异常: {e}")
        
        return results
    
    def check_user_info(self):
        """检查用户信息"""
        logger.info("👤 检查用户信息")
        logger.info("=" * 60)
        
        try:
            # 从cookies中获取用户信息
            u = self.session.cookies.get('u', '')
            logger.info(f"用户ID: {u}")
            
            # 尝试获取用户详细信息
            user_apis = [
                "https://xueqiu.com/account/info.json",
                f"https://xueqiu.com/u/{u}.json",
                "https://xueqiu.com/user/info.json",
            ]
            
            for api_url in user_apis:
                try:
                    logger.info(f"\n🔗 {api_url}")
                    response = self.session.get(api_url, timeout=10)
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            logger.info(f"   ✅ 用户信息: {data}")
                            return data
                        except json.JSONDecodeError:
                            logger.info(f"   ⚠️ 非JSON响应")
                    else:
                        logger.info(f"   ❌ 状态码: {response.status_code}")
                        
                except Exception as e:
                    logger.info(f"   ❌ 异常: {e}")
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 检查用户信息失败: {e}")
            return None

def main():
    """主函数"""
    print("🎯 雪球模拟账户创建和持仓搜索")
    print("尝试创建模拟盈亏账户或寻找现有持仓")
    print("=" * 80)
    
    creator = XueqiuAccountCreator()
    
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'user_info': None,
        'create_results': [],
        'search_results': {},
    }
    
    # 1. 检查用户信息
    logger.info("\n👤 1. 检查用户信息")
    user_info = creator.check_user_info()
    all_results['user_info'] = user_info
    
    # 2. 尝试创建模拟账户
    logger.info("\n🔧 2. 尝试创建模拟账户")
    create_results = creator.try_create_simulation_account()
    all_results['create_results'] = create_results
    
    # 3. 搜索现有投资组合
    logger.info("\n🔍 3. 搜索现有投资组合")
    search_results = creator.search_existing_portfolios()
    all_results['search_results'] = search_results
    
    # 4. 汇总结果
    logger.info("\n📊 4. 结果汇总")
    logger.info("=" * 60)
    
    if user_info:
        logger.info(f"✅ 用户信息: 获取成功")
    else:
        logger.warning("⚠️ 用户信息: 获取失败")
    
    if create_results:
        logger.info(f"✅ 模拟账户创建: 找到 {len(create_results)} 个成功的API")
        for result in create_results:
            logger.info(f"   🎯 {result['api']}: {result['result']}")
    else:
        logger.warning("⚠️ 模拟账户创建: 所有API都失败")
    
    # 检查搜索结果
    found_portfolios = False
    for method, result in search_results.items():
        if isinstance(result, dict) and 'error' not in result and result:
            found_portfolios = True
            logger.info(f"✅ {method}: 找到投资组合数据")
    
    if not found_portfolios:
        logger.warning("⚠️ 投资组合搜索: 未找到现有投资组合")
    
    # 5. 保存结果
    try:
        with open('simulation_account_creation_results.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        logger.info(f"\n💾 完整结果已保存到: simulation_account_creation_results.json")
    except Exception as e:
        logger.error(f"\n❌ 保存结果失败: {e}")
    
    print(f"\n🎉 搜索和创建尝试完成！")
    
    # 给出建议
    if create_results:
        print(f"🎯 建议: 使用找到的API创建模拟账户")
    elif any('stocks' in str(result) for result in search_results.values()):
        print(f"📊 建议: 基于找到的自选股创建本地模拟账户")
    else:
        print(f"💡 建议: 手动在雪球网页上创建模拟盈亏账户，然后重新运行脚本")

if __name__ == '__main__':
    main()
