-- A股量化交易平台 - TimescaleDB数据库初始化脚本
-- 创建数据库、用户、扩展和基础表结构

-- 创建TimescaleDB扩展
CREATE EXTENSION IF NOT EXISTS timescaledb;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- ============================================================================
-- 1. 用户管理模块 (yonghu)
-- ============================================================================

-- 用户信息表
CREATE TABLE IF NOT EXISTS yonghu_xinxi (
    id SERIAL PRIMARY KEY,
    yonghuming VARCHAR(50) UNIQUE NOT NULL,
    youxiang VARCHAR(100) UNIQUE NOT NULL,
    mima_hash VARCHAR(255) NOT NULL,
    dingyue_leixing VARCHAR(20) DEFAULT 'jiben' CHECK (dingyue_leixing IN ('jiben', 'gaoji')),
    dingyue_kaishi DATE,
    dingyue_jieshu DATE,
    xukezheng_miyao VARCHAR(255) UNIQUE,
    shebei_id VARCHAR(255),
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW(),
    shi_huoyue BOOLEAN DEFAULT TRUE
);

-- 用户设置表
CREATE TABLE IF NOT EXISTS yonghu_shezhi (
    id SERIAL PRIMARY KEY,
    yonghu_id INTEGER REFERENCES yonghu_xinxi(id) ON DELETE CASCADE,
    shezhi_jian VARCHAR(100) NOT NULL,
    shezhi_zhi TEXT,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 2. 股票数据模块 (gupiao)
-- ============================================================================

-- 股票基本信息表
CREATE TABLE IF NOT EXISTS gupiao_jiben (
    id SERIAL PRIMARY KEY,
    ts_daima VARCHAR(20) UNIQUE NOT NULL,
    gupiao_daima VARCHAR(10) NOT NULL,
    gupiao_mingcheng VARCHAR(50) NOT NULL,
    diqu VARCHAR(20),
    hangye VARCHAR(50),
    shichang VARCHAR(20),
    shangshi_riqi DATE,
    shi_huoyue BOOLEAN DEFAULT TRUE,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- 股票日线数据表 (时序表)
CREATE TABLE IF NOT EXISTS gupiao_rixian (
    shijian TIMESTAMPTZ NOT NULL,
    ts_daima VARCHAR(20) NOT NULL,
    jiaoyi_riqi DATE NOT NULL,
    kaipan_jia DECIMAL(10,3),
    zuigao_jia DECIMAL(10,3),
    zuidi_jia DECIMAL(10,3),
    shoupan_jia DECIMAL(10,3),
    qian_shoupan DECIMAL(10,3),
    zhangdie_e DECIMAL(10,3),
    zhangdie_fu DECIMAL(8,4),
    chengjiao_liang BIGINT,
    chengjiao_e DECIMAL(20,2),
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- 技术指标表 (时序表)
CREATE TABLE IF NOT EXISTS jishu_zhibiao (
    shijian TIMESTAMPTZ NOT NULL,
    ts_daima VARCHAR(20) NOT NULL,
    jiaoyi_riqi DATE NOT NULL,
    zhibiao_mingcheng VARCHAR(50) NOT NULL,
    zhibiao_zhi DECIMAL(15,6),
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 3. 策略管理模块 (celue)
-- ============================================================================

-- 策略信息表
CREATE TABLE IF NOT EXISTS celue_xinxi (
    id SERIAL PRIMARY KEY,
    yonghu_id INTEGER REFERENCES yonghu_xinxi(id) ON DELETE CASCADE,
    celue_mingcheng VARCHAR(100) NOT NULL,
    celue_miaoshu TEXT,
    celue_leixing VARCHAR(50) CHECK (celue_leixing IN ('xitong', 'zidingyi')),
    celue_daima TEXT,
    canshu_peizhi JSONB,
    shi_huoyue BOOLEAN DEFAULT TRUE,
    shi_xitong BOOLEAN DEFAULT FALSE,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- 策略模板表
CREATE TABLE IF NOT EXISTS celue_moban (
    id SERIAL PRIMARY KEY,
    moban_mingcheng VARCHAR(100) NOT NULL,
    moban_fenlei VARCHAR(50),
    moban_miaoshu TEXT,
    moban_daima TEXT,
    moren_canshu JSONB,
    dingyue_dengji VARCHAR(20) DEFAULT 'jiben' CHECK (dingyue_dengji IN ('jiben', 'gaoji')),
    shi_huoyue BOOLEAN DEFAULT TRUE,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 4. 回测模块 (huice)
-- ============================================================================

-- 回测记录表
CREATE TABLE IF NOT EXISTS huice_jilu (
    id SERIAL PRIMARY KEY,
    yonghu_id INTEGER REFERENCES yonghu_xinxi(id) ON DELETE CASCADE,
    celue_id INTEGER REFERENCES celue_xinxi(id) ON DELETE CASCADE,
    huice_mingcheng VARCHAR(100),
    kaishi_riqi DATE NOT NULL,
    jieshu_riqi DATE NOT NULL,
    chushi_zijin DECIMAL(15,2),
    zuizhong_zijin DECIMAL(15,2),
    zongshou_yi DECIMAL(8,4),
    nianhua_shouyi DECIMAL(8,4),
    zuida_huiche DECIMAL(8,4),
    xiapulv DECIMAL(8,4),
    shenglv DECIMAL(8,4),
    zongjiao_yi_cishu INTEGER,
    zhuangtai VARCHAR(20) DEFAULT 'dengdai' CHECK (zhuangtai IN ('dengdai', 'yunxing', 'wancheng', 'shibai')),
    jieguo_shuju JSONB,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    wancheng_shijian TIMESTAMPTZ
);

-- 回测交易记录表 (时序表)
CREATE TABLE IF NOT EXISTS huice_jiaoyi (
    shijian TIMESTAMPTZ NOT NULL,
    huice_id INTEGER REFERENCES huice_jilu(id) ON DELETE CASCADE,
    ts_daima VARCHAR(20) NOT NULL,
    jiaoyi_riqi DATE NOT NULL,
    caozuo VARCHAR(10) NOT NULL CHECK (caozuo IN ('mai', 'mai')),
    shuliang INTEGER NOT NULL,
    jiage DECIMAL(10,3) NOT NULL,
    jine DECIMAL(15,2) NOT NULL,
    shouxufei DECIMAL(10,2),
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 5. 模拟交易模块 (moni_jiaoyi)
-- ============================================================================

-- 虚拟账户表
CREATE TABLE IF NOT EXISTS xuni_zhanghu (
    id SERIAL PRIMARY KEY,
    yonghu_id INTEGER REFERENCES yonghu_xinxi(id) ON DELETE CASCADE,
    zhanghu_mingcheng VARCHAR(100),
    chushi_zijin DECIMAL(15,2),
    dangqian_zijin DECIMAL(15,2),
    keyong_xianjin DECIMAL(15,2),
    shizhi DECIMAL(15,2),
    zongzi_chan DECIMAL(15,2),
    zongshou_yi DECIMAL(8,4),
    shi_huoyue BOOLEAN DEFAULT TRUE,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- 虚拟持仓表
CREATE TABLE IF NOT EXISTS xuni_chicang (
    id SERIAL PRIMARY KEY,
    zhanghu_id INTEGER REFERENCES xuni_zhanghu(id) ON DELETE CASCADE,
    ts_daima VARCHAR(20) NOT NULL,
    shuliang INTEGER NOT NULL,
    pingjun_chengben DECIMAL(10,3) NOT NULL,
    dangqian_jiage DECIMAL(10,3),
    shizhi DECIMAL(15,2),
    fudong_yingkui DECIMAL(15,2),
    yishixian_yingkui DECIMAL(15,2),
    gengxin_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- 虚拟订单表
CREATE TABLE IF NOT EXISTS xuni_dingdan (
    id SERIAL PRIMARY KEY,
    zhanghu_id INTEGER REFERENCES xuni_zhanghu(id) ON DELETE CASCADE,
    celue_id INTEGER REFERENCES celue_xinxi(id),
    ts_daima VARCHAR(20) NOT NULL,
    dingdan_leixing VARCHAR(20) NOT NULL CHECK (dingdan_leixing IN ('shijia', 'xianjia')),
    caozuo VARCHAR(10) NOT NULL CHECK (caozuo IN ('mai', 'mai')),
    shuliang INTEGER NOT NULL,
    jiage DECIMAL(10,3),
    zhuangtai VARCHAR(20) DEFAULT 'dengdai' CHECK (zhuangtai IN ('dengdai', 'chengjiao', 'quxiao', 'jujue')),
    chengjiao_shuliang INTEGER DEFAULT 0,
    chengjiao_jiage DECIMAL(10,3),
    shouxufei DECIMAL(10,2),
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW(),
    chengjiao_shijian TIMESTAMPTZ
);

-- ============================================================================
-- 6. 系统管理模块 (xitong)
-- ============================================================================

-- 系统日志表
CREATE TABLE IF NOT EXISTS xitong_rizhi (
    id SERIAL PRIMARY KEY,
    yonghu_id INTEGER REFERENCES yonghu_xinxi(id),
    rizhi_dengji VARCHAR(20) CHECK (rizhi_dengji IN ('DEBUG', 'INFO', 'WARNING', 'ERROR')),
    mokuai VARCHAR(50),
    xiaoxi TEXT,
    extra_shuju JSONB,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- 数据更新记录表
CREATE TABLE IF NOT EXISTS shuju_gengxin (
    id SERIAL PRIMARY KEY,
    shuju_leixing VARCHAR(50),
    gengxin_riqi DATE,
    zhuangtai VARCHAR(20) CHECK (zhuangtai IN ('chenggong', 'shibai')),
    jilu_shuliang INTEGER,
    cuowu_xiaoxi TEXT,
    chuangjian_shijian TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 创建时序表
-- ============================================================================

-- 将普通表转换为时序表
SELECT create_hypertable('gupiao_rixian', 'shijian', 
    chunk_time_interval => INTERVAL '1 month',
    if_not_exists => TRUE);

SELECT create_hypertable('jishu_zhibiao', 'shijian',
    chunk_time_interval => INTERVAL '1 month',
    if_not_exists => TRUE);

SELECT create_hypertable('huice_jiaoyi', 'shijian',
    chunk_time_interval => INTERVAL '1 month',
    if_not_exists => TRUE);

-- ============================================================================
-- 创建索引
-- ============================================================================

-- 用户相关索引
CREATE INDEX IF NOT EXISTS idx_yonghu_yonghuming ON yonghu_xinxi(yonghuming);
CREATE INDEX IF NOT EXISTS idx_yonghu_youxiang ON yonghu_xinxi(youxiang);
CREATE INDEX IF NOT EXISTS idx_yonghu_xukezheng ON yonghu_xinxi(xukezheng);
CREATE INDEX IF NOT EXISTS idx_yonghu_shezhi_yonghu_id ON yonghu_shezhi(yonghu_id);

-- 股票相关索引
CREATE INDEX IF NOT EXISTS idx_gupiao_ts_daima ON gupiao_jiben(ts_daima);
CREATE INDEX IF NOT EXISTS idx_gupiao_daima ON gupiao_jiben(gupiao_daima);
CREATE INDEX IF NOT EXISTS idx_gupiao_hangye ON gupiao_jiben(hangye);

-- 时序表索引
CREATE INDEX IF NOT EXISTS idx_gupiao_rixian_ts_daima_shijian 
    ON gupiao_rixian(ts_daima, shijian DESC);
CREATE INDEX IF NOT EXISTS idx_gupiao_rixian_jiaoyi_riqi 
    ON gupiao_rixian(jiaoyi_riqi);

CREATE INDEX IF NOT EXISTS idx_jishu_zhibiao_ts_daima_shijian 
    ON jishu_zhibiao(ts_daima, shijian DESC);
CREATE INDEX IF NOT EXISTS idx_jishu_zhibiao_mingcheng 
    ON jishu_zhibiao(zhibiao_mingcheng);

-- 策略相关索引
CREATE INDEX IF NOT EXISTS idx_celue_yonghu_id ON celue_xinxi(yonghu_id);
CREATE INDEX IF NOT EXISTS idx_celue_leixing ON celue_xinxi(celue_leixing);
CREATE INDEX IF NOT EXISTS idx_celue_canshu_peizhi ON celue_xinxi USING GIN(canshu_peizhi);

-- 回测相关索引
CREATE INDEX IF NOT EXISTS idx_huice_yonghu_id ON huice_jilu(yonghu_id);
CREATE INDEX IF NOT EXISTS idx_huice_celue_id ON huice_jilu(celue_id);
CREATE INDEX IF NOT EXISTS idx_huice_zhuangtai ON huice_jilu(zhuangtai);
CREATE INDEX IF NOT EXISTS idx_huice_jiaoyi_huice_id ON huice_jiaoyi(huice_id);

-- 模拟交易相关索引
CREATE INDEX IF NOT EXISTS idx_xuni_zhanghu_yonghu_id ON xuni_zhanghu(yonghu_id);
CREATE INDEX IF NOT EXISTS idx_xuni_chicang_zhanghu_id ON xuni_chicang(zhanghu_id);
CREATE INDEX IF NOT EXISTS idx_xuni_chicang_ts_daima ON xuni_chicang(ts_daima);
CREATE INDEX IF NOT EXISTS idx_xuni_dingdan_zhanghu_id ON xuni_dingdan(zhanghu_id);

-- ============================================================================
-- 创建唯一约束
-- ============================================================================

CREATE UNIQUE INDEX IF NOT EXISTS idx_gupiao_rixian_unique 
    ON gupiao_rixian(ts_daima, jiaoyi_riqi);

CREATE UNIQUE INDEX IF NOT EXISTS idx_jishu_zhibiao_unique 
    ON jishu_zhibiao(ts_daima, jiaoyi_riqi, zhibiao_mingcheng);

CREATE UNIQUE INDEX IF NOT EXISTS idx_xuni_chicang_unique 
    ON xuni_chicang(zhanghu_id, ts_daima);

-- ============================================================================
-- 插入初始数据
-- ============================================================================

-- 插入系统策略模板
INSERT INTO celue_moban (moban_mingcheng, moban_fenlei, moban_miaoshu, dingyue_dengji, shi_xitong) VALUES
('双均线策略', '趋势跟踪', '经典的双均线交叉策略，适合趋势市场', 'jiben', TRUE),
('RSI策略', '反转策略', '基于RSI指标的超买超卖策略', 'jiben', TRUE),
('MACD策略', '趋势跟踪', '基于MACD指标的趋势跟踪策略', 'jiben', TRUE),
('布林带策略', '均值回归', '基于布林带的均值回归策略', 'jiben', TRUE),
('ATR策略', '波动率策略', '基于ATR的波动率突破策略', 'jiben', TRUE),
('多因子选股', '量化选股', '基于多因子模型的选股策略', 'gaoji', TRUE),
('网格交易', '套利策略', '网格交易策略，适合震荡市场', 'gaoji', TRUE),
('配对交易', '统计套利', '基于协整关系的配对交易策略', 'gaoji', TRUE)
ON CONFLICT DO NOTHING;

-- 创建默认管理员用户
INSERT INTO yonghu_xinxi (yonghuming, youxiang, mima_hash, dingyue_leixing, dingyue_kaishi, dingyue_jieshu) VALUES
('admin', '<EMAIL>', crypt('admin123', gen_salt('bf')), 'gaoji', CURRENT_DATE, CURRENT_DATE + INTERVAL '1 year')
ON CONFLICT (yonghuming) DO NOTHING;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'TimescaleDB数据库初始化完成！';
    RAISE NOTICE '数据库名称: quantitative_trading';
    RAISE NOTICE '默认管理员: admin / admin123';
    RAISE NOTICE '时区设置: Asia/Shanghai';
    RAISE NOTICE '=================================================';
END $$;
