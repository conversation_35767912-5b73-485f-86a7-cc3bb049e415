# A股量化交易平台 - AI开发文档

## 📋 项目概述

### 基本信息
- **项目名称**: A股量化交易平台
- **项目类型**: 桌面应用 (Electron)
- **目标用户**: 个人投资者
- **市场范围**: A股市场
- **商业模式**: 订阅制按月收费
- **部署方式**: 本地私有化部署

### 核心特性
- **数据源**: TuShare (免费)
- **交易模式**: 模拟交易 (先期)
- **策略系统**: 用户可自定义 + 系统策略防泄露
- **更新机制**: 内置自动更新检查

## 🎯 需求变更记录

### v1.0 - 初始需求 (2024-08-21)
**确认的需求**:
- ✅ 目标群体: 个人投资者
- ✅ 市场范围: A股市场
- ✅ 商业模式: 订阅制按月收费
- ✅ 部署方式: 本地私有化部署
- ✅ 数据源: TuShare (免费)
- ✅ 交易模式: 先做模拟交易
- ✅ 桌面应用: Electron打包
- ✅ 定价策略: 基础版¥99/月，高级版¥199/月
- ✅ 更新机制: 内置自动更新检查
- ✅ 策略系统: 用户可自定义策略 + 系统策略防泄露

**待确认的需求**:
- ❓ 数据库选择: SQLite vs DuckDB vs 其他方案
- ❓ 开发规范: Python开发文档遵循
- ❓ 测试策略: test目录管理，测试后删除
- ❓ 项目管理: AIreadme.md记录所有变更

## 💻 技术架构

### 前端技术栈
- **框架**: Vue.js 3 + TypeScript
- **UI组件**: Element Plus
- **图表库**: ECharts
- **状态管理**: Pinia
- **构建工具**: Vite
- **桌面框架**: Electron

### 后端技术栈
- **框架**: FastAPI (异步高性能)
- **数据库**: SQLite (待确认) / DuckDB (备选)
- **ORM**: SQLAlchemy
- **数据处理**: Pandas + NumPy
- **技术指标**: TA-Lib
- **任务调度**: APScheduler
- **数据源**: TuShare API

### 开发工具
- **代码规范**: 遵循Python PEP 8
- **类型检查**: mypy
- **代码格式化**: black
- **导入排序**: isort
- **测试框架**: pytest
- **文档生成**: Sphinx

## 📊 功能模块

### 核心功能
1. **用户管理**: 注册/登录/订阅管理/许可证验证
2. **数据管理**: TuShare数据获取/存储/更新
3. **策略管理**: 策略编辑器/模板库/参数配置
4. **回测系统**: 历史数据回测/性能分析/报告生成
5. **模拟交易**: 虚拟账户/订单管理/风险控制
6. **监控分析**: 实时监控/性能分析/报表生成

### 订阅功能分级
**基础版 (¥99/月)**:
- 5个策略模板
- 单策略回测
- 基础技术指标 (20个)
- 模拟交易 (10万虚拟资金)

**高级版 (¥199/月)**:
- 20+策略模板
- 多策略并行回测
- 高级技术指标 (50+个)
- 策略优化工具
- 无限模拟资金
- 实时预警功能

## 🗄️ 数据库方案对比

### 方案1: SQLite ⭐⭐⭐⭐⭐
**优势**: 零配置、高性能、跨平台、易部署
**劣势**: 并发写入限制
**适用**: 个人用户、本地部署

### 方案2: DuckDB ⭐⭐⭐⭐
**优势**: 分析性能极佳、列式存储、零配置
**劣势**: 生态相对较新
**适用**: 复杂分析查询场景

### 方案3: LevelDB ⭐⭐⭐
**优势**: 写入性能高、数据压缩好
**劣势**: 无SQL支持、需自实现索引
**适用**: 高频写入场景

## 📁 项目结构规范

```
quantitative-trading-platform/
├── README.md                 # 用户文档
├── AIreadme.md              # AI开发文档 (本文件)
├── requirements.txt         # Python依赖
├── pyproject.toml          # 项目配置
├── .gitignore              # Git忽略文件
├── .pre-commit-config.yaml # 代码检查配置
├── 
├── src/                    # 源代码目录
│   ├── backend/           # 后端代码
│   │   ├── __init__.py
│   │   ├── main.py       # FastAPI应用入口
│   │   ├── api/          # API路由
│   │   ├── core/         # 核心业务逻辑
│   │   ├── database/     # 数据库相关
│   │   ├── models/       # 数据模型
│   │   ├── services/     # 业务服务
│   │   └── utils/        # 工具函数
│   │
│   ├── frontend/         # 前端代码
│   │   ├── src/
│   │   ├── public/
│   │   ├── package.json
│   │   └── vite.config.ts
│   │
│   └── electron/         # Electron主进程
│       ├── main.js
│       └── preload.js
│
├── test/                 # 测试目录
│   ├── __init__.py
│   ├── conftest.py      # pytest配置
│   ├── unit/            # 单元测试
│   ├── integration/     # 集成测试
│   └── e2e/             # 端到端测试
│
├── docs/                # 文档目录
│   ├── api/            # API文档
│   ├── user/           # 用户手册
│   └── dev/            # 开发文档
│
├── scripts/            # 构建和部署脚本
│   ├── build.py       # 构建脚本
│   ├── deploy.py      # 部署脚本
│   └── data_init.py   # 数据初始化
│
└── assets/            # 静态资源
    ├── icons/         # 图标文件
    ├── images/        # 图片资源
    └── data/          # 示例数据
```

## 🔧 开发规范

### Python代码规范
- **PEP 8**: 严格遵循Python官方代码风格
- **类型注解**: 所有函数必须有类型注解
- **文档字符串**: 使用Google风格的docstring
- **命名规范**: 
  - 变量/函数: snake_case
  - 类名: PascalCase
  - 常量: UPPER_CASE
  - 私有成员: _leading_underscore

### 代码质量工具
```toml
# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
```

### 测试规范
- **测试覆盖率**: 目标90%以上
- **测试命名**: test_功能_场景_期望结果
- **测试文件**: 测试完成后自动删除临时文件
- **测试数据**: 使用fixtures管理测试数据

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 🚀 开发计划

### Phase 1: 项目搭建 (Week 1-2)
- [ ] 项目结构初始化
- [ ] 开发环境配置
- [ ] 数据库方案确定
- [ ] 基础框架搭建

### Phase 2: 核心功能 (Week 3-6)
- [ ] 用户管理系统
- [ ] TuShare数据接入
- [ ] 策略管理模块
- [ ] 回测引擎开发

### Phase 3: 界面开发 (Week 7-8)
- [ ] 前端界面开发
- [ ] Electron集成
- [ ] 用户体验优化

### Phase 4: 测试部署 (Week 9-10)
- [ ] 全面测试
- [ ] 性能优化
- [ ] 打包部署
- [ ] 文档完善

## 📝 变更日志

### 2024-08-21
**新增需求**:
- 确认使用Electron桌面应用
- 确认订阅制定价策略
- 确认策略系统设计 (用户自定义 + 系统防泄露)
- 新增开发规范要求
- 新增测试管理要求
- 新增AIreadme.md管理要求

**待确认**:
- 数据库最终选择
- 具体开发时间计划
- 团队分工安排

## 🤔 当前待解决问题

1. **数据库选择**: SQLite vs DuckDB vs 其他方案
2. **开发团队**: 团队规模和技能分工
3. **开发周期**: 具体的里程碑时间点
4. **测试策略**: 自动化测试的具体实施方案
5. **部署方案**: 安装包制作和分发机制

## 📞 下一步行动

1. **确认数据库方案**
2. **搭建项目基础框架**
3. **配置开发环境和工具链**
4. **开始核心模块开发**

---

**注意**: 本文档将记录所有需求变更和技术决策，每次修改都会更新版本和变更日志，确保AI助手能够准确理解项目当前状态和历史变更。
