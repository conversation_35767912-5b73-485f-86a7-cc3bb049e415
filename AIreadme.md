# A股量化交易平台 - AI开发文档

## 📋 项目概述

### 基本信息
- **项目名称**: A股量化交易平台
- **项目类型**: 桌面应用 (Electron)
- **目标用户**: 个人投资者
- **市场范围**: A股市场
- **商业模式**: 订阅制按月收费
- **部署方式**: 本地私有化部署

### 核心特性
- **数据源**: TuShare (免费)
- **交易模式**: 模拟交易 (先期)
- **策略系统**: 用户可自定义 + 系统策略防泄露
- **更新机制**: 内置自动更新检查

## 🎯 需求变更记录

### v1.0 - 初始需求 (2024-08-21)
**确认的需求**:
- ✅ 目标群体: 个人投资者
- ✅ 市场范围: A股市场
- ✅ 商业模式: 订阅制按月收费
- ✅ 部署方式: 本地私有化部署
- ✅ 数据源: TuShare (免费)
- ✅ 交易模式: 先做模拟交易
- ✅ 桌面应用: Electron打包
- ✅ 定价策略: 基础版¥99/月，高级版¥199/月
- ✅ 更新机制: 内置自动更新检查
- ✅ 策略系统: 用户可自定义策略 + 系统策略防泄露

**待确认的需求**:
- ❓ 数据库选择: SQLite vs DuckDB vs 其他方案
- ❓ 开发规范: Python开发文档遵循
- ❓ 测试策略: test目录管理，测试后删除
- ❓ 项目管理: AIreadme.md记录所有变更

## 💻 技术架构

### 前端技术栈
- **框架**: Vue.js 3 + TypeScript
- **UI组件**: Element Plus
- **图表库**: ECharts
- **状态管理**: Pinia
- **构建工具**: Vite
- **桌面框架**: Electron

### 后端技术栈
- **框架**: FastAPI (异步高性能)
- **数据库**: TimescaleDB (PostgreSQL扩展)
- **ORM**: SQLAlchemy + asyncpg
- **数据处理**: Pandas + NumPy
- **技术指标**: TA-Lib
- **任务调度**: APScheduler
- **数据源**: TuShare API
- **容器化**: Docker + Docker Compose
- **连接池**: asyncpg连接池

### 开发工具
- **代码规范**: 遵循Python PEP 8
- **类型检查**: mypy
- **代码格式化**: black
- **导入排序**: isort
- **测试框架**: pytest
- **文档生成**: Sphinx

## 📊 功能模块

### 核心功能
1. **用户管理**: 注册/登录/订阅管理/许可证验证
2. **数据管理**: TuShare数据获取/存储/更新
3. **策略管理**: 策略编辑器/模板库/参数配置
4. **回测系统**: 历史数据回测/性能分析/报告生成
5. **模拟交易**: 虚拟账户/订单管理/风险控制
6. **监控分析**: 实时监控/性能分析/报表生成

### 订阅功能分级
**基础版 (¥99/月)**:
- 5个策略模板
- 单策略回测
- 基础技术指标 (20个)
- 模拟交易 (10万虚拟资金)

**高级版 (¥199/月)**:
- 20+策略模板
- 多策略并行回测
- 高级技术指标 (50+个)
- 策略优化工具
- 无限模拟资金
- 实时预警功能

## 🗄️ 数据库方案对比

### ✅ 已选择方案: TimescaleDB (PostgreSQL扩展) ⭐⭐⭐⭐⭐
**优势**:
- 专业时序数据库，完美适配股票数据
- 完整SQL支持，查询功能强大
- 高性能时序查询和聚合
- 自动分区和数据压缩
- 成熟的PostgreSQL生态系统
- 支持复杂的金融数据分析

**劣势**:
- 需要PostgreSQL服务器部署
- 相比SQLite部署复杂度较高
- 资源消耗相对较大

**选择理由**:
- 股票数据本质上是时序数据
- 需要高性能的历史数据查询
- 支持复杂的金融指标计算
- 专业量化交易平台的标准选择

### 备选方案记录
- **SQLite**: 轻量级，但不适合大量时序数据
- **DuckDB**: 分析性能好，但时序功能不如TimescaleDB
- **ClickHouse**: 性能极佳，但部署复杂度更高

## 📁 项目结构规范 (拼音命名)

```
quantitative-trading-platform/
├── README.md                    # 用户文档
├── AIreadme.md                 # AI开发文档 (本文件)
├── requirements.txt            # Python依赖
├── pyproject.toml             # 项目配置
├── docker-compose.yml         # TimescaleDB容器配置
├── .gitignore                 # Git忽略文件
├── .pre-commit-config.yaml    # 代码检查配置
├──
├── src/                       # 源代码目录
│   ├── houduan/              # 后端代码 (backend)
│   │   ├── __init__.py
│   │   ├── main.py          # FastAPI应用入口
│   │   ├── api/             # API路由
│   │   │   ├── yonghu/      # 用户相关API (user)
│   │   │   ├── celue/       # 策略相关API (strategy)
│   │   │   ├── shuju/       # 数据相关API (data)
│   │   │   ├── jiaoyi/      # 交易相关API (trading)
│   │   │   └── huice/       # 回测相关API (backtest)
│   │   ├── hexin/           # 核心业务逻辑 (core)
│   │   │   ├── celue_yinqing/    # 策略引擎 (strategy_engine)
│   │   │   ├── huice_yinqing/    # 回测引擎 (backtest_engine)
│   │   │   ├── jiaoyi_yinqing/   # 交易引擎 (trading_engine)
│   │   │   ├── shuju_chuli/      # 数据处理 (data_processing)
│   │   │   └── fengxian_kongzhi/ # 风险控制 (risk_management)
│   │   ├── shujuku/         # 数据库相关 (database)
│   │   │   ├── lianjie/     # 数据库连接 (connection)
│   │   │   ├── qianyi/      # 数据迁移 (migration)
│   │   │   └── cangku/      # 数据仓库 (repository)
│   │   ├── moxing/          # 数据模型 (models)
│   │   │   ├── yonghu/      # 用户模型 (user)
│   │   │   ├── gupiao/      # 股票模型 (stock)
│   │   │   ├── celue/       # 策略模型 (strategy)
│   │   │   ├── jiaoyi/      # 交易模型 (trading)
│   │   │   └── huice/       # 回测模型 (backtest)
│   │   ├── fuwu/            # 业务服务 (services)
│   │   │   ├── tushare_fuwu/     # TuShare服务 (tushare_service)
│   │   │   ├── yonghu_fuwu/      # 用户服务 (user_service)
│   │   │   ├── celue_fuwu/       # 策略服务 (strategy_service)
│   │   │   ├── jiaoyi_fuwu/      # 交易服务 (trading_service)
│   │   │   └── tongzhi_fuwu/     # 通知服务 (notification_service)
│   │   └── gongju/          # 工具函数 (utils)
│   │       ├── jiami/       # 加密工具 (encryption)
│   │       ├── riqi/        # 日期工具 (datetime)
│   │       ├── yanzheng/    # 验证工具 (validation)
│   │       └── rizhi/       # 日志工具 (logging)
│   │
│   ├── qianduan/            # 前端代码 (frontend)
│   │   ├── src/
│   │   │   ├── zujian/      # 组件 (components)
│   │   │   ├── yemian/      # 页面 (pages)
│   │   │   ├── shangdian/   # 状态管理 (store)
│   │   │   ├── fuwu/        # 前端服务 (services)
│   │   │   └── gongju/      # 前端工具 (utils)
│   │   ├── public/
│   │   ├── package.json
│   │   └── vite.config.ts
│   │
│   └── electron/            # Electron主进程
│       ├── main.js
│       └── preload.js
│
├── ceshi/                   # 测试目录 (test)
│   ├── __init__.py
│   ├── conftest.py         # pytest配置
│   ├── danwei/             # 单元测试 (unit)
│   ├── jicheng/            # 集成测试 (integration)
│   └── duanduan/           # 端到端测试 (e2e)
│
├── wendang/                # 文档目录 (docs)
│   ├── api/               # API文档
│   ├── yonghu/            # 用户手册 (user)
│   └── kaifa/             # 开发文档 (dev)
│
├── jiaoben/               # 构建和部署脚本 (scripts)
│   ├── gouzhu.py         # 构建脚本 (build)
│   ├── bushu.py          # 部署脚本 (deploy)
│   ├── shuju_chushihua.py # 数据初始化 (data_init)
│   └── shujuku_qianyi.py  # 数据库迁移 (db_migration)
│
└── ziyuan/               # 静态资源 (assets)
    ├── tubiao/           # 图标文件 (icons)
    ├── tupian/           # 图片资源 (images)
    └── shuju/            # 示例数据 (data)
```

### 拼音命名规范说明
- **houduan** (后端): backend
- **qianduan** (前端): frontend
- **yonghu** (用户): user
- **celue** (策略): strategy
- **shuju** (数据): data
- **jiaoyi** (交易): trading
- **huice** (回测): backtest
- **hexin** (核心): core
- **shujuku** (数据库): database
- **moxing** (模型): models
- **fuwu** (服务): services
- **gongju** (工具): utils
- **ceshi** (测试): test
- **wendang** (文档): docs
- **jiaoben** (脚本): scripts
- **ziyuan** (资源): assets

## 🔧 开发规范

### Python代码规范
- **PEP 8**: 严格遵循Python官方代码风格
- **类型注解**: 所有函数必须有类型注解
- **文档字符串**: 使用Google风格的docstring
- **命名规范**: 
  - 变量/函数: snake_case
  - 类名: PascalCase
  - 常量: UPPER_CASE
  - 私有成员: _leading_underscore

### 代码质量工具
```toml
# pyproject.toml
[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
```

### 测试规范
- **测试覆盖率**: 目标90%以上
- **测试命名**: test_功能_场景_期望结果
- **测试文件**: 测试完成后自动删除临时文件
- **测试数据**: 使用fixtures管理测试数据

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 🚀 开发计划

### Phase 1: 项目搭建 (Week 1-2)
- [ ] 项目结构初始化
- [ ] 开发环境配置
- [ ] 数据库方案确定
- [ ] 基础框架搭建

### Phase 2: 核心功能 (Week 3-6)
- [ ] 用户管理系统
- [ ] TuShare数据接入
- [ ] 策略管理模块
- [ ] 回测引擎开发

### Phase 3: 界面开发 (Week 7-8)
- [ ] 前端界面开发
- [ ] Electron集成
- [ ] 用户体验优化

### Phase 4: 测试部署 (Week 9-10)
- [ ] 全面测试
- [ ] 性能优化
- [ ] 打包部署
- [ ] 文档完善

## 📝 变更日志

### 2024-08-21 v1.2 - 前端页面系统完成
**新增功能**:
- ✅ Vue.js 3 + TypeScript + Element Plus 前端框架
- ✅ 响应式布局系统 (支持PC/平板/手机)
- ✅ 完整的路由配置和导航系统
- ✅ Pinia状态管理 (用户状态、应用状态)
- ✅ 仪表板页面 (资产统计、图表展示、实时数据)
- ✅ 主题切换 (浅色/深色/自动)
- ✅ 完整的样式系统 (SCSS变量、混合器、工具类)
- ✅ HTTP请求封装 (拦截器、错误处理、加载状态)
- ✅ TypeScript类型定义系统

### 2024-08-21 v1.1 - 数据库和项目结构确认
**新增需求**:
- ✅ 确认数据库选择: TimescaleDB (PostgreSQL扩展)
- ✅ 确认项目结构: 拼音命名规范
- ✅ 确认模块化设计: 每个模块独立文件夹
- ✅ 确认容器化部署: Docker + Docker Compose

**技术栈更新**:
- 数据库: SQLite → TimescaleDB
- 部署方式: 本地文件 → Docker容器
- 连接方式: 同步 → 异步 (asyncpg)

### 2024-08-21 v1.0 - 初始需求确认
**确认需求**:
- ✅ 确认使用Electron桌面应用
- ✅ 确认订阅制定价策略
- ✅ 确认策略系统设计 (用户自定义 + 系统防泄露)
- ✅ 新增开发规范要求
- ✅ 新增测试管理要求
- ✅ 新增AIreadme.md管理要求

**已解决**:
- ✅ 数据库选择: TimescaleDB
- ✅ 项目结构: 拼音命名规范
- ✅ 模块化设计: 独立文件夹结构

## ✅ 已完成的工作

### 2024-08-21 - 项目基础搭建完成
1. **✅ 数据库方案确定**: TimescaleDB (PostgreSQL扩展)
2. **✅ 项目结构创建**: 拼音命名规范，模块化设计
3. **✅ Docker配置完成**: TimescaleDB + Redis + pgAdmin
4. **✅ 数据库设计完成**: 完整的表结构和索引设计
5. **✅ 开发规范建立**: Python PEP 8 + 类型注解 + 测试规范
6. **✅ 项目配置完成**: pyproject.toml + pre-commit + 测试配置

### 项目文件清单

#### 后端基础设施
- ✅ `docker-compose.yml` - 容器化部署配置
- ✅ `TimescaleDB数据库设计.md` - 完整数据库设计文档
- ✅ `jiaoben/shujuku_chushihua.sql` - 数据库初始化脚本
- ✅ `jiaoben/chuangjian_xiangmu_jiegou.py` - 项目结构创建脚本
- ✅ `pyproject.toml` - Python项目配置 (支持TimescaleDB)
- ✅ `.pre-commit-config.yaml` - 代码质量检查配置
- ✅ `ceshi/conftest.py` - 测试框架配置
- ✅ 完整的拼音命名目录结构

#### 前端页面系统
- ✅ `src/qianduan/package.json` - 前端依赖配置
- ✅ `src/qianduan/vite.config.ts` - Vite构建配置
- ✅ `src/qianduan/src/main.ts` - 应用入口文件
- ✅ `src/qianduan/src/App.vue` - 根组件
- ✅ `src/qianduan/src/router/index.ts` - 路由配置
- ✅ `src/qianduan/src/shangdian/` - Pinia状态管理
- ✅ `src/qianduan/src/yemian/buju/ZhuBuju.vue` - 主布局组件
- ✅ `src/qianduan/src/yemian/yibiaopan/YibiaopanYemian.vue` - 仪表板页面
- ✅ `src/qianduan/src/styles/` - 完整样式系统
- ✅ `src/qianduan/src/types/` - TypeScript类型定义
- ✅ `src/qianduan/src/fuwu/` - API服务封装

## 🤔 当前待解决问题

1. **✅ 数据库选择**: 已确定TimescaleDB
2. **开发团队**: 团队规模和技能分工
3. **开发周期**: 具体的里程碑时间点
4. **✅ 测试策略**: 已建立完整测试框架
5. **部署方案**: 安装包制作和分发机制

## 📞 下一步行动

1. **✅ 确认数据库方案** - 已完成
2. **✅ 搭建项目基础框架** - 已完成
3. **✅ 配置开发环境和工具链** - 已完成
4. **✅ 前端页面开发** - 已完成基础框架
   - ✅ Vue.js 3 + TypeScript + Element Plus
   - ✅ 路由配置和状态管理
   - ✅ 主布局和仪表板页面
   - ✅ 样式系统和组件库
5. **🔄 后端核心模块开发** - 待开始
   - 数据库连接模块
   - TuShare数据服务
   - 用户管理系统
   - 策略管理模块

---

**注意**: 本文档将记录所有需求变更和技术决策，每次修改都会更新版本和变更日志，确保AI助手能够准确理解项目当前状态和历史变更。
