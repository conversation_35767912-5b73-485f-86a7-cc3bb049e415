<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑支付渠道' : '添加支付渠道'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="渠道名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入渠道名称" />
      </el-form-item>
      
      <el-form-item label="渠道类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择渠道类型" style="width: 100%">
          <el-option label="支付宝" value="alipay" />
          <el-option label="微信支付" value="wechat" />
          <el-option label="银行卡" value="bank" />
          <el-option label="余额支付" value="balance" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="手续费率" prop="fee_rate">
        <el-input-number 
          v-model="form.fee_rate" 
          :min="0" 
          :max="10" 
          :precision="2"
          style="width: 100%"
        />
        <span style="margin-left: 8px;">%</span>
      </el-form-item>
      
      <el-form-item label="日限额" prop="daily_limit">
        <el-input-number 
          v-model="form.daily_limit" 
          :min="1000" 
          :step="1000"
          style="width: 100%"
        />
        <span style="margin-left: 8px;">元</span>
      </el-form-item>
      
      <el-form-item label="月限额" prop="monthly_limit">
        <el-input-number 
          v-model="form.monthly_limit" 
          :min="10000" 
          :step="10000"
          style="width: 100%"
        />
        <span style="margin-left: 8px;">元</span>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="active">启用</el-radio>
          <el-radio label="inactive">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
  channel?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  channel: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref()
const submitting = ref(false)

const form = ref({
  name: '',
  type: '',
  fee_rate: 0.6,
  daily_limit: 1000000,
  monthly_limit: 30000000,
  status: 'active'
})

const rules = {
  name: [
    { required: true, message: '请输入渠道名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择渠道类型', trigger: 'change' }
  ],
  fee_rate: [
    { required: true, message: '请输入手续费率', trigger: 'blur' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.channel)

// 方法
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '渠道更新成功' : '渠道添加成功')
    emit('refresh')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  formRef.value?.resetFields()
  visible.value = false
}

// 监听器
watch(() => props.channel, (newChannel) => {
  if (newChannel) {
    form.value = { ...newChannel }
  } else {
    form.value = {
      name: '',
      type: '',
      fee_rate: 0.6,
      daily_limit: 1000000,
      monthly_limit: 30000000,
      status: 'active'
    }
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
