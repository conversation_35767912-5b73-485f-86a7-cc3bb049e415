# A股量化交易平台

## 📊 项目简介

专为个人投资者设计的A股量化交易平台，提供策略开发、回测分析、模拟交易等功能。

### 🎯 核心特性

- **策略开发**: 可视化策略编辑器 + 代码编辑
- **历史回测**: 基于TimescaleDB的高性能回测
- **模拟交易**: 虚拟资金模拟真实交易环境
- **数据管理**: TuShare数据源，自动更新
- **风险控制**: 多层次风险管理机制

### 💻 技术架构

- **后端**: FastAPI + TimescaleDB + Redis
- **前端**: Vue.js 3 + TypeScript + Element Plus
- **桌面**: Electron跨平台桌面应用
- **部署**: Docker + Docker Compose

### 🚀 快速开始

1. **环境准备**
   ```bash
   # 安装Python依赖
   pip install -e .
   
   # 启动数据库
   docker-compose up -d timescaledb redis
   ```

2. **数据库初始化**
   ```bash
   # 运行初始化脚本
   python jiaoben/shujuku_qianyi.py
   ```

3. **启动服务**
   ```bash
   # 启动后端服务
   python src/houduan/main.py
   
   # 启动前端开发服务器
   cd src/qianduan && npm run dev
   ```

### 📁 项目结构

```
├── src/houduan/          # 后端代码
├── src/qianduan/         # 前端代码
├── src/electron/         # Electron主进程
├── ceshi/               # 测试代码
├── wendang/             # 项目文档
├── jiaoben/             # 构建脚本
└── ziyuan/              # 静态资源
```

### 📖 文档

- [API文档](wendang/api/)
- [用户手册](wendang/yonghu/)
- [开发文档](wendang/kaifa/)

### 📄 许可证

MIT License

### 🤝 贡献

欢迎提交Issue和Pull Request！
