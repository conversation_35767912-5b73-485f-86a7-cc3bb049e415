#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
专业股票交易客户端
参考同花顺界面设计，基于pysnowball和统一交易引擎
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import json
import logging
import os
from datetime import datetime
from unified_trading_engine import UnifiedTradingEngine, TradingMode

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

# 全局交易引擎实例
trading_engine = None

def init_trading_engine():
    """初始化交易引擎"""
    global trading_engine
    try:
        trading_engine = UnifiedTradingEngine()
        success = trading_engine.initialize()
        if success:
            logging.info("✅ 交易引擎初始化成功")
        else:
            logging.error("❌ 交易引擎初始化失败")
        return success
    except Exception as e:
        logging.error(f"❌ 交易引擎初始化异常: {e}")
        return False

@app.route('/')
def index():
    """主页 - 专业交易界面"""
    # 直接返回HTML内容，避免模板文件依赖
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业股票交易客户端</title>
    <link rel="stylesheet" href="/static/trading.css">
</head>
<body>
    <div id="app">
        <div class="trading-header">
            <div class="header-left">
                <h1>📈 专业股票交易客户端</h1>
                <div class="mode-switch">
                    <label>
                        <input type="radio" name="mode" value="simulation" checked> 模拟交易
                    </label>
                    <label>
                        <input type="radio" name="mode" value="real"> 真实交易
                    </label>
                </div>
            </div>
            <div class="header-right">
                <div class="status-indicator" id="engineStatus">
                    <span class="status-dot"></span>
                    <span class="status-text">未连接</span>
                </div>
                <button class="btn btn-primary" onclick="initializeEngine()">初始化引擎</button>
            </div>
        </div>

        <div class="trading-main">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <!-- 股票搜索 -->
                <div class="panel search-panel">
                    <h3>🔍 股票搜索</h3>
                    <div class="search-box">
                        <input type="text" id="searchInput" placeholder="输入股票代码或名称">
                        <button onclick="searchStock()">搜索</button>
                    </div>
                    <div id="searchResults" class="search-results"></div>
                </div>

                <!-- 账户信息 -->
                <div class="panel account-panel">
                    <h3>💰 账户信息</h3>
                    <div id="accountInfo" class="account-info">
                        <div class="loading">加载中...</div>
                    </div>
                </div>

                <!-- 持仓信息 -->
                <div class="panel position-panel">
                    <h3>📋 持仓信息</h3>
                    <div id="positionInfo" class="position-info">
                        <div class="loading">加载中...</div>
                    </div>
                </div>
            </div>

            <!-- 中间面板 -->
            <div class="center-panel">
                <!-- 行情显示 -->
                <div class="panel quote-panel">
                    <h3>📊 实时行情</h3>
                    <div class="stock-selector">
                        <input type="text" id="currentSymbol" value="SH600519" placeholder="股票代码">
                        <button onclick="loadQuote()">刷新行情</button>
                    </div>
                    <div id="quoteDisplay" class="quote-display">
                        <div class="loading">请输入股票代码</div>
                    </div>
                </div>

                <!-- 五档行情 -->
                <div class="panel pankou-panel">
                    <h3>📈 五档买卖盘</h3>
                    <div id="pankouDisplay" class="pankou-display">
                        <div class="loading">加载中...</div>
                    </div>
                </div>

                <!-- K线图 -->
                <div class="panel chart-panel">
                    <h3>📉 K线图</h3>
                    <div id="chartDisplay" class="chart-display">
                        <div class="chart-placeholder">K线图显示区域</div>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="right-panel">
                <!-- 交易下单 -->
                <div class="panel trade-panel">
                    <h3>🔄 交易下单</h3>
                    <div class="trade-form">
                        <div class="form-group">
                            <label>股票代码:</label>
                            <input type="text" id="tradeSymbol" placeholder="如: SH600519">
                        </div>
                        <div class="form-group">
                            <label>交易价格:</label>
                            <input type="number" id="tradePrice" step="0.01" placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label>交易数量:</label>
                            <input type="number" id="tradeAmount" placeholder="100">
                        </div>
                        <div class="trade-buttons">
                            <button class="btn btn-buy" onclick="buyStock()">买入</button>
                            <button class="btn btn-sell" onclick="sellStock()">卖出</button>
                        </div>
                    </div>
                </div>

                <!-- 交易记录 -->
                <div class="panel history-panel">
                    <h3>📝 交易记录</h3>
                    <div id="tradeHistory" class="trade-history">
                        <div class="no-data">暂无交易记录</div>
                    </div>
                </div>

                <!-- 系统日志 -->
                <div class="panel log-panel">
                    <h3>📋 系统日志</h3>
                    <div id="systemLog" class="system-log">
                        <div class="log-entry">系统启动...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/trading.js"></script>
</body>
</html>
    '''

@app.route('/api/engine/status')
def get_engine_status():
    """获取引擎状态"""
    try:
        if trading_engine:
            status = trading_engine.get_status()
            return jsonify({
                'success': True,
                'status': status
            })
        else:
            return jsonify({
                'success': False,
                'error': '交易引擎未初始化'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/engine/initialize', methods=['POST'])
def initialize_engine():
    """初始化交易引擎"""
    try:
        success = init_trading_engine()
        return jsonify({
            'success': success,
            'message': '交易引擎初始化成功' if success else '交易引擎初始化失败'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/engine/switch_mode', methods=['POST'])
def switch_trading_mode():
    """切换交易模式"""
    try:
        data = request.get_json()
        mode = data.get('mode')
        
        if not trading_engine:
            return jsonify({
                'success': False,
                'error': '交易引擎未初始化'
            })
        
        success = trading_engine.switch_mode(mode)
        return jsonify({
            'success': success,
            'message': f'切换到{mode}模式成功' if success else '切换模式失败'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/quote/<symbol>')
def get_quote(symbol):
    """获取股票行情"""
    try:
        if not trading_engine:
            return jsonify({
                'success': False,
                'error': '交易引擎未初始化'
            })
        
        quote = trading_engine.get_quote(symbol)
        if quote:
            return jsonify({
                'success': True,
                'quote': quote
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取行情失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/pankou/<symbol>')
def get_pankou(symbol):
    """获取五档行情"""
    try:
        if not trading_engine:
            return jsonify({
                'success': False,
                'error': '交易引擎未初始化'
            })
        
        pankou = trading_engine.get_pankou(symbol)
        if pankou:
            return jsonify({
                'success': True,
                'pankou': pankou
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取五档行情失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/account/balance')
def get_balance():
    """获取账户余额"""
    try:
        if not trading_engine:
            return jsonify({
                'success': False,
                'error': '交易引擎未初始化'
            })
        
        balance = trading_engine.get_balance()
        if balance:
            return jsonify({
                'success': True,
                'balance': balance
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取账户余额失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/account/position')
def get_position():
    """获取持仓信息"""
    try:
        if not trading_engine:
            return jsonify({
                'success': False,
                'error': '交易引擎未初始化'
            })
        
        position = trading_engine.get_position()
        if position is not None:
            return jsonify({
                'success': True,
                'position': position
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取持仓信息失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/trade/buy', methods=['POST'])
def buy_stock():
    """买入股票"""
    try:
        if not trading_engine:
            return jsonify({
                'success': False,
                'error': '交易引擎未初始化'
            })
        
        data = request.get_json()
        symbol = data.get('symbol')
        price = float(data.get('price'))
        amount = int(data.get('amount'))
        
        result = trading_engine.buy(symbol, price, amount)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/trade/sell', methods=['POST'])
def sell_stock():
    """卖出股票"""
    try:
        if not trading_engine:
            return jsonify({
                'success': False,
                'error': '交易引擎未初始化'
            })
        
        data = request.get_json()
        symbol = data.get('symbol')
        price = float(data.get('price'))
        amount = int(data.get('amount'))
        
        result = trading_engine.sell(symbol, price, amount)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/search/stock/<query>')
def search_stock(query):
    """搜索股票"""
    try:
        # 使用pysnowball搜索股票
        import pysnowball as ball
        result = ball.suggest_stock(query)
        
        if result and 'data' in result:
            return jsonify({
                'success': True,
                'stocks': result['data']
            })
        else:
            return jsonify({
                'success': False,
                'error': '搜索失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/kline/<symbol>')
def get_kline(symbol):
    """获取K线数据"""
    try:
        period = request.args.get('period', 'day')
        count = int(request.args.get('count', 30))
        
        # 使用pysnowball获取K线数据
        import pysnowball as ball
        result = ball.kline(symbol, period, count)
        
        if result and 'data' in result:
            return jsonify({
                'success': True,
                'kline': result['data']
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取K线数据失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('static', filename)

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'API端点不存在'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': '服务器内部错误'
    }), 500

def create_static_directory():
    """创建静态文件目录"""
    static_dir = 'static'
    if not os.path.exists(static_dir):
        os.makedirs(static_dir)
        logging.info(f"✅ 创建静态文件目录: {static_dir}")

def main():
    """主函数"""
    print("🚀 专业股票交易客户端")
    print("参考同花顺界面设计，基于pysnowball和统一交易引擎")
    print("=" * 60)
    
    # 创建必要的目录
    create_static_directory()
    
    # 初始化交易引擎
    print("🔧 初始化交易引擎...")
    if init_trading_engine():
        print("✅ 交易引擎初始化成功")
    else:
        print("⚠️ 交易引擎初始化失败，部分功能可能不可用")
    
    print("\n🌐 启动Web服务器...")
    print("访问地址: http://localhost:5004")
    print("功能特点:")
    print("  📊 专业交易界面 (参考同花顺设计)")
    print("  📈 实时行情显示")
    print("  📋 五档买卖盘")
    print("  💰 账户资金管理")
    print("  🔄 模拟/真实交易切换")
    print("  📱 响应式设计")
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5004, debug=True)

if __name__ == '__main__':
    main()
