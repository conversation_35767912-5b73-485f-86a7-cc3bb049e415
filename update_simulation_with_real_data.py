#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用真实雪球数据更新模拟交易系统
将真实的持仓数据导入到"测试"和"test"账户中
"""

import json
import logging
import sqlite3
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimulationUpdater:
    """模拟系统更新器"""
    
    def __init__(self, db_file='simulation_trading.db'):
        self.db_file = db_file
        self.load_config()
        self.load_real_data()
    
    def load_config(self):
        """加载雪球配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ 雪球Token设置成功")
            
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
    
    def load_real_data(self):
        """加载真实持仓数据"""
        try:
            with open('real_holdings_data.json', 'r', encoding='utf-8') as f:
                self.real_data = json.load(f)
            logger.info("✅ 真实持仓数据加载成功")
        except Exception as e:
            logger.error(f"❌ 加载真实数据失败: {e}")
            self.real_data = None
    
    def extract_real_holdings(self):
        """提取真实持仓信息"""
        holdings = []

        # 方法1: 从real_holdings_data.json中提取
        if self.real_data:
            pysnowball_results = self.real_data.get('pysnowball_result', {}).get('results', {})

            for key, data in pysnowball_results.items():
                if 'current' in key and 'last_rb' in data:
                    last_rb = data['last_rb']
                    if 'holdings' in last_rb:
                        for holding in last_rb['holdings']:
                            stock_symbol = holding.get('stock_symbol', '')
                            stock_name = holding.get('stock_name', '')
                            weight = holding.get('weight', 0)
                            volume = holding.get('volume', 0)

                            if stock_symbol and stock_name:
                                holdings.append({
                                    'stock_code': stock_symbol,
                                    'stock_name': stock_name,
                                    'weight': weight,
                                    'volume': volume,
                                    'source': key
                                })
                                logger.info(f"   📊 {stock_name} ({stock_symbol}): 权重 {weight}%")

        # 方法2: 从最新发现的"模拟"分类中提取
        try:
            logger.info("   🔍 从'模拟'分类获取持仓...")
            stocks_result = ball.watch_stock(-4)  # 模拟分类ID: -4

            if stocks_result and 'data' in stocks_result:
                stock_list = stocks_result['data'].get('stocks', [])
                logger.info(f"   📋 模拟分类中有 {len(stock_list)} 只股票")

                for stock in stock_list:
                    stock_symbol = stock.get('symbol', '')
                    stock_name = stock.get('name', '')

                    if stock_symbol and stock_name:
                        # 如果已经在holdings中，跳过
                        if any(h['stock_code'] == stock_symbol for h in holdings):
                            continue

                        holdings.append({
                            'stock_code': stock_symbol,
                            'stock_name': stock_name,
                            'weight': 100.0,  # 默认权重
                            'volume': 0,
                            'source': 'simulation_watchlist'
                        })
                        logger.info(f"   📊 {stock_name} ({stock_symbol}): 来自模拟分类")

        except Exception as e:
            logger.warning(f"   ⚠️ 从模拟分类获取失败: {e}")

        return holdings
    
    def get_current_stock_price(self, stock_code):
        """获取股票当前价格"""
        try:
            quote = ball.quotec(stock_code)
            if quote and 'data' in quote and quote['data']:
                return quote['data'][0].get('current', 0)
            return 0
        except:
            return 0
    
    def calculate_shares_from_weight(self, weight, total_value, stock_price):
        """根据权重计算股数"""
        if stock_price <= 0:
            return 0
        
        target_value = total_value * (weight / 100)
        shares = int(target_value / stock_price / 100) * 100  # 按手计算
        return shares
    
    def update_simulation_accounts(self, holdings):
        """更新模拟账户"""
        if not holdings:
            logger.warning("没有真实持仓数据可更新")
            return
        
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 要更新的账户
        accounts_to_update = [
            {"name": "测试", "total_value": 1000000},
            {"name": "test", "total_value": 1000000},
        ]
        
        for account in accounts_to_update:
            account_name = account["name"]
            total_value = account["total_value"]
            
            logger.info(f"\n🔄 更新账户: {account_name}")
            
            # 清空现有持仓
            cursor.execute('DELETE FROM positions WHERE account_name = ?', (account_name,))
            cursor.execute('DELETE FROM trades WHERE account_name = ?', (account_name,))
            
            # 重置账户资金
            cursor.execute('''
                UPDATE simulation_accounts 
                SET current_cash = ?, total_asset = ?, updated_at = CURRENT_TIMESTAMP
                WHERE account_name = ?
            ''', (total_value, total_value, account_name))
            
            total_used_cash = 0
            
            # 添加真实持仓
            for holding in holdings:
                stock_code = holding['stock_code']
                stock_name = holding['stock_name']
                weight = holding['weight']
                
                # 获取当前价格
                current_price = self.get_current_stock_price(stock_code)
                if current_price <= 0:
                    logger.warning(f"   ⚠️ 无法获取 {stock_name} 的价格，跳过")
                    continue
                
                # 计算股数
                shares = self.calculate_shares_from_weight(weight, total_value, current_price)
                if shares <= 0:
                    logger.warning(f"   ⚠️ {stock_name} 计算股数为0，跳过")
                    continue
                
                # 计算成本
                total_cost = shares * current_price
                total_used_cash += total_cost
                
                # 插入持仓
                cursor.execute('''
                    INSERT INTO positions 
                    (account_name, stock_code, stock_name, shares, avg_cost, total_cost)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (account_name, stock_code, stock_name, shares, current_price, total_cost))
                
                # 插入买入交易记录
                cursor.execute('''
                    INSERT INTO trades 
                    (account_name, stock_code, stock_name, action, shares, price, amount, commission, trade_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (account_name, stock_code, stock_name, 'buy', shares, current_price, total_cost, total_cost * 0.0003, datetime.now().isoformat()))
                
                logger.info(f"   ✅ {stock_name}: {shares}股 @ ¥{current_price:.2f} = ¥{total_cost:,.2f}")
            
            # 更新剩余现金
            remaining_cash = total_value - total_used_cash
            cursor.execute('''
                UPDATE simulation_accounts 
                SET current_cash = ?, updated_at = CURRENT_TIMESTAMP
                WHERE account_name = ?
            ''', (remaining_cash, account_name))
            
            logger.info(f"   💰 剩余现金: ¥{remaining_cash:,.2f}")
            logger.info(f"   📊 持仓市值: ¥{total_used_cash:,.2f}")
        
        conn.commit()
        conn.close()
        logger.info("✅ 模拟账户更新完成")
    
    def show_account_summary(self):
        """显示账户摘要"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        logger.info("\n📊 账户摘要")
        logger.info("=" * 60)
        
        cursor.execute('''
            SELECT sa.account_name, sa.current_cash, sa.total_asset,
                   COUNT(p.id) as position_count,
                   COALESCE(SUM(CASE WHEN p.shares > 0 THEN p.total_cost ELSE 0 END), 0) as market_value
            FROM simulation_accounts sa
            LEFT JOIN positions p ON sa.account_name = p.account_name
            WHERE sa.account_name IN ('测试', 'test')
            GROUP BY sa.account_name
        ''')
        
        for row in cursor.fetchall():
            account_name, current_cash, total_asset, position_count, market_value = row
            logger.info(f"\n💼 {account_name}:")
            logger.info(f"   总资产: ¥{total_asset:,.2f}")
            logger.info(f"   现金: ¥{current_cash:,.2f}")
            logger.info(f"   持仓市值: ¥{market_value:,.2f}")
            logger.info(f"   持仓数量: {position_count} 只")
            
            # 显示具体持仓
            cursor.execute('''
                SELECT stock_name, stock_code, shares, avg_cost, total_cost
                FROM positions 
                WHERE account_name = ? AND shares > 0
            ''', (account_name,))
            
            positions = cursor.fetchall()
            if positions:
                logger.info(f"   持仓详情:")
                for pos in positions:
                    stock_name, stock_code, shares, avg_cost, total_cost = pos
                    logger.info(f"     • {stock_name} ({stock_code}): {shares}股 @ ¥{avg_cost:.2f} = ¥{total_cost:,.2f}")
        
        conn.close()

def main():
    """主函数"""
    print("🔄 使用真实雪球数据更新模拟交易系统")
    print("将真实持仓导入到测试账户中")
    print("=" * 80)
    
    updater = SimulationUpdater()
    
    # 1. 提取真实持仓
    logger.info("\n📊 1. 提取真实持仓数据")
    holdings = updater.extract_real_holdings()
    
    if not holdings:
        logger.error("❌ 没有找到真实持仓数据")
        logger.info("💡 可能的原因:")
        logger.info("   1. real_holdings_data.json 文件不存在")
        logger.info("   2. 文件中没有有效的持仓数据")
        logger.info("   3. 数据格式不正确")
        return
    
    logger.info(f"✅ 找到 {len(holdings)} 只股票的持仓数据")
    
    # 2. 更新模拟账户
    logger.info("\n🔄 2. 更新模拟账户")
    updater.update_simulation_accounts(holdings)
    
    # 3. 显示账户摘要
    logger.info("\n📊 3. 显示更新后的账户摘要")
    updater.show_account_summary()
    
    print(f"\n🎉 模拟账户更新完成！")
    print(f"现在您的'测试'和'test'账户包含了真实的雪球持仓数据")
    print(f"请刷新模拟交易界面查看更新后的持仓")

if __name__ == '__main__':
    main()
