#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
回到第一版API方法查看持仓情况
使用xq.json中的配置直接获取持仓数据
"""

import json
import logging
import easytrader
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OriginalAPIChecker:
    """原始API检查器"""
    
    def __init__(self):
        self.load_config()
    
    def load_config(self):
        """加载xq.json配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            self.account = self.config.get('account', '')
            self.password = self.config.get('password', '')
            self.portfolio_code = self.config.get('portfolio_code', '')
            self.cookies = self.config.get('cookies', '')
            
            logger.info(f"✅ 配置加载成功")
            logger.info(f"   账户: {self.account}")
            logger.info(f"   组合代码: {self.portfolio_code}")
            
            # 设置pysnowball token
            cookies_str = self.cookies
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ pysnowball Token设置成功: u={u}")
                self.user_id = u
            
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
    
    def test_easytrader_with_config(self):
        """使用配置测试easytrader"""
        logger.info("🔧 使用配置测试easytrader")
        logger.info("=" * 60)
        
        try:
            # 创建easytrader实例
            xq = easytrader.use('xq')
            
            # 使用配置文件中的信息登录
            login_info = {
                'cookies': self.cookies,
                'portfolio_code': self.portfolio_code,
                'portfolio_market': 'cn'
            }
            
            logger.info(f"🔑 使用组合代码: {self.portfolio_code}")
            logger.info(f"🔑 使用账户: {self.account}")
            
            # 尝试登录
            xq.prepare(**login_info)
            logger.info(f"✅ easytrader登录成功")
            
            results = {}
            
            # 1. 获取余额
            try:
                balance = xq.balance
                logger.info(f"💰 余额信息:")
                if balance:
                    for i, bal in enumerate(balance):
                        logger.info(f"   账户 {i+1}:")
                        for key, value in bal.items():
                            logger.info(f"     {key}: {value}")
                results['balance'] = balance
            except Exception as e:
                logger.error(f"❌ 获取余额失败: {e}")
                results['balance_error'] = str(e)
            
            # 2. 获取持仓
            try:
                position = xq.position
                logger.info(f"📊 持仓信息:")
                if position:
                    logger.info(f"   持仓数量: {len(position)} 只")
                    for i, pos in enumerate(position):
                        logger.info(f"   股票 {i+1}:")
                        for key, value in pos.items():
                            logger.info(f"     {key}: {value}")
                        logger.info(f"   ---")
                else:
                    logger.info(f"   无持仓")
                results['position'] = position
            except Exception as e:
                logger.error(f"❌ 获取持仓失败: {e}")
                results['position_error'] = str(e)
            
            # 3. 获取今日委托
            try:
                entrusts = xq.today_entrusts
                logger.info(f"📋 今日委托:")
                if entrusts:
                    logger.info(f"   委托数量: {len(entrusts)} 笔")
                    for i, ent in enumerate(entrusts):
                        logger.info(f"   委托 {i+1}: {ent}")
                else:
                    logger.info(f"   无委托")
                results['entrusts'] = entrusts
            except Exception as e:
                logger.error(f"❌ 获取委托失败: {e}")
                results['entrusts_error'] = str(e)
            
            # 4. 获取今日成交
            try:
                trades = xq.today_trades
                logger.info(f"💼 今日成交:")
                if trades:
                    logger.info(f"   成交数量: {len(trades)} 笔")
                    for i, trade in enumerate(trades):
                        logger.info(f"   成交 {i+1}: {trade}")
                else:
                    logger.info(f"   无成交")
                results['trades'] = trades
            except Exception as e:
                logger.error(f"❌ 获取成交失败: {e}")
                results['trades_error'] = str(e)
            
            return {
                'success': True,
                'results': results
            }
            
        except Exception as e:
            logger.error(f"❌ easytrader测试失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_alternative_portfolio_codes(self):
        """测试其他可能的组合代码"""
        logger.info("🔍 测试其他可能的组合代码")
        logger.info("=" * 60)
        
        # 基于配置生成可能的组合代码
        base_code = self.portfolio_code
        alternative_codes = [
            base_code,  # 原始代码
            base_code.upper(),  # 大写
            base_code.lower(),  # 小写
            f"ZH{base_code}",  # ZH前缀
            f"ZH{base_code.upper()}",
            f"ZH{base_code.lower()}",
        ]
        
        # 如果是test，添加更多变体
        if base_code.lower() == 'test':
            alternative_codes.extend([
                "测试",
                "TEST",
                "Test",
                "ZH测试",
                "ZHTEST",
                "ZHTest",
                "ZHtest",
            ])
        
        successful_codes = []
        
        for code in alternative_codes:
            try:
                logger.info(f"\n🧪 测试组合代码: {code}")
                
                # 创建新的easytrader实例
                xq = easytrader.use('xq')
                
                login_info = {
                    'cookies': self.cookies,
                    'portfolio_code': code,
                    'portfolio_market': 'cn'
                }
                
                # 尝试登录
                xq.prepare(**login_info)
                logger.info(f"   ✅ 登录成功")
                
                # 尝试获取数据
                test_results = {}
                
                try:
                    balance = xq.balance
                    if balance:
                        test_results['balance'] = balance
                        logger.info(f"   💰 余额: 成功获取")
                except Exception as e:
                    test_results['balance_error'] = str(e)
                    logger.info(f"   💰 余额: 失败 - {e}")
                
                try:
                    position = xq.position
                    if position:
                        test_results['position'] = position
                        logger.info(f"   📊 持仓: 成功获取 {len(position)} 只")
                    else:
                        logger.info(f"   📊 持仓: 空")
                except Exception as e:
                    test_results['position_error'] = str(e)
                    logger.info(f"   📊 持仓: 失败 - {e}")
                
                # 如果有任何成功的数据，记录这个代码
                if any(key for key in test_results.keys() if not key.endswith('_error')):
                    successful_codes.append({
                        'code': code,
                        'results': test_results
                    })
                    logger.info(f"   🎉 代码 {code} 有有效数据！")
                
            except Exception as e:
                logger.info(f"   ❌ 代码 {code} 测试失败: {e}")
        
        return successful_codes
    
    def check_pysnowball_with_config(self):
        """使用pysnowball检查配置相关的组合"""
        logger.info("🐍 使用pysnowball检查组合")
        logger.info("=" * 60)
        
        results = {}
        
        # 基于配置生成组合代码
        base_code = self.portfolio_code
        test_codes = [
            base_code,
            f"ZH{base_code}",
            f"ZH{base_code.upper()}",
        ]
        
        # 如果是test，添加数字变体
        if base_code.lower() == 'test':
            test_codes.extend([
                "ZH000001", "ZH000002", "ZH000003",
                "ZH001001", "ZH001002", "ZH001003",
                "测试", "TEST",
            ])
        
        for code in test_codes:
            try:
                logger.info(f"\n🔍 测试pysnowball组合: {code}")
                
                # 尝试获取当前调仓
                try:
                    current = ball.rebalancing_current(code)
                    if current and 'last_rb' in current:
                        logger.info(f"   ✅ 当前调仓: 成功")
                        results[f'{code}_current'] = current
                    else:
                        logger.info(f"   ⚠️ 当前调仓: 空数据")
                except Exception as e:
                    logger.info(f"   ❌ 当前调仓: {e}")
                
                # 尝试获取历史调仓
                try:
                    history = ball.rebalancing_history(code)
                    if history and 'list' in history:
                        logger.info(f"   ✅ 历史调仓: {len(history['list'])} 条")
                        results[f'{code}_history'] = history
                    else:
                        logger.info(f"   ⚠️ 历史调仓: 空数据")
                except Exception as e:
                    logger.info(f"   ❌ 历史调仓: {e}")
                
            except Exception as e:
                logger.info(f"   ❌ 组合 {code} 处理失败: {e}")
        
        return results

def main():
    """主函数"""
    print("🎯 回到第一版API方法查看持仓情况")
    print("使用xq.json配置直接获取数据")
    print("=" * 80)
    
    checker = OriginalAPIChecker()
    
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'config': {
            'account': checker.account,
            'portfolio_code': checker.portfolio_code,
        },
        'easytrader_result': None,
        'alternative_codes': [],
        'pysnowball_result': {}
    }
    
    # 1. 使用配置测试easytrader
    logger.info("\n🔧 1. 使用配置测试easytrader")
    easytrader_result = checker.test_easytrader_with_config()
    all_results['easytrader_result'] = easytrader_result
    
    # 2. 测试其他可能的组合代码
    logger.info("\n🔍 2. 测试其他可能的组合代码")
    alternative_results = checker.test_alternative_portfolio_codes()
    all_results['alternative_codes'] = alternative_results
    
    # 3. 使用pysnowball检查
    logger.info("\n🐍 3. 使用pysnowball检查组合")
    pysnowball_result = checker.check_pysnowball_with_config()
    all_results['pysnowball_result'] = pysnowball_result
    
    # 4. 汇总结果
    logger.info("\n📊 4. 结果汇总")
    logger.info("=" * 60)
    
    # 检查easytrader结果
    if easytrader_result.get('success', False):
        logger.info("✅ easytrader: 连接成功")
        results = easytrader_result.get('results', {})
        
        if 'balance' in results:
            balance = results['balance']
            if balance:
                logger.info(f"   💰 余额: {len(balance)} 个账户")
            else:
                logger.info(f"   💰 余额: 空")
        
        if 'position' in results:
            position = results['position']
            if position:
                logger.info(f"   📊 持仓: {len(position)} 只股票")
                for i, pos in enumerate(position):
                    stock_name = pos.get('stock_name', 'N/A')
                    stock_code = pos.get('stock_code', 'N/A')
                    shares = pos.get('current_amount', 0)
                    logger.info(f"     {i+1}. {stock_name} ({stock_code}): {shares} 股")
            else:
                logger.info(f"   📊 持仓: 空")
        
    else:
        logger.warning(f"⚠️ easytrader: 连接失败 - {easytrader_result.get('error', 'Unknown')}")
    
    # 检查替代代码结果
    if alternative_results:
        logger.info(f"✅ 替代代码: 找到 {len(alternative_results)} 个有效代码")
        for result in alternative_results:
            code = result['code']
            data = result['results']
            logger.info(f"   🎯 {code}: {list(data.keys())}")
    else:
        logger.warning("⚠️ 替代代码: 没有找到有效代码")
    
    # 检查pysnowball结果
    if pysnowball_result:
        logger.info(f"✅ pysnowball: 找到 {len(pysnowball_result)} 个有效组合")
        for key in pysnowball_result.keys():
            logger.info(f"   📊 {key}")
    else:
        logger.warning("⚠️ pysnowball: 没有找到有效组合")
    
    # 5. 保存结果
    try:
        with open('original_api_holdings_check.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        logger.info(f"\n💾 完整结果已保存到: original_api_holdings_check.json")
    except Exception as e:
        logger.error(f"\n❌ 保存结果失败: {e}")
    
    print(f"\n🎉 第一版API检查完成！")
    
    # 总结发现
    found_holdings = False
    if easytrader_result.get('success') and easytrader_result.get('results', {}).get('position'):
        found_holdings = True
        print(f"🎯 easytrader成功获取到持仓数据！")
    
    if alternative_results:
        for result in alternative_results:
            if 'position' in result['results']:
                found_holdings = True
                print(f"🎯 组合代码 {result['code']} 有持仓数据！")
    
    if not found_holdings:
        print(f"⚠️ 未能通过第一版API获取到持仓数据")
        print(f"💡 可能需要:")
        print(f"   1. 检查portfolio_code是否正确")
        print(f"   2. 确认雪球账户状态")
        print(f"   3. 验证cookies是否有效")

if __name__ == '__main__':
    main()
