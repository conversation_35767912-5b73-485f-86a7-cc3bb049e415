# 市场深度交易

Veighna Elite Trader提供了市场深度交易支持（实时）。


## 应用场景

市场深度交易组件应用于单合约实时日内交易。支持显示买方和卖方订单的实时挂单数据，按目前价格进行更新以反映实时市场活动。


## 启动模块

市场深度交易模块是Veighna Elite Trader自带的模块，无需启动之前通过【策略应用】标签页加载。

启动登录VeighNa Elite Trader后，启动模块之前，请先连接交易接口。看到VeighNa Trader主界面【日志】栏输出“合约信息查询成功”之后再启动模块。

请注意，IB接口因为登录时无法自动获取所有的合约信息，只有在用户手动订阅行情时才能获取。因此需要在主界面上先行手动订阅合约行情，再启动模块。

成功连接交易接口后，在菜单栏中点击【功能】-> 【市场深度交易】，或者点击左侧按钮栏的图标：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/ladder/1.png)

即可进入市场深度交易模块的UI界面，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/ladder/2.png)


## 创建图表

打开市场深度交易模块的UI界面后，在【本地代码】编辑框中输入合约代码（注意本地代码由代码前缀和交易所后缀两部分组成，如rb2305.SHFE）。

点击【打开】按钮，即可创建对应合约的市场深度图表，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/ladder/3.png)


### 多个图表

市场深度交易模块支持同时创建多个图表，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/ladder/7.png)

### 关闭图表

若想关闭已创建的市场深度图表图表，点击对应图表左下角的【关闭】按钮即可顺利关闭该图表。


## 数据监控

图表的顶部显示的是合约代码，左上方显示的是合约的委托路由（可选）、涨跌幅、开盘价、最高价、最低价以及成交量的信息，左侧中部显示的是该合约的持仓情况，左下方显示的是委托相关的按钮以及关闭图表的按钮。

图表的右侧显示的是该合约的市场实时深度信息。【价格】列显示的是合约的价格，价格由高到低排序。【买入】与【卖出】列分别显示的是合约的买方挂单量和卖方挂单量，与【价格】一一对应。

【价格】列上深蓝色单元格显示的是实时买一价，左侧【买入】列同一高度单元深蓝色单元格上显示的是对应的买一量。【价格】列上深红色单元格显示的是实时卖一价，右侧【卖出】列同一高度单元深红色单元格上显示的是对应的卖一量。

【成交】列显示的是最新一个tick的成交量，【委托】列显示的是用户当前在该合约上对应价格的挂单数量。


## 深度下单

市场深度图表创建成功后，用户可基于图表上更新的买方深度挂单和卖方深度挂单进行手动委托。

在界面左侧中部设置好预计委托的手数与开平方向之后，双击【买入】或【卖出】列上与中间【价格】列价格对应的蓝色/红色单元格，会弹出【操作确认】窗口，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/ladder/4.png)

点击【确认】，即可发出委托。此时【委托】列上与委托价格平齐的单元格上则会显示活跃委托的数量，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/ladder/5.png)


## 深度撤单

若想对撤销还未成交的挂单，可双击【委托】列上显示委托价格的单元格，会弹出【操作确认】窗口，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/ladder/6.png)

点击【确认】，即可撤销所有该对应价格的活动委托。

若想对该合约的活动委托进行批量撤单，也可点击图表左下角的【撤卖】/【全撤】/【撤买】按钮撤销委托。


## 多账户支持

### 加载

市场深度交易模块提供了多账户批量下单交易支持（手动）。

以登录**CTP**接口为例，在登录界面下方的【交易接口】标签页的下拉框中先选中CTP接口。在“自定义接口”处填写自定义的接口名（例如“CTP1”、“CTP2”）之后点击【添加】按钮，填写子账户的配置信息，点击【确定】按钮，则可依次加载对应账户的接口。

添加完毕后，点击登录界面的【登录】按钮登录VeighNa Elite Trader。在菜单栏中依次点击【系统】->【连接xxx】（xxx是自定义的接口名，若加载时填写的“CTP1”，则菜单栏中显示的就是【连接CTP1】），即可连接子账户接口。

连接成功以后，VeighNa Elite Trader主界面【日志】组件会立刻输出登录相关信息，同时用户也可以看到对应的账号信息，持仓信息等相关信息。

此时已经可以在【市场深度交易】界面通过指定的账户进行委托了。

### 创建账户下单组合

如果需要构建账户组合，并为组合内的每个账户分配不同的比例乘数，可以在菜单栏中点击【功能】->【多账户批量下单】，进入多账户批量下单模块的UI界面创建下单组合。

### 深度下单

#### 账户下单

若需通过市场深度交易模块进行多账户委托，可在创建的图表左上角选择账户对应的【路由】进行委托。

#### 组合下单

若需通过市场深度交易模块进行组合下单，可在创建的图表左上角选择下单组合对应的【路由】进行委托（根据下单组合指定的比例发出委托）。

发出委托后，除了可在图表的【委托】列观察到委托数量之外，还可在VeighNa Elite Trader主界面【委托】组件和【成交】组件上跟踪到根据对应接口下单的委托，如下图所示：

![](https://vnpy-doc.oss-cn-shanghai.aliyuncs.com/elite/ladder/8.png)

**请注意**：
 - 目前支持同时登录最多登录5个交易账户
 - 市场深度交易图表上显示的是连接的所有路由上该合约的总持仓
