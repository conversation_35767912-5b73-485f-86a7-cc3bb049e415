// 用户相关类型定义

export interface UserInfo {
  id: number
  yonghuming: string
  youxiang: string
  dingyue_leixing: 'jiben' | 'gaoji'
  dingyue_kaishi?: string
  dingyue_jieshu?: string
  xukezheng_miyao?: string
  shebei_id?: string
  chuangjian_shijian: string
  gengxin_shijian: string
  shi_huoyue: boolean
}

export interface LoginForm {
  yonghuming: string
  mima: string
  jizhu_denglu?: boolean
}

export interface RegisterForm {
  yonghuming: string
  youxiang: string
  mima: string
  queren_mima: string
  tongyi_xieyi: boolean
  yanzheng_ma?: string
}

export interface ChangePasswordForm {
  old_password: string
  new_password: string
  confirm_password: string
}

export interface UserSettings {
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  notifications: {
    email: boolean
    push: boolean
    strategy_alerts: boolean
    trade_alerts: boolean
  }
  trading: {
    default_amount: number
    risk_level: 'low' | 'medium' | 'high'
    auto_stop_loss: boolean
    stop_loss_percent: number
  }
}

export interface SubscriptionInfo {
  type: 'jiben' | 'gaoji'
  start_date: string
  end_date: string
  auto_renew: boolean
  payment_method: string
  next_billing_date?: string
  status: 'active' | 'expired' | 'cancelled'
}

export interface LoginResponse {
  token: string
  user: UserInfo
  permissions?: string[]
  expires_in: number
}

export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}
