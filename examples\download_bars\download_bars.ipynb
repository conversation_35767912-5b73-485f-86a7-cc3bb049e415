{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# 忽略各模块的警告信息\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 加载所需使用的模块\n", "from datetime import datetime\n", "\n", "from vnpy.trader.datafeed import get_datafeed\n", "from vnpy.trader.database import get_database, DB_TZ\n", "from vnpy.trader.constant import Interval\n", "from vnpy.trader.object import BarData, HistoryRequest\n", "from vnpy.trader.utility import extract_vt_symbol\n", "from vnpy.trader.setting import SETTINGS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 配置数据服务\n", "SETTINGS[\"datafeed.name\"] = \"rqdata\"            # 可以根据自己的需求选择数据服务：rqdata/xt/wind等\n", "SETTINGS[\"datafeed.username\"] = \"license\"       # RQData的用户名统一为“license”这个字符串\n", "SETTINGS[\"datafeed.password\"] = \"123456\"        # 这里需要替换为你购买或者申请试用的RQData数据license\n", "\n", "# 配置数据库\n", "SETTINGS[\"database.name\"] = \"taos\"              # 可以根据自己的需求选择数据库，这里使用的是TDengine\n", "SETTINGS[\"database.database\"] = \"vnpy\"\n", "SETTINGS[\"database.host\"] = \"127.0.0.1\"\n", "SETTINGS[\"database.port\"] = 6030\n", "SETTINGS[\"database.user\"] = \"root\"\n", "SETTINGS[\"database.password\"] = \"taosdata\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 创建对象实例\n", "datafeed = get_datafeed()\n", "\n", "database = get_database()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 要下载数据的合约代码\n", "vt_symbols = [\n", "    \"IF2501.CFFEX\",\n", "    \"IF2502.CFFEX\",\n", "    \"IF2503.CFFEX\",\n", "\n", "    \"IH2501.CFFEX\",\n", "    \"IH2502.CFFEX\",\n", "    \"IH2503.CFFEX\",\n", "\n", "    \"IC2501.CFFEX\",\n", "    \"IC2502.CFFEX\",\n", "    \"IC2503.CFFEX\",\n", "\n", "    \"IM2501.CFFEX\",\n", "    \"IM2502.CFFEX\",\n", "    \"IM2503.CFFEX\",\n", "]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 要下载数据的起止时间\n", "start = datetime(2025, 1, 1, tzinfo=DB_TZ)\n", "end = datetime(2025, 3, 30, tzinfo=DB_TZ)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["下载数据成功：IF2501.CFFEX，总数据量：2880\n", "下载数据成功：IF2502.CFFEX，总数据量：7440\n", "下载数据成功：IF2503.CFFEX，总数据量：12240\n", "下载数据成功：IH2501.CFFEX，总数据量：2880\n", "下载数据成功：IH2502.CFFEX，总数据量：7440\n", "下载数据成功：IH2503.CFFEX，总数据量：12240\n", "下载数据成功：IC2501.CFFEX，总数据量：2880\n", "下载数据成功：IC2502.CFFEX，总数据量：7440\n", "下载数据成功：IC2503.CFFEX，总数据量：12240\n", "下载数据成功：IM2501.CFFEX，总数据量：2880\n", "下载数据成功：IM2502.CFFEX，总数据量：7440\n", "下载数据成功：IM2503.CFFEX，总数据量：12240\n"]}], "source": ["# 遍历列表执行下载\n", "for vt_symbol in vt_symbols:\n", "    # 拆分合约代码和交易所\n", "    symbol, exchange = extract_vt_symbol(vt_symbol)\n", "\n", "    # 创建历史数据请求对象\n", "    req: HistoryRequest = HistoryRequest(\n", "        symbol=symbol,\n", "        exchange=exchange,\n", "        start=start,\n", "        end=end,\n", "        interval=Interval.MINUTE        # 这里下载最常用的1分钟K线\n", "    )\n", "\n", "    # 从数据服务下载数据\n", "    bars: list[BarData] = datafeed.query_bar_history(req)\n", "\n", "    # 如果下载成功则保存\n", "    if bars:\n", "        database.save_bar_data(bars)\n", "        print(f\"下载数据成功：{vt_symbol}，总数据量：{len(bars)}\")\n", "    # 否则失败则打印信息\n", "    else:\n", "        print(f\"下载数据失败：{vt_symbol}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.2"}}, "nbformat": 4, "nbformat_minor": 4}