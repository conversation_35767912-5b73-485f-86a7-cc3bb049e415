# Pre-commit hooks配置
# 安装: pre-commit install
# 运行: pre-commit run --all-files

repos:
  # 基础检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        description: 删除行尾空白字符
      - id: end-of-file-fixer
        description: 确保文件以换行符结尾
      - id: check-yaml
        description: 检查YAML文件语法
      - id: check-json
        description: 检查JSON文件语法
      - id: check-toml
        description: 检查TOML文件语法
      - id: check-merge-conflict
        description: 检查合并冲突标记
      - id: check-added-large-files
        description: 检查大文件
        args: ['--maxkb=1000']
      - id: debug-statements
        description: 检查调试语句
      - id: check-docstring-first
        description: 检查文档字符串位置

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        description: 代码格式化
        language_version: python3
        args: [--line-length=88]

  # 导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        description: 导入语句排序
        args: [--profile=black, --line-length=88]

  # 代码风格检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        description: 代码风格检查
        additional_dependencies:
          - flake8-bugbear
          - flake8-docstrings
          - flake8-import-order
          - flake8-annotations

  # 类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        description: 静态类型检查
        additional_dependencies:
          - types-requests
          - types-python-dateutil
          - types-pytz
        args: [--ignore-missing-imports]

  # 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        description: 安全漏洞检查
        args: [-r, src]
        exclude: ^test/

  # 文档字符串检查
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        description: 文档字符串风格检查
        args: [--convention=google]
        exclude: ^(test/|migrations/)

  # 复杂度检查
  - repo: https://github.com/xenon-python/xenon
    rev: v0.9.0
    hooks:
      - id: xenon
        description: 代码复杂度检查
        args: [--max-average=A, --max-modules=B, --max-absolute=B]

  # Jupyter Notebook清理
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.0
    hooks:
      - id: nbqa-black
        description: Notebook代码格式化
      - id: nbqa-isort
        description: Notebook导入排序

  # 提交信息检查
  - repo: https://github.com/commitizen-tools/commitizen
    rev: 3.6.0
    hooks:
      - id: commitizen
        description: 提交信息格式检查
        stages: [commit-msg]

# 全局配置
default_language_version:
  python: python3.9

# 排除的文件
exclude: |
  (?x)^(
    migrations/.*|
    .*\.min\.(js|css)|
    node_modules/.*|
    \.venv/.*|
    build/.*|
    dist/.*
  )$

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks
    
    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
