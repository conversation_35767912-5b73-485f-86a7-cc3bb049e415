#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
雪球真实模拟交易系统
基于雪球网真实API接入，实现组合调仓功能
参考: https://blog.csdn.net/CY19980216/article/details/82770410
"""

import json
import logging
import requests
import time
from datetime import datetime
from typing import Dict, List, Optional

try:
    import pysnowball as ball
    PYSNOWBALL_AVAILABLE = True
except ImportError:
    PYSNOWBALL_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class XueqiuRealTrader:
    """雪球真实模拟交易器 - 接入雪球系统"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/******** Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
        }
        self.session.headers.update(self.headers)
        
        # 雪球API端点
        self.api_endpoints = {
            'login_sms': 'https://xueqiu.com/account/sms/send_verification_code.json',
            'login': 'https://xueqiu.com/snowman/login',
            'cube_list': 'https://xueqiu.com/cubes/list.json',
            'cube_create': 'https://xueqiu.com/cubes/create.json',
            'cube_rebalancing': 'https://xueqiu.com/cubes/rebalancing.json',
            'cube_nav': 'https://xueqiu.com/cubes/nav_daily/all.json',
            'cube_current': 'https://xueqiu.com/cubes/rebalancing/current.json',
            'cube_history': 'https://xueqiu.com/cubes/rebalancing/history.json',
            'stock_quote': 'https://stock.xueqiu.com/v5/stock/realtime/quotec.json',
            'stock_search': 'https://xueqiu.com/query/v1/suggest_stock.json'
        }
        
        self.user_cubes = []
        self.current_cube_symbol = None
        self.is_logged_in = False
        
        # 加载配置
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 解析cookies
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u and PYSNOWBALL_AVAILABLE:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                self.is_logged_in = True
                logger.info("✅ 配置加载成功，已登录雪球")
            else:
                logger.warning("⚠️ 未找到有效登录信息")
                
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
    
    def login_with_sms(self, phone_number: str, verification_code: str = None):
        """使用短信验证码登录"""
        try:
            # 发送验证码
            if not verification_code:
                code_data = {
                    'areacode': '86',
                    'telephone': phone_number
                }
                
                response = self.session.post(self.api_endpoints['login_sms'], data=code_data)
                if response.status_code == 200:
                    logger.info("✅ 验证码发送成功")
                    verification_code = input("请输入收到的验证码: ")
                else:
                    logger.error(f"❌ 验证码发送失败: {response.text}")
                    return False
            
            # 登录
            login_data = {
                'areacode': '86',
                'remember_me': 'true',
                'telephone': phone_number,
                'code': verification_code
            }
            
            response = self.session.post(self.api_endpoints['login'], data=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('user_id'):
                    self.is_logged_in = True
                    logger.info(f"✅ 登录成功: {result.get('screen_name', 'N/A')}")
                    
                    # 保存cookies到配置文件
                    self.save_cookies()
                    return True
                else:
                    logger.error(f"❌ 登录失败: {result}")
                    return False
            else:
                logger.error(f"❌ 登录请求失败: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 登录异常: {e}")
            return False
    
    def save_cookies(self):
        """保存cookies到配置文件"""
        try:
            cookies_str = '; '.join([f"{cookie.name}={cookie.value}" for cookie in self.session.cookies])
            
            config = {'cookies': cookies_str}
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info("✅ Cookies已保存")
            
        except Exception as e:
            logger.error(f"❌ 保存cookies失败: {e}")
    
    def get_user_cubes(self):
        """获取用户创建的组合列表"""
        try:
            if not self.is_logged_in:
                logger.error("❌ 请先登录")
                return []
            
            # 使用pysnowball获取组合列表
            if PYSNOWBALL_AVAILABLE:
                result = ball.watch_list()
                if result and 'data' in result:
                    cubes = result['data'].get('cubes', [])
                    self.user_cubes = cubes
                    
                    logger.info(f"✅ 获取到 {len(cubes)} 个组合")
                    for cube in cubes:
                        logger.info(f"   组合: {cube.get('name', 'N/A')} "
                                  f"ID: {cube.get('id', 'N/A')} "
                                  f"股票数: {cube.get('symbol_count', 0)}")
                    
                    return cubes
                else:
                    logger.warning("⚠️ 没有找到组合数据")
                    return []
            else:
                logger.error("❌ pysnowball未安装")
                return []
                
        except Exception as e:
            logger.error(f"❌ 获取组合列表失败: {e}")
            return []
    
    def create_cube(self, name: str, description: str = ""):
        """创建新的投资组合"""
        try:
            if not self.is_logged_in:
                logger.error("❌ 请先登录")
                return None
            
            create_data = {
                'name': name,
                'description': description or f"通过API创建于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                'market': 'cn',  # 中国市场
                'category': 'stock'  # 股票组合
            }
            
            response = self.session.post(self.api_endpoints['cube_create'], json=create_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('id'):
                    cube_symbol = f"ZH{result['id']:06d}"
                    logger.info(f"✅ 组合创建成功: {name} ({cube_symbol})")
                    
                    # 刷新组合列表
                    self.get_user_cubes()
                    return cube_symbol
                else:
                    logger.error(f"❌ 组合创建失败: {result}")
                    return None
            else:
                logger.error(f"❌ 创建请求失败: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 创建组合异常: {e}")
            return None
    
    def set_current_cube(self, cube_symbol: str):
        """设置当前操作的组合"""
        self.current_cube_symbol = cube_symbol
        logger.info(f"📊 当前组合设置为: {cube_symbol}")
    
    def rebalance_cube(self, holdings: List[Dict], comment: str = ""):
        """调仓操作 - 雪球真实模拟交易的核心功能"""
        try:
            if not self.is_logged_in:
                logger.error("❌ 请先登录")
                return False
            
            if not self.current_cube_symbol:
                logger.error("❌ 请先设置当前组合")
                return False
            
            # 构建调仓数据
            rebalancing_data = {
                'cube_symbol': self.current_cube_symbol,
                'holdings': holdings,
                'comment': comment or f"API调仓于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            }
            
            # 发送调仓请求
            response = self.session.post(self.api_endpoints['cube_rebalancing'], json=rebalancing_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    logger.info(f"✅ 调仓成功: {self.current_cube_symbol}")
                    logger.info(f"   调仓说明: {comment}")
                    
                    # 显示调仓详情
                    for holding in holdings:
                        action = "买入" if holding.get('weight', 0) > 0 else "清仓"
                        logger.info(f"   {action}: {holding.get('stock_name', 'N/A')} "
                                  f"权重: {holding.get('weight', 0):.1f}%")
                    
                    return True
                else:
                    logger.error(f"❌ 调仓失败: {result.get('error_description', '未知错误')}")
                    return False
            else:
                logger.error(f"❌ 调仓请求失败: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 调仓异常: {e}")
            return False
    
    def buy_stock(self, stock_symbol: str, weight: float, comment: str = ""):
        """买入股票 - 通过调仓实现"""
        try:
            # 获取股票信息
            stock_info = self.get_stock_info(stock_symbol)
            if not stock_info:
                logger.error(f"❌ 无法获取股票 {stock_symbol} 信息")
                return False
            
            # 获取当前持仓
            current_holdings = self.get_current_holdings()
            
            # 构建新的持仓结构
            new_holdings = []
            stock_found = False
            
            for holding in current_holdings:
                if holding.get('stock_symbol') == stock_symbol:
                    # 更新现有持仓
                    holding['weight'] = weight
                    stock_found = True
                new_holdings.append(holding)
            
            if not stock_found:
                # 添加新股票
                new_holdings.append({
                    'stock_symbol': stock_symbol,
                    'stock_name': stock_info.get('name', stock_symbol),
                    'weight': weight
                })
            
            # 执行调仓
            buy_comment = comment or f"买入 {stock_info.get('name', stock_symbol)} {weight}%"
            return self.rebalance_cube(new_holdings, buy_comment)
            
        except Exception as e:
            logger.error(f"❌ 买入操作异常: {e}")
            return False
    
    def sell_stock(self, stock_symbol: str, weight: float = 0, comment: str = ""):
        """卖出股票 - 通过调仓实现"""
        try:
            # 获取股票信息
            stock_info = self.get_stock_info(stock_symbol)
            if not stock_info:
                logger.error(f"❌ 无法获取股票 {stock_symbol} 信息")
                return False
            
            # 获取当前持仓
            current_holdings = self.get_current_holdings()
            
            # 构建新的持仓结构
            new_holdings = []
            
            for holding in current_holdings:
                if holding.get('stock_symbol') == stock_symbol:
                    if weight > 0:
                        # 减持到指定权重
                        holding['weight'] = weight
                        new_holdings.append(holding)
                    # weight = 0 表示清仓，不添加到新持仓中
                else:
                    new_holdings.append(holding)
            
            # 执行调仓
            action = "清仓" if weight == 0 else f"减持至{weight}%"
            sell_comment = comment or f"{action} {stock_info.get('name', stock_symbol)}"
            return self.rebalance_cube(new_holdings, sell_comment)
            
        except Exception as e:
            logger.error(f"❌ 卖出操作异常: {e}")
            return False
    
    def get_current_holdings(self):
        """获取当前持仓"""
        try:
            if not self.current_cube_symbol:
                return []
            
            if PYSNOWBALL_AVAILABLE:
                result = ball.rebalancing_current(self.current_cube_symbol)
                if result:
                    last_rb = result.get('last_rb', {})
                    holdings = last_rb.get('holdings', [])
                    return holdings
            
            return []
            
        except Exception as e:
            logger.error(f"❌ 获取持仓失败: {e}")
            return []
    
    def get_stock_info(self, stock_symbol: str):
        """获取股票信息"""
        try:
            if PYSNOWBALL_AVAILABLE:
                result = ball.quotec(stock_symbol)
                if result and 'data' in result and result['data']:
                    return result['data'][0]
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取股票信息失败: {e}")
            return None
    
    def search_stock(self, query: str):
        """搜索股票"""
        try:
            if PYSNOWBALL_AVAILABLE:
                result = ball.suggest_stock(query)
                if result and 'data' in result:
                    return result['data']
            return []
            
        except Exception as e:
            logger.error(f"❌ 搜索股票失败: {e}")
            return []
    
    def get_cube_performance(self, cube_symbol: str = None):
        """获取组合表现"""
        try:
            target_cube = cube_symbol or self.current_cube_symbol
            if not target_cube:
                return None
            
            if PYSNOWBALL_AVAILABLE:
                # 获取净值数据
                nav_result = ball.nav_daily(target_cube)
                quote_result = ball.quote_current(target_cube)
                
                performance = {}
                
                if nav_result and len(nav_result) > 0:
                    cube_data = nav_result[0]
                    performance['name'] = cube_data.get('name', 'N/A')
                    performance['nav_history'] = cube_data.get('list', [])
                
                if quote_result and target_cube in quote_result:
                    quote_data = quote_result[target_cube]
                    performance.update({
                        'current_nav': quote_data.get('net_value', 'N/A'),
                        'daily_gain': quote_data.get('daily_gain', 'N/A'),
                        'total_gain': quote_data.get('total_gain', 'N/A'),
                        'annualized_gain': quote_data.get('annualized_gain', 'N/A')
                    })
                
                return performance
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取组合表现失败: {e}")
            return None

def main():
    """主函数 - 测试雪球真实模拟交易"""
    print("🚀 雪球真实模拟交易系统")
    print("基于雪球网真实API，实现组合调仓功能")
    print("=" * 60)
    
    # 创建交易器
    trader = XueqiuRealTrader()
    
    if not trader.is_logged_in:
        print("⚠️ 未登录，请先登录雪球账户")
        phone = input("请输入手机号码: ")
        if trader.login_with_sms(phone):
            print("✅ 登录成功")
        else:
            print("❌ 登录失败")
            return
    
    # 获取用户组合
    print("\n📊 获取用户组合...")
    cubes = trader.get_user_cubes()
    
    if not cubes:
        print("📝 没有找到现有组合，创建新组合...")
        cube_name = input("请输入组合名称: ") or "API测试组合"
        cube_symbol = trader.create_cube(cube_name, "通过API创建的测试组合")
        if cube_symbol:
            trader.set_current_cube(cube_symbol)
        else:
            print("❌ 创建组合失败")
            return
    else:
        # 使用第一个组合
        first_cube = cubes[0]
        cube_symbol = f"ZH{first_cube['id']:06d}"
        trader.set_current_cube(cube_symbol)
        print(f"📊 使用组合: {first_cube['name']} ({cube_symbol})")
    
    # 测试交易功能
    print(f"\n🔄 测试交易功能...")
    
    # 搜索股票
    search_query = input("请输入要搜索的股票 (默认: 茅台): ") or "茅台"
    stocks = trader.search_stock(search_query)
    if stocks:
        print(f"搜索结果:")
        for i, stock in enumerate(stocks[:5]):
            print(f"  {i+1}. {stock.get('query', 'N/A')} ({stock.get('code', 'N/A')})")
        
        # 选择股票进行交易
        try:
            choice = int(input("请选择股票编号 (1-5): ")) - 1
            if 0 <= choice < len(stocks):
                selected_stock = stocks[choice]
                stock_symbol = selected_stock.get('code', '')
                stock_name = selected_stock.get('query', '')
                
                print(f"\n📈 选择股票: {stock_name} ({stock_symbol})")
                
                # 买入测试
                weight = float(input("请输入买入权重 (%, 默认10): ") or "10")
                if trader.buy_stock(stock_symbol, weight, f"API测试买入{stock_name}"):
                    print("✅ 买入成功")
                    
                    # 显示组合表现
                    performance = trader.get_cube_performance()
                    if performance:
                        print(f"\n📊 组合表现:")
                        print(f"   当前净值: {performance.get('current_nav', 'N/A')}")
                        print(f"   总收益: {performance.get('total_gain', 'N/A')}%")
                else:
                    print("❌ 买入失败")
                    
        except (ValueError, IndexError):
            print("⚠️ 无效选择")

if __name__ == '__main__':
    main()
