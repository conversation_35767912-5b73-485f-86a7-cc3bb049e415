#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一交易引擎
整合pysnowball(雪球模拟交易)和easytrader(真实券商交易)
解决'portfolio_market'错误，提供统一的交易接口
"""

import json
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Union

# 导入pysnowball用于雪球模拟交易
try:
    import pysnowball as ball
    PYSNOWBALL_AVAILABLE = True
except ImportError:
    PYSNOWBALL_AVAILABLE = False

# 导入easytrader用于真实券商交易
try:
    import easytrader
    EASYTRADER_AVAILABLE = True
except ImportError:
    EASYTRADER_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TradingMode:
    """交易模式枚举"""
    SIMULATION = "simulation"  # 雪球模拟交易
    REAL = "real"             # 真实券商交易

class UnifiedTradingEngine:
    """统一交易引擎"""
    
    def __init__(self, config_file='trading_config.json'):
        self.config_file = config_file
        self.config = {}
        self.mode = TradingMode.SIMULATION
        self.xueqiu_trader = None
        self.real_trader = None
        self.is_initialized = False
        
        # 加载配置
        self.load_config()
    
    def load_config(self):
        """加载交易配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            logger.info(f"✅ 配置加载成功: {self.config_file}")
        except FileNotFoundError:
            # 创建默认配置
            self.create_default_config()
        except Exception as e:
            logger.error(f"❌ 配置加载失败: {e}")
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "trading_mode": "simulation",
            "xueqiu": {
                "config_file": "xq.json",
                "enabled": True
            },
            "real_brokers": {
                "ht": {
                    "config_file": "ht.json",
                    "enabled": False
                },
                "ping_an": {
                    "config_file": "ping_an.json", 
                    "enabled": False
                }
            },
            "default_portfolio_id": -1,
            "risk_control": {
                "max_position_ratio": 0.3,
                "max_single_trade_amount": 100000
            }
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            self.config = default_config
            logger.info(f"✅ 默认配置已创建: {self.config_file}")
        except Exception as e:
            logger.error(f"❌ 创建默认配置失败: {e}")
            self.config = default_config
    
    def initialize_xueqiu_trader(self):
        """初始化雪球交易器"""
        if not PYSNOWBALL_AVAILABLE:
            logger.error("❌ pysnowball未安装，无法使用雪球模拟交易")
            return False
        
        try:
            xq_config_file = self.config.get('xueqiu', {}).get('config_file', 'xq.json')
            
            # 从雪球配置文件读取token
            with open(xq_config_file, 'r', encoding='utf-8') as f:
                xq_config = json.load(f)
            
            # 解析cookies获取token
            cookies_str = xq_config.get('cookies', '')
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                
                # 创建雪球交易器包装类
                self.xueqiu_trader = XueqiuTrader(token)
                logger.info(f"✅ 雪球交易器初始化成功: u={u}")
                return True
            else:
                logger.error("❌ 无法从雪球配置获取有效token")
                return False
                
        except Exception as e:
            logger.error(f"❌ 雪球交易器初始化失败: {e}")
            return False
    
    def initialize_real_trader(self, broker='ht'):
        """初始化真实券商交易器"""
        if not EASYTRADER_AVAILABLE:
            logger.error("❌ easytrader未安装，无法使用真实券商交易")
            return False
        
        try:
            broker_config = self.config.get('real_brokers', {}).get(broker, {})
            if not broker_config.get('enabled', False):
                logger.info(f"⚠️ 券商 {broker} 未启用")
                return False
            
            config_file = broker_config.get('config_file')
            if not config_file:
                logger.error(f"❌ 券商 {broker} 配置文件未指定")
                return False
            
            # 创建easytrader实例
            self.real_trader = easytrader.use(broker)
            self.real_trader.prepare(config_file)
            
            logger.info(f"✅ 真实券商交易器初始化成功: {broker}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 真实券商交易器初始化失败: {e}")
            return False
    
    def initialize(self):
        """初始化交易引擎"""
        logger.info("🚀 初始化统一交易引擎")
        
        # 设置交易模式
        self.mode = self.config.get('trading_mode', TradingMode.SIMULATION)
        logger.info(f"📊 交易模式: {self.mode}")
        
        success = False
        
        if self.mode == TradingMode.SIMULATION:
            # 初始化雪球模拟交易
            success = self.initialize_xueqiu_trader()
        else:
            # 初始化真实券商交易
            success = self.initialize_real_trader()
        
        self.is_initialized = success
        
        if success:
            logger.info("✅ 统一交易引擎初始化成功")
        else:
            logger.error("❌ 统一交易引擎初始化失败")
        
        return success
    
    def get_quote(self, symbol: str) -> Optional[Dict]:
        """获取股票行情"""
        try:
            if self.mode == TradingMode.SIMULATION and self.xueqiu_trader:
                return self.xueqiu_trader.get_quote(symbol)
            elif self.mode == TradingMode.REAL and self.real_trader:
                # 真实券商通常不提供行情，使用雪球行情作为补充
                if PYSNOWBALL_AVAILABLE:
                    result = ball.quotec(symbol)
                    if result and 'data' in result and result['data']:
                        return result['data'][0]
                return None
            else:
                logger.error("❌ 交易器未初始化")
                return None
        except Exception as e:
            logger.error(f"❌ 获取行情失败: {e}")
            return None
    
    def get_pankou(self, symbol: str) -> Optional[Dict]:
        """获取五档行情"""
        try:
            if self.xueqiu_trader:
                return self.xueqiu_trader.get_pankou(symbol)
            else:
                logger.error("❌ 雪球交易器未初始化")
                return None
        except Exception as e:
            logger.error(f"❌ 获取五档行情失败: {e}")
            return None
    
    def get_balance(self) -> Optional[Dict]:
        """获取账户余额"""
        try:
            if self.mode == TradingMode.SIMULATION and self.xueqiu_trader:
                return self.xueqiu_trader.get_balance()
            elif self.mode == TradingMode.REAL and self.real_trader:
                return self.real_trader.balance
            else:
                logger.error("❌ 交易器未初始化")
                return None
        except Exception as e:
            logger.error(f"❌ 获取账户余额失败: {e}")
            return None
    
    def get_position(self) -> Optional[List]:
        """获取持仓信息"""
        try:
            if self.mode == TradingMode.SIMULATION and self.xueqiu_trader:
                return self.xueqiu_trader.get_position()
            elif self.mode == TradingMode.REAL and self.real_trader:
                return self.real_trader.position
            else:
                logger.error("❌ 交易器未初始化")
                return None
        except Exception as e:
            logger.error(f"❌ 获取持仓信息失败: {e}")
            return None
    
    def buy(self, symbol: str, price: float, amount: int) -> Dict:
        """买入股票"""
        try:
            if not self.is_initialized:
                return {'success': False, 'message': '交易器未初始化'}
            
            # 风险控制检查
            risk_check = self.risk_control_check(symbol, price, amount, 'buy')
            if not risk_check['passed']:
                return {'success': False, 'message': f'风险控制: {risk_check["message"]}'}
            
            if self.mode == TradingMode.SIMULATION and self.xueqiu_trader:
                return self.xueqiu_trader.buy(symbol, price, amount)
            elif self.mode == TradingMode.REAL and self.real_trader:
                return self.real_trader.buy(symbol, price, amount)
            else:
                return {'success': False, 'message': '交易器未初始化'}
                
        except Exception as e:
            logger.error(f"❌ 买入操作失败: {e}")
            return {'success': False, 'message': f'买入失败: {str(e)}'}
    
    def sell(self, symbol: str, price: float, amount: int) -> Dict:
        """卖出股票"""
        try:
            if not self.is_initialized:
                return {'success': False, 'message': '交易器未初始化'}
            
            # 风险控制检查
            risk_check = self.risk_control_check(symbol, price, amount, 'sell')
            if not risk_check['passed']:
                return {'success': False, 'message': f'风险控制: {risk_check["message"]}'}
            
            if self.mode == TradingMode.SIMULATION and self.xueqiu_trader:
                return self.xueqiu_trader.sell(symbol, price, amount)
            elif self.mode == TradingMode.REAL and self.real_trader:
                return self.real_trader.sell(symbol, price, amount)
            else:
                return {'success': False, 'message': '交易器未初始化'}
                
        except Exception as e:
            logger.error(f"❌ 卖出操作失败: {e}")
            return {'success': False, 'message': f'卖出失败: {str(e)}'}
    
    def risk_control_check(self, symbol: str, price: float, amount: int, action: str) -> Dict:
        """风险控制检查"""
        try:
            risk_config = self.config.get('risk_control', {})
            
            # 检查单笔交易金额
            trade_amount = price * amount
            max_trade_amount = risk_config.get('max_single_trade_amount', 100000)
            
            if trade_amount > max_trade_amount:
                return {
                    'passed': False,
                    'message': f'单笔交易金额 {trade_amount:.2f} 超过限制 {max_trade_amount}'
                }
            
            # 检查持仓比例（仅买入时检查）
            if action == 'buy':
                max_position_ratio = risk_config.get('max_position_ratio', 0.3)
                # 这里可以添加更复杂的持仓比例检查逻辑
            
            return {'passed': True, 'message': '风险控制检查通过'}
            
        except Exception as e:
            logger.error(f"❌ 风险控制检查失败: {e}")
            return {'passed': False, 'message': f'风险控制检查异常: {str(e)}'}
    
    def switch_mode(self, mode: str) -> bool:
        """切换交易模式"""
        try:
            if mode not in [TradingMode.SIMULATION, TradingMode.REAL]:
                logger.error(f"❌ 无效的交易模式: {mode}")
                return False
            
            self.mode = mode
            self.config['trading_mode'] = mode
            
            # 保存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            # 重新初始化
            return self.initialize()
            
        except Exception as e:
            logger.error(f"❌ 切换交易模式失败: {e}")
            return False
    
    def get_status(self) -> Dict:
        """获取引擎状态"""
        return {
            'initialized': self.is_initialized,
            'mode': self.mode,
            'pysnowball_available': PYSNOWBALL_AVAILABLE,
            'easytrader_available': EASYTRADER_AVAILABLE,
            'xueqiu_trader_ready': self.xueqiu_trader is not None,
            'real_trader_ready': self.real_trader is not None
        }

class XueqiuTrader:
    """雪球交易器包装类"""
    
    def __init__(self, token: str):
        self.token = token
        self.default_portfolio_id = -1
    
    def get_quote(self, symbol: str) -> Optional[Dict]:
        """获取股票行情"""
        try:
            result = ball.quotec(symbol)
            if result and 'data' in result and result['data']:
                return result['data'][0]
            return None
        except Exception as e:
            logger.error(f"❌ 雪球获取行情失败: {e}")
            return None
    
    def get_pankou(self, symbol: str) -> Optional[Dict]:
        """获取五档行情"""
        try:
            result = ball.pankou(symbol)
            return result if result else None
        except Exception as e:
            logger.error(f"❌ 雪球获取五档行情失败: {e}")
            return None
    
    def get_balance(self) -> Dict:
        """获取账户余额（模拟数据）"""
        return {
            'total_asset': 1000000.0,
            'available_cash': 50000.0,
            'market_value': 950000.0,
            'currency': 'CNY'
        }
    
    def get_position(self) -> List:
        """获取持仓信息"""
        try:
            # 获取自选股作为持仓信息
            result = ball.watch_stock(self.default_portfolio_id)
            if result and 'data' in result:
                stocks = result['data'].get('stocks', [])
                return [{'symbol': stock['symbol'], 'name': stock['name']} for stock in stocks]
            return []
        except Exception as e:
            logger.error(f"❌ 雪球获取持仓失败: {e}")
            return []
    
    def buy(self, symbol: str, price: float, amount: int) -> Dict:
        """买入股票（模拟交易）"""
        try:
            # 雪球模拟交易：添加到自选股
            logger.info(f"📈 雪球模拟买入: {symbol} 价格:{price} 数量:{amount}")
            
            # 这里可以实现真实的雪球组合调仓逻辑
            # 目前返回模拟成功结果
            return {
                'success': True,
                'message': f'模拟买入成功: {symbol} {amount}股 @{price}',
                'order_id': f'XQ{datetime.now().strftime("%Y%m%d%H%M%S")}',
                'trade_amount': price * amount
            }
            
        except Exception as e:
            logger.error(f"❌ 雪球模拟买入失败: {e}")
            return {'success': False, 'message': f'模拟买入失败: {str(e)}'}
    
    def sell(self, symbol: str, price: float, amount: int) -> Dict:
        """卖出股票（模拟交易）"""
        try:
            # 雪球模拟交易：从自选股移除或减少权重
            logger.info(f"📉 雪球模拟卖出: {symbol} 价格:{price} 数量:{amount}")
            
            # 这里可以实现真实的雪球组合调仓逻辑
            # 目前返回模拟成功结果
            return {
                'success': True,
                'message': f'模拟卖出成功: {symbol} {amount}股 @{price}',
                'order_id': f'XQ{datetime.now().strftime("%Y%m%d%H%M%S")}',
                'trade_amount': price * amount
            }
            
        except Exception as e:
            logger.error(f"❌ 雪球模拟卖出失败: {e}")
            return {'success': False, 'message': f'模拟卖出失败: {str(e)}'}

def main():
    """主函数 - 测试统一交易引擎"""
    print("🚀 统一交易引擎测试")
    print("=" * 50)
    
    # 创建交易引擎
    engine = UnifiedTradingEngine()
    
    # 初始化
    if not engine.initialize():
        print("❌ 交易引擎初始化失败")
        return
    
    # 获取状态
    status = engine.get_status()
    print(f"📊 引擎状态: {status}")
    
    # 测试行情获取
    print("\n📈 测试行情获取:")
    quote = engine.get_quote('SH600519')
    if quote:
        print(f"   贵州茅台: {quote.get('current', 'N/A')} "
              f"涨跌: {quote.get('chg', 'N/A')}")
    
    # 测试账户信息
    print("\n💰 测试账户信息:")
    balance = engine.get_balance()
    if balance:
        print(f"   总资产: {balance.get('total_asset', 'N/A')}")
        print(f"   可用资金: {balance.get('available_cash', 'N/A')}")
    
    # 测试模拟交易
    print("\n🔄 测试模拟交易:")
    buy_result = engine.buy('SH600519', 1430.0, 100)
    print(f"   买入结果: {buy_result}")
    
    sell_result = engine.sell('SH600519', 1440.0, 50)
    print(f"   卖出结果: {sell_result}")

if __name__ == '__main__':
    main()
