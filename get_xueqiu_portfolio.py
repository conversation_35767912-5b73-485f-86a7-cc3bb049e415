#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取雪球真实组合代码
"""

import requests
import json
import re

def get_portfolio_list():
    """获取用户的组合列表"""
    
    # 从配置文件读取cookies
    try:
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        cookies_str = config.get('cookies', '')
    except:
        print("❌ 无法读取配置文件")
        return None
    
    # 解析cookies
    cookies = {}
    for cookie in cookies_str.split(';'):
        if '=' in cookie:
            key, value = cookie.strip().split('=', 1)
            cookies[key] = value
    
    # 请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://xueqiu.com/',
        'Accept': 'application/json, text/plain, */*',
    }
    
    try:
        # 获取用户信息
        user_url = 'https://xueqiu.com/v4/stock/portfolio/stocks.json'
        
        # 先尝试获取组合列表
        portfolio_url = 'https://xueqiu.com/cubes/nav_daily/all.json'
        
        print("🔍 正在获取组合列表...")
        
        # 尝试不同的API端点
        urls_to_try = [
            'https://xueqiu.com/v4/stock/portfolio/stocks.json',
            'https://xueqiu.com/cubes/nav_daily/all.json',
            'https://xueqiu.com/v4/stock/portfolio.json',
            'https://xueqiu.com/P/ZH123456',  # 示例组合页面
        ]
        
        for url in urls_to_try:
            try:
                print(f"尝试访问: {url}")
                response = requests.get(url, headers=headers, cookies=cookies, timeout=10)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
                    except:
                        print(f"响应内容: {response.text[:500]}...")
                        
                        # 尝试从HTML中提取组合代码
                        portfolio_codes = re.findall(r'ZH\d+', response.text)
                        if portfolio_codes:
                            print(f"找到组合代码: {portfolio_codes}")
                            return portfolio_codes
                else:
                    print(f"请求失败: {response.status_code}")
                    
            except Exception as e:
                print(f"请求异常: {str(e)}")
                continue
        
        return None
        
    except Exception as e:
        print(f"❌ 获取组合列表失败: {str(e)}")
        return None

def manual_input_portfolio():
    """手动输入组合代码"""
    print("\n" + "="*50)
    print("📝 手动获取组合代码步骤：")
    print("="*50)
    
    print("1. 打开浏览器，访问 https://xueqiu.com")
    print("2. 登录你的雪球账户")
    print("3. 点击顶部菜单的「组合」")
    print("4. 选择或创建一个模拟组合")
    print("5. 进入组合页面，从URL中复制组合代码")
    print("   例如：https://xueqiu.com/P/ZH123456 中的 ZH123456")
    
    print("\n💡 如果没有组合，请先创建一个模拟组合：")
    print("1. 点击「创建组合」")
    print("2. 选择「模拟组合」")
    print("3. 填写组合名称和描述")
    print("4. 创建成功后获取组合代码")
    
    while True:
        portfolio_code = input("\n请输入组合代码（格式如ZH123456）: ").strip()
        
        if not portfolio_code:
            print("❌ 组合代码不能为空")
            continue
            
        if not portfolio_code.startswith('ZH'):
            print("❌ 组合代码应该以ZH开头")
            continue
            
        if len(portfolio_code) < 8:
            print("❌ 组合代码长度不正确")
            continue
            
        return portfolio_code

def update_config(portfolio_code):
    """更新配置文件"""
    try:
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config['portfolio_code'] = portfolio_code
        
        with open('xq.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已更新，组合代码: {portfolio_code}")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置失败: {str(e)}")
        return False

def test_portfolio(portfolio_code):
    """测试组合代码是否有效"""
    try:
        import easytrader
        
        print(f"\n🧪 测试组合代码: {portfolio_code}")
        
        # 创建雪球交易用户
        user = easytrader.use('xq')
        user.prepare('xq.json')
        
        # 测试获取账户信息
        balance = user.balance
        print("✅ 账户连接成功！")
        
        # 测试获取持仓
        position = user.position
        print("✅ 持仓信息获取成功！")
        print(f"持仓数量: {len(position) if position else 0}")
        
        if position:
            print("持仓详情:")
            for pos in position[:3]:  # 显示前3个持仓
                print(f"  {pos.get('stock_name', 'N/A')} ({pos.get('stock_code', 'N/A')})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔍 雪球组合代码获取工具")
    print("="*50)
    
    # 尝试自动获取
    print("1. 尝试自动获取组合列表...")
    portfolio_codes = get_portfolio_list()
    
    if portfolio_codes:
        print(f"✅ 找到组合代码: {portfolio_codes}")
        
        if len(portfolio_codes) == 1:
            portfolio_code = portfolio_codes[0]
        else:
            print("发现多个组合代码，请选择：")
            for i, code in enumerate(portfolio_codes):
                print(f"{i+1}. {code}")
            
            while True:
                try:
                    choice = int(input("请选择组合代码 (输入序号): ")) - 1
                    if 0 <= choice < len(portfolio_codes):
                        portfolio_code = portfolio_codes[choice]
                        break
                    else:
                        print("❌ 无效选择")
                except ValueError:
                    print("❌ 请输入数字")
    else:
        print("❌ 自动获取失败，请手动输入")
        portfolio_code = manual_input_portfolio()
    
    # 更新配置
    if update_config(portfolio_code):
        # 测试配置
        if test_portfolio(portfolio_code):
            print("\n🎉 配置完成！现在可以正常使用Web交易界面了")
        else:
            print("\n⚠️ 配置已保存，但测试失败，请检查组合代码是否正确")
    else:
        print("\n❌ 配置更新失败")

if __name__ == '__main__':
    main()
