#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查VeighNa服务启动状态
"""

import time
import psutil
import sys
from datetime import datetime

def check_vnpy_process():
    """检查VeighNa进程"""
    print("🔍 检查VeighNa进程状态")
    print("=" * 50)
    
    vnpy_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('run_vnpy_trader.py' in cmd for cmd in cmdline):
                vnpy_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if vnpy_processes:
        print(f"✅ 找到 {len(vnpy_processes)} 个VeighNa进程:")
        for proc in vnpy_processes:
            print(f"   PID: {proc['pid']}, 命令: {' '.join(proc['cmdline'])}")
        return True
    else:
        print("❌ 未找到VeighNa进程")
        return False

def check_python_processes():
    """检查Python进程"""
    print("\n🐍 检查Python进程")
    print("=" * 50)
    
    python_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'].lower().startswith('python'):
                cmdline = proc.info['cmdline']
                if cmdline and len(cmdline) > 1:
                    python_processes.append({
                        'pid': proc.info['pid'],
                        'script': cmdline[-1] if cmdline else 'N/A'
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if python_processes:
        print(f"✅ 找到 {len(python_processes)} 个Python进程:")
        for proc in python_processes:
            print(f"   PID: {proc['pid']}, 脚本: {proc['script']}")
    else:
        print("❌ 未找到Python进程")

def check_qt_processes():
    """检查Qt相关进程"""
    print("\n🖥️ 检查Qt图形界面进程")
    print("=" * 50)
    
    qt_keywords = ['qt', 'gui', 'vnpy', 'trader']
    qt_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            name = proc.info['name'].lower()
            cmdline = ' '.join(proc.info['cmdline']).lower() if proc.info['cmdline'] else ''
            
            if any(keyword in name or keyword in cmdline for keyword in qt_keywords):
                qt_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if qt_processes:
        print(f"✅ 找到 {len(qt_processes)} 个相关进程:")
        for proc in qt_processes:
            print(f"   PID: {proc['pid']}, 名称: {proc['name']}")
    else:
        print("❌ 未找到Qt相关进程")

def show_startup_guide():
    """显示启动指南"""
    print("\n🚀 VeighNa启动指南")
    print("=" * 50)
    
    print("如果VeighNa没有正常启动，请尝试:")
    print()
    print("1. 检查Python环境:")
    print("   python --version")
    print("   pip list | grep vnpy")
    print()
    print("2. 手动启动VeighNa:")
    print("   python run_vnpy_trader.py")
    print()
    print("3. 检查错误信息:")
    print("   查看控制台输出的错误信息")
    print("   检查是否有模块缺失")
    print()
    print("4. 使用替代启动方式:")
    print("   双击 start_vnpy.bat")
    print("   或者直接运行 python -m vnpy.trader.ui")
    print()
    print("5. 如果图形界面没有显示:")
    print("   - 检查是否有防火墙阻止")
    print("   - 尝试以管理员权限运行")
    print("   - 检查显示器设置")

def monitor_startup():
    """监控启动过程"""
    print("\n⏱️ 监控VeighNa启动过程")
    print("=" * 50)
    
    print("正在监控启动过程，请稍等...")
    
    for i in range(30):  # 监控30秒
        time.sleep(1)
        
        # 检查进程
        vnpy_running = check_vnpy_process()
        
        if vnpy_running:
            print(f"\n✅ VeighNa已成功启动! (耗时: {i+1}秒)")
            print("💡 如果图形界面没有显示，请检查任务栏或最小化窗口")
            return True
        
        # 显示进度
        if i % 5 == 0:
            print(f"   等待中... ({i+1}/30秒)")
    
    print("\n⚠️ 启动监控超时")
    print("VeighNa可能需要更长时间启动，或者遇到了问题")
    return False

def main():
    """主函数"""
    print("🎯 VeighNa服务启动状态检查")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 检查当前进程状态
    vnpy_running = check_vnpy_process()
    check_python_processes()
    check_qt_processes()
    
    if not vnpy_running:
        print("\n🚀 VeighNa未运行，开始监控启动过程...")
        startup_success = monitor_startup()
        
        if not startup_success:
            show_startup_guide()
    else:
        print("\n✅ VeighNa已在运行中!")
        print("💡 如果看不到图形界面，请检查:")
        print("   - 任务栏是否有VeighNa图标")
        print("   - 窗口是否被最小化")
        print("   - 是否在其他显示器上")
    
    print(f"\n📊 当前系统状态:")
    print(f"   CPU使用率: {psutil.cpu_percent()}%")
    print(f"   内存使用率: {psutil.virtual_memory().percent}%")
    print(f"   运行的Python进程数: {len([p for p in psutil.process_iter() if 'python' in p.name().lower()])}")

if __name__ == "__main__":
    main()
