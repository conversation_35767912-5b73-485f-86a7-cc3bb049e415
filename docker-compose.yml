# A股量化交易平台 - TimescaleDB配置
# 使用Docker Compose部署TimescaleDB数据库

version: '3.8'

services:
  # TimescaleDB数据库服务
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: qtp_timescaledb
    restart: unless-stopped
    
    # 环境变量配置
    environment:
      # 数据库配置
      POSTGRES_DB: quantitative_trading
      POSTGRES_USER: qtp_user
      POSTGRES_PASSWORD: qtp_password_2024
      
      # TimescaleDB配置
      TIMESCALEDB_TELEMETRY: off
      
      # 性能优化配置
      POSTGRES_SHARED_PRELOAD_LIBRARIES: timescaledb
      POSTGRES_MAX_CONNECTIONS: 200
      POSTGRES_SHARED_BUFFERS: 256MB
      POSTGRES_EFFECTIVE_CACHE_SIZE: 1GB
      POSTGRES_WORK_MEM: 4MB
      POSTGRES_MAINTENANCE_WORK_MEM: 64MB
    
    # 端口映射
    ports:
      - "5432:5432"
    
    # 数据卷挂载
    volumes:
      # 数据持久化
      - timescaledb_data:/var/lib/postgresql/data
      
      # 初始化脚本
      - ./jiaoben/shujuku_chushihua.sql:/docker-entrypoint-initdb.d/01_init.sql
      
      # 配置文件
      - ./config/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./config/pg_hba.conf:/etc/postgresql/pg_hba.conf
    
    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U qtp_user -d quantitative_trading"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 网络配置
    networks:
      - qtp_network
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis缓存服务 (可选)
  redis:
    image: redis:7-alpine
    container_name: qtp_redis
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "6379:6379"
    
    # 配置
    command: redis-server --appendonly yes --requirepass redis_password_2024
    
    # 数据卷
    volumes:
      - redis_data:/data
    
    # 健康检查
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    # 网络配置
    networks:
      - qtp_network

  # pgAdmin管理界面 (开发环境)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: qtp_pgadmin
    restart: unless-stopped
    
    # 环境变量
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin_password_2024
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    
    # 端口映射
    ports:
      - "8080:80"
    
    # 数据卷
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    
    # 依赖服务
    depends_on:
      timescaledb:
        condition: service_healthy
    
    # 网络配置
    networks:
      - qtp_network
    
    # 仅在开发环境启用
    profiles:
      - dev

# 数据卷定义
volumes:
  timescaledb_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/timescaledb
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis
  
  pgadmin_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pgadmin

# 网络定义
networks:
  qtp_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
