// SCSS混合器文件

// 响应式断点混合器
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1}) {
      @content;
    }
  }
  @else if $breakpoint == sm {
    @media (max-width: #{$breakpoint-sm - 1}) {
      @content;
    }
  }
  @else if $breakpoint == md {
    @media (max-width: #{$breakpoint-md - 1}) {
      @content;
    }
  }
  @else if $breakpoint == lg {
    @media (max-width: #{$breakpoint-lg - 1}) {
      @content;
    }
  }
  @else if $breakpoint == xl {
    @media (max-width: #{$breakpoint-xl - 1}) {
      @content;
    }
  }
}

// 最小宽度响应式
@mixin respond-above($breakpoint) {
  @if $breakpoint == xs {
    @media (min-width: #{$breakpoint-xs}) {
      @content;
    }
  }
  @else if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) {
      @content;
    }
  }
  @else if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  }
  @else if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
  @else if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) {
      @content;
    }
  }
}

// Flexbox布局混合器
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// 文本省略混合器
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 固定宽高比
@mixin aspect-ratio($width: 16, $height: 9) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: percentage($height / $width);
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// 按钮样式混合器
@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;
  
  &:hover,
  &:focus {
    color: $color;
    background-color: darken($background, 7.5%);
    border-color: darken($border, 10%);
  }
  
  &:active {
    color: $color;
    background-color: darken($background, 10%);
    border-color: darken($border, 12.5%);
  }
  
  &.is-disabled,
  &:disabled {
    color: $color;
    background-color: $background;
    border-color: $border;
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 卡片阴影混合器
@mixin card-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  } @else if $level == 2 {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  } @else if $level == 3 {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
}

// 过渡动画混合器
@mixin transition($property: all, $duration: 0.3s, $timing: ease) {
  transition: $property $duration $timing;
}

// 渐变背景混合器
@mixin gradient-bg($start-color, $end-color, $direction: to right) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 滚动条样式混合器
@mixin scrollbar($width: 8px, $track-color: #f1f1f1, $thumb-color: #c1c1c1) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $width / 2;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}

// 盈亏颜色混合器
@mixin profit-loss-color($value) {
  @if $value > 0 {
    color: $profit-color;
  } @else if $value < 0 {
    color: $loss-color;
  } @else {
    color: $neutral-color;
  }
}

// 表格行悬停效果
@mixin table-row-hover {
  &:hover {
    background-color: $table-row-hover-bg;
    cursor: pointer;
  }
}

// 加载状态混合器
@mixin loading-state {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
}

// 禁用状态混合器
@mixin disabled-state {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

// 动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 动画类混合器
@mixin animate($name, $duration: 0.3s, $timing: ease, $delay: 0s, $iteration: 1, $direction: normal, $fill-mode: both) {
  animation-name: $name;
  animation-duration: $duration;
  animation-timing-function: $timing;
  animation-delay: $delay;
  animation-iteration-count: $iteration;
  animation-direction: $direction;
  animation-fill-mode: $fill-mode;
}
