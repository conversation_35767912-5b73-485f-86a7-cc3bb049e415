@echo off
chcp 65001 >nul
echo 🚀 VeighNa Studio 社区版启动脚本
echo ================================
echo.

:: 设置可能的安装路径
set "PATHS[0]=C:\Program Files\VeighNa Studio\VeighNa Studio.exe"
set "PATHS[1]=C:\Program Files (x86)\VeighNa Studio\VeighNa Studio.exe"
set "PATHS[2]=C:\Users\<USER>\AppData\Local\VeighNa Studio\VeighNa Studio.exe"
set "PATHS[3]=C:\Users\<USER>\AppData\Roaming\VeighNa Studio\VeighNa Studio.exe"
set "PATHS[4]=C:\VeighNa Studio\VeighNa Studio.exe"
set "PATHS[5]=D:\Program Files\VeighNa Studio\VeighNa Studio.exe"
set "PATHS[6]=D:\VeighNa Studio\VeighNa Studio.exe"

echo 🔍 正在查找VeighNa Studio安装位置...
echo.

:: 检查每个可能的路径
for /L %%i in (0,1,6) do (
    call set "current_path=%%PATHS[%%i]%%"
    call :check_path "!current_path!" %%i
)

:: 如果没找到，尝试通过注册表查找
echo 📋 尝试通过注册表查找...
for /f "tokens=2*" %%a in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall" /s /f "VeighNa" 2^>nul ^| findstr "InstallLocation"') do (
    if exist "%%b\VeighNa Studio.exe" (
        echo ✅ 通过注册表找到: %%b\VeighNa Studio.exe
        start "" "%%b\VeighNa Studio.exe"
        goto :found
    )
)

:: 尝试通过开始菜单快捷方式启动
echo 🔗 尝试通过开始菜单启动...
powershell -Command "& {$shell = New-Object -ComObject Shell.Application; $folder = $shell.Namespace('shell:Programs'); $items = $folder.Items(); foreach($item in $items) { if($item.Name -like '*VeighNa*') { $item.InvokeVerb('open'); exit 0 } } exit 1 }" 2>nul
if %errorlevel% equ 0 (
    echo ✅ 通过开始菜单启动成功
    goto :found
)

:: 如果都没找到，提供手动查找指导
echo.
echo ❌ 未找到VeighNa Studio安装位置
echo.
echo 💡 请手动查找VeighNa Studio:
echo    1. 按Win键搜索 "VeighNa Studio"
echo    2. 查看桌面是否有VeighNa Studio图标
echo    3. 检查以下目录:
echo       - C:\Program Files\VeighNa Studio\
echo       - C:\Program Files (x86)\VeighNa Studio\
echo       - %USERPROFILE%\AppData\Local\VeighNa Studio\
echo.
echo 📥 如果没有安装，请从官网下载:
echo    https://www.vnpy.com/
echo.

:: 提供备用启动方式
echo 🔄 备用启动方式:
echo    1. 使用Python版本: python run_vnpy_trader.py
echo    2. 下载官方安装包重新安装
echo.

goto :end

:check_path
set "path_to_check=%~1"
set "index=%~2"
if exist "%path_to_check%" (
    echo ✅ 找到VeighNa Studio: %path_to_check%
    echo 🚀 正在启动...
    start "" "%path_to_check%"
    goto :found
) else (
    echo ⚪ 检查路径 %index%: 未找到
)
goto :eof

:found
echo.
echo ✅ VeighNa Studio 启动成功!
echo.
echo 💡 使用提示:
echo    - 如果是首次使用，请先配置数据源
echo    - 模拟交易请查看 功能 → 模拟账户
echo    - 策略测试请查看 功能 → CTA策略
echo    - 策略回测请查看 功能 → CTA回测
echo.
echo 📖 详细使用指南请查看项目目录中的文档
timeout /t 3 >nul
goto :end

:end
pause
