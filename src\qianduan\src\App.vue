<template>
  <div id="app" class="app-container">
    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition name="fade" mode="out-in">
        <keep-alive :include="keepAliveComponents">
          <component :is="Component" :key="route.path" />
        </keep-alive>
      </transition>
    </router-view>
    
    <!-- 全局加载遮罩 -->
    <el-loading
      v-loading="globalLoading"
      :text="loadingText"
      background="rgba(0, 0, 0, 0.8)"
      element-loading-spinner="el-icon-loading"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/shangdian/app'

// 使用应用状态
const appStore = useAppStore()

// 计算属性
const globalLoading = computed(() => appStore.loading)
const loadingText = computed(() => appStore.loadingText)
const keepAliveComponents = computed(() => appStore.keepAliveComponents)

// 监听系统主题变化
const prefersDark = window.matchMedia('(prefers-color-scheme: dark)')
const handleThemeChange = (e: MediaQueryListEvent) => {
  if (appStore.theme === 'auto') {
    document.documentElement.classList.toggle('dark', e.matches)
  }
}

prefersDark.addEventListener('change', handleThemeChange)

// 初始化主题
const initTheme = () => {
  const theme = appStore.theme
  if (theme === 'dark') {
    document.documentElement.classList.add('dark')
  } else if (theme === 'light') {
    document.documentElement.classList.remove('dark')
  } else {
    // auto模式
    document.documentElement.classList.toggle('dark', prefersDark.matches)
  }
}

// 组件挂载时初始化
onMounted(() => {
  initTheme()
  
  // 监听主题变化
  watch(() => appStore.theme, () => {
    initTheme()
  })
})

// 组件卸载时清理
onUnmounted(() => {
  prefersDark.removeEventListener('change', handleThemeChange)
})
</script>

<style lang="scss">
.app-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-light);
  border-radius: 4px;
  
  &:hover {
    background: var(--el-border-color);
  }
}

// 暗色主题下的滚动条
.dark {
  ::-webkit-scrollbar-track {
    background: #1a1a1a;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #404040;
    
    &:hover {
      background: #606060;
    }
  }
}

// 全局字体优化
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
               'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 
               'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 
               'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Element Plus 组件样式调整
.el-button {
  font-weight: 500;
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-table {
  .el-table__header {
    th {
      background-color: var(--el-fill-color-light);
      font-weight: 600;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    font-size: 14px;
  }
}
</style>
