#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取真实的雪球持仓数据
基于配置文件中的portfolio_code获取真实持仓
"""

import json
import logging
import easytrader
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealHoldingsGetter:
    """真实持仓获取器"""
    
    def __init__(self):
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.account = config.get('account', '')
            self.password = config.get('password', '')
            self.portfolio_code = config.get('portfolio_code', '')
            self.cookies = config.get('cookies', '')
            
            logger.info(f"✅ 配置加载成功")
            logger.info(f"   账户: {self.account}")
            logger.info(f"   组合代码: {self.portfolio_code}")
            
            # 设置pysnowball token
            cookies_str = self.cookies
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ pysnowball Token设置成功: u={u}")
            
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
    
    def get_holdings_via_easytrader(self):
        """通过easytrader获取持仓"""
        logger.info("🔧 通过easytrader获取持仓")
        logger.info("=" * 60)
        
        try:
            # 创建easytrader实例
            xq = easytrader.use('xq')
            
            # 准备登录信息
            login_info = {
                'cookies': self.cookies,
                'portfolio_code': self.portfolio_code,
                'portfolio_market': 'cn'
            }
            
            logger.info(f"🔑 使用组合代码: {self.portfolio_code}")
            
            # 尝试登录
            xq.prepare(**login_info)
            logger.info(f"✅ easytrader登录成功")
            
            # 获取余额
            try:
                balance = xq.balance
                logger.info(f"💰 余额信息: {balance}")
            except Exception as e:
                logger.error(f"❌ 获取余额失败: {e}")
                balance = None
            
            # 获取持仓
            try:
                position = xq.position
                logger.info(f"📊 持仓信息: {position}")
                return {
                    'success': True,
                    'balance': balance,
                    'position': position,
                    'source': 'easytrader'
                }
            except Exception as e:
                logger.error(f"❌ 获取持仓失败: {e}")
                return {
                    'success': False,
                    'error': str(e),
                    'balance': balance,
                    'source': 'easytrader'
                }
                
        except Exception as e:
            logger.error(f"❌ easytrader连接失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'source': 'easytrader'
            }
    
    def get_holdings_via_pysnowball(self):
        """通过pysnowball获取持仓"""
        logger.info("🐍 通过pysnowball获取持仓")
        logger.info("=" * 60)
        
        results = {}
        
        # 尝试不同的组合代码格式
        portfolio_codes = [
            self.portfolio_code,  # 原始代码
            f"ZH{self.portfolio_code}",  # ZH前缀
            f"ZH{self.portfolio_code.upper()}",  # ZH前缀+大写
        ]
        
        # 如果portfolio_code是"test"，尝试更多变体
        if self.portfolio_code.lower() == 'test':
            portfolio_codes.extend([
                "ZH000001", "ZH000002", "ZH000003",
                "TEST", "Test", "测试",
                f"ZH{hash('test') % 1000000:06d}",
            ])
        
        for code in portfolio_codes:
            try:
                logger.info(f"\n🔍 尝试组合代码: {code}")
                
                # 尝试获取组合当前调仓
                try:
                    current_result = ball.rebalancing_current(code)
                    if current_result and 'last_rb' in current_result:
                        logger.info(f"   ✅ 获取到当前调仓数据")
                        results[f'{code}_current'] = current_result
                    else:
                        logger.info(f"   ⚠️ 当前调仓数据为空")
                except Exception as e:
                    logger.info(f"   ❌ 当前调仓失败: {e}")
                
                # 尝试获取组合历史调仓
                try:
                    history_result = ball.rebalancing_history(code)
                    if history_result and 'list' in history_result:
                        logger.info(f"   ✅ 获取到历史调仓数据: {len(history_result['list'])} 条")
                        results[f'{code}_history'] = history_result
                    else:
                        logger.info(f"   ⚠️ 历史调仓数据为空")
                except Exception as e:
                    logger.info(f"   ❌ 历史调仓失败: {e}")
                
                # 尝试获取组合净值
                try:
                    nav_result = ball.nav_daily(code)
                    if nav_result:
                        logger.info(f"   ✅ 获取到净值数据")
                        results[f'{code}_nav'] = nav_result
                    else:
                        logger.info(f"   ⚠️ 净值数据为空")
                except Exception as e:
                    logger.info(f"   ❌ 净值获取失败: {e}")
                    
            except Exception as e:
                logger.info(f"   ❌ 组合 {code} 处理失败: {e}")
        
        return {
            'success': len(results) > 0,
            'results': results,
            'source': 'pysnowball'
        }
    
    def search_user_portfolios(self):
        """搜索用户的组合"""
        logger.info("🔍 搜索用户组合")
        logger.info("=" * 60)
        
        try:
            # 获取用户ID
            u = ''
            for cookie in self.cookies.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'u':
                        u = value
                        break
            
            if not u:
                logger.error("❌ 无法获取用户ID")
                return {'success': False, 'error': '无法获取用户ID'}
            
            logger.info(f"👤 用户ID: {u}")
            
            # 搜索包含"test"和"测试"的组合
            search_terms = ['test', '测试', self.portfolio_code]
            found_portfolios = []
            
            for term in search_terms:
                try:
                    logger.info(f"\n🔍 搜索关键词: {term}")
                    
                    # 使用雪球搜索API
                    search_result = ball.search(term, count=50)
                    
                    if search_result and 'list' in search_result:
                        cubes = search_result['list']
                        logger.info(f"   找到 {len(cubes)} 个结果")
                        
                        # 查找属于当前用户的组合
                        for cube in cubes:
                            cube_user_id = str(cube.get('user_id', ''))
                            cube_name = cube.get('name', '')
                            cube_symbol = cube.get('symbol', '')
                            
                            if cube_user_id == u:
                                logger.info(f"   🎯 找到用户组合: {cube_name} ({cube_symbol})")
                                found_portfolios.append(cube)
                    
                except Exception as e:
                    logger.warning(f"   搜索'{term}'失败: {e}")
            
            return {
                'success': len(found_portfolios) > 0,
                'portfolios': found_portfolios,
                'source': 'search'
            }
            
        except Exception as e:
            logger.error(f"❌ 搜索用户组合失败: {e}")
            return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    print("🎯 获取真实雪球持仓数据")
    print("基于配置文件中的portfolio_code")
    print("=" * 80)
    
    getter = RealHoldingsGetter()
    
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'portfolio_code': getter.portfolio_code,
        'easytrader_result': None,
        'pysnowball_result': None,
        'search_result': None
    }
    
    # 1. 通过easytrader获取
    logger.info("\n🔧 1. 通过easytrader获取持仓")
    easytrader_result = getter.get_holdings_via_easytrader()
    all_results['easytrader_result'] = easytrader_result
    
    # 2. 通过pysnowball获取
    logger.info("\n🐍 2. 通过pysnowball获取持仓")
    pysnowball_result = getter.get_holdings_via_pysnowball()
    all_results['pysnowball_result'] = pysnowball_result
    
    # 3. 搜索用户组合
    logger.info("\n🔍 3. 搜索用户组合")
    search_result = getter.search_user_portfolios()
    all_results['search_result'] = search_result
    
    # 4. 汇总结果
    logger.info("\n📊 4. 结果汇总")
    logger.info("=" * 60)
    
    found_real_data = False
    
    # 检查easytrader结果
    if easytrader_result.get('success', False):
        logger.info("✅ easytrader: 成功获取持仓数据")
        if easytrader_result.get('balance'):
            logger.info(f"   💰 余额: {easytrader_result['balance']}")
        if easytrader_result.get('position'):
            logger.info(f"   📊 持仓: {len(easytrader_result['position'])} 只股票")
            for pos in easytrader_result['position']:
                logger.info(f"     • {pos.get('stock_name', 'N/A')} ({pos.get('stock_code', 'N/A')}): {pos.get('current_amount', 0)} 股")
        found_real_data = True
    else:
        logger.warning(f"⚠️ easytrader: {easytrader_result.get('error', '获取失败')}")
    
    # 检查pysnowball结果
    if pysnowball_result.get('success', False):
        logger.info(f"✅ pysnowball: 找到 {len(pysnowball_result['results'])} 个有效数据")
        for key, data in pysnowball_result['results'].items():
            logger.info(f"   📊 {key}: 数据长度 {len(str(data))}")
        found_real_data = True
    else:
        logger.warning("⚠️ pysnowball: 未找到有效数据")
    
    # 检查搜索结果
    if search_result.get('success', False):
        logger.info(f"✅ 搜索: 找到 {len(search_result['portfolios'])} 个用户组合")
        for portfolio in search_result['portfolios']:
            logger.info(f"   🎯 {portfolio.get('name', 'N/A')} ({portfolio.get('symbol', 'N/A')})")
        found_real_data = True
    else:
        logger.warning("⚠️ 搜索: 未找到用户组合")
    
    if not found_real_data:
        logger.error("❌ 所有方法都未能获取到真实持仓数据")
        logger.info("💡 可能的原因:")
        logger.info("   1. portfolio_code配置不正确")
        logger.info("   2. 雪球组合不存在或已删除")
        logger.info("   3. API权限不足")
        logger.info("   4. 需要重新登录雪球")
    
    # 5. 保存结果
    try:
        with open('real_holdings_data.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        logger.info(f"\n💾 完整结果已保存到: real_holdings_data.json")
    except Exception as e:
        logger.error(f"\n❌ 保存结果失败: {e}")
    
    print(f"\n🎉 持仓数据获取完成！")
    
    if found_real_data:
        print(f"🎯 成功获取到真实持仓数据！")
    else:
        print(f"⚠️ 未能获取到真实持仓数据，请检查配置")

if __name__ == '__main__':
    main()
