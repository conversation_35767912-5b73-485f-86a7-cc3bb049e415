#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
雪球投资组合完整API操作
基于CSDN博客研究的正确雪球API调用方式
支持：创建组合、调仓、删除、修改等完整功能
参考：https://blog.csdn.net/CY19980216/article/details/82770410
"""

import json
import logging
import requests
import time
from datetime import datetime
from typing import Dict, List, Optional

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class XueqiuPortfolioAPI:
    """雪球投资组合完整API操作类"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.session = requests.Session()
        
        # 设置请求头（基于CSDN博客的研究）
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/******** Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
        self.session.headers.update(self.headers)
        
        # 雪球API端点（基于博客研究）
        self.api_endpoints = {
            # 登录相关
            'send_sms': 'https://xueqiu.com/account/sms/send_verification_code.json',
            'login': 'https://xueqiu.com/snowman/login',
            
            # 组合管理
            'cube_list': 'https://xueqiu.com/cubes/list.json',
            'cube_create': 'https://xueqiu.com/cubes/create.json',
            'cube_delete': 'https://xueqiu.com/cubes/delete.json',
            'cube_update': 'https://xueqiu.com/cubes/update.json',
            
            # 调仓操作（核心功能）
            'rebalancing': 'https://xueqiu.com/cubes/rebalancing.json',
            'rebalancing_history': 'https://xueqiu.com/cubes/rebalancing/history.json',
            'rebalancing_current': 'https://xueqiu.com/cubes/rebalancing/current.json',
            
            # 组合数据
            'cube_nav': 'https://xueqiu.com/cubes/nav_daily/all.json',
            'cube_quote': 'https://xueqiu.com/v4/stock/quote.json',
            
            # 股票搜索
            'stock_search': 'https://xueqiu.com/query/v1/suggest_stock.json'
        }
        
        self.is_logged_in = False
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 解析cookies
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)

            # 检查是否已登录
            if self.session.cookies.get('xq_a_token') and self.session.cookies.get('u'):
                self.is_logged_in = True
                logger.info("✅ 从配置文件加载登录状态成功")

                # 访问主页获取必要的token和cookies
                self.init_session()
            else:
                logger.warning("⚠️ 配置文件中没有有效的登录信息")

        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")

    def init_session(self):
        """初始化session，获取必要的token"""
        try:
            # 访问雪球主页，获取必要的cookies和token
            main_response = self.session.get('https://xueqiu.com/', timeout=10)

            if main_response.status_code == 200:
                logger.info("✅ 主页访问成功，session初始化完成")

                # 尝试访问组合页面，确保session有效
                cube_page = self.session.get('https://xueqiu.com/P/ZH000001', timeout=10)
                if cube_page.status_code == 200:
                    logger.info("✅ 组合页面访问成功，session验证通过")
                else:
                    logger.warning(f"⚠️ 组合页面访问失败: {cube_page.status_code}")
            else:
                logger.error(f"❌ 主页访问失败: {main_response.status_code}")

        except Exception as e:
            logger.error(f"❌ Session初始化失败: {e}")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            cookies_str = '; '.join([f"{cookie.name}={cookie.value}" for cookie in self.session.cookies])
            config = {'cookies': cookies_str}
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info("✅ 配置已保存")
            
        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")
    
    def send_sms_code(self, phone_number: str) -> bool:
        """发送短信验证码"""
        try:
            data = {
                'areacode': '86',
                'telephone': phone_number
            }
            
            response = self.session.post(self.api_endpoints['send_sms'], data=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('error_code') == 0:
                    logger.info("✅ 验证码发送成功")
                    return True
                else:
                    logger.error(f"❌ 验证码发送失败: {result.get('error_description', '未知错误')}")
                    return False
            else:
                logger.error(f"❌ 验证码发送请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 发送验证码异常: {e}")
            return False
    
    def login_with_sms(self, phone_number: str, verification_code: str = None) -> bool:
        """使用短信验证码登录"""
        try:
            # 如果没有提供验证码，先发送验证码
            if not verification_code:
                if not self.send_sms_code(phone_number):
                    return False
                verification_code = input("请输入收到的验证码: ")
            
            # 登录
            login_data = {
                'areacode': '86',
                'remember_me': 'true',
                'telephone': phone_number,
                'code': verification_code
            }
            
            response = self.session.post(self.api_endpoints['login'], data=login_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('user_id'):
                    self.is_logged_in = True
                    logger.info(f"✅ 登录成功: {result.get('screen_name', 'N/A')}")
                    
                    # 保存登录状态
                    self.save_config()
                    return True
                else:
                    logger.error(f"❌ 登录失败: {result}")
                    return False
            else:
                logger.error(f"❌ 登录请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 登录异常: {e}")
            return False
    
    def create_cube(self, name: str, description: str = "", market: str = "cn") -> Optional[str]:
        """创建投资组合"""
        try:
            if not self.is_logged_in:
                logger.error("❌ 请先登录")
                return None

            # 使用表单数据格式（基于CSDN博客的研究）
            create_data = {
                'name': name,
                'description': description or f"通过API创建于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                'market': market,  # cn=中国市场, us=美股, hk=港股
                'category': 'stock'  # stock=股票组合
            }

            # 使用表单数据而不是JSON
            response = self.session.post(self.api_endpoints['cube_create'], data=create_data)

            if response.status_code == 200:
                result = response.json()
                if result.get('id'):
                    cube_symbol = f"ZH{result['id']:06d}"
                    logger.info(f"✅ 组合创建成功: {name} ({cube_symbol})")
                    return cube_symbol
                else:
                    logger.error(f"❌ 组合创建失败: {result}")
                    return None
            else:
                logger.error(f"❌ 创建请求失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"❌ 创建组合异常: {e}")
            return None
    
    def delete_cube(self, cube_symbol: str) -> bool:
        """删除投资组合"""
        try:
            if not self.is_logged_in:
                logger.error("❌ 请先登录")
                return False
            
            delete_data = {
                'cube_symbol': cube_symbol
            }
            
            response = self.session.post(self.api_endpoints['cube_delete'], json=delete_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    logger.info(f"✅ 组合删除成功: {cube_symbol}")
                    return True
                else:
                    logger.error(f"❌ 组合删除失败: {result.get('error_description', '未知错误')}")
                    return False
            else:
                logger.error(f"❌ 删除请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 删除组合异常: {e}")
            return False
    
    def update_cube(self, cube_symbol: str, name: str = None, description: str = None) -> bool:
        """更新投资组合信息"""
        try:
            if not self.is_logged_in:
                logger.error("❌ 请先登录")
                return False
            
            update_data = {
                'cube_symbol': cube_symbol
            }
            
            if name:
                update_data['name'] = name
            if description:
                update_data['description'] = description
            
            response = self.session.post(self.api_endpoints['cube_update'], json=update_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    logger.info(f"✅ 组合更新成功: {cube_symbol}")
                    return True
                else:
                    logger.error(f"❌ 组合更新失败: {result.get('error_description', '未知错误')}")
                    return False
            else:
                logger.error(f"❌ 更新请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 更新组合异常: {e}")
            return False
    
    def rebalance_cube(self, cube_symbol: str, holdings: List[Dict], comment: str = "") -> bool:
        """调仓操作 - 雪球模拟交易的核心功能"""
        try:
            if not self.is_logged_in:
                logger.error("❌ 请先登录")
                return False

            # 构建调仓数据（基于CSDN博客的研究）
            # 将holdings转换为JSON字符串
            holdings_json = json.dumps(holdings)

            rebalancing_data = {
                'cube_symbol': cube_symbol,
                'holdings': holdings_json,  # 作为JSON字符串传递
                'comment': comment or f"API调仓于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            }

            # 使用表单数据格式
            response = self.session.post(self.api_endpoints['rebalancing'], data=rebalancing_data)

            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    logger.info(f"✅ 调仓成功: {cube_symbol}")
                    logger.info(f"   调仓说明: {comment}")

                    # 显示调仓详情
                    for holding in holdings:
                        action = "买入" if holding.get('weight', 0) > 0 else "清仓"
                        logger.info(f"   {action}: {holding.get('stock_name', 'N/A')} "
                                  f"权重: {holding.get('weight', 0):.1f}%")

                    return True
                else:
                    logger.error(f"❌ 调仓失败: {result.get('error_description', '未知错误')}")
                    return False
            else:
                logger.error(f"❌ 调仓请求失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"❌ 调仓异常: {e}")
            return False
    
    def add_stock_to_cube(self, cube_symbol: str, stock_symbol: str, weight: float, comment: str = "") -> bool:
        """添加股票到组合（买入操作）"""
        try:
            # 获取当前持仓
            current_holdings = self.get_current_holdings(cube_symbol)
            
            # 获取股票名称
            stock_name = self.get_stock_name(stock_symbol)
            
            # 构建新的持仓列表
            new_holdings = []
            stock_found = False
            
            for holding in current_holdings:
                if holding.get('stock_symbol') == stock_symbol:
                    # 更新现有持仓
                    holding['weight'] = weight
                    stock_found = True
                new_holdings.append(holding)
            
            if not stock_found:
                # 添加新股票
                new_holdings.append({
                    'stock_symbol': stock_symbol,
                    'stock_name': stock_name or stock_symbol,
                    'weight': weight
                })
            
            # 执行调仓
            buy_comment = comment or f"买入 {stock_name or stock_symbol} {weight}%"
            return self.rebalance_cube(cube_symbol, new_holdings, buy_comment)
            
        except Exception as e:
            logger.error(f"❌ 添加股票失败: {e}")
            return False
    
    def remove_stock_from_cube(self, cube_symbol: str, stock_symbol: str, comment: str = "") -> bool:
        """从组合移除股票（卖出操作）"""
        try:
            # 获取当前持仓
            current_holdings = self.get_current_holdings(cube_symbol)
            
            # 获取股票名称
            stock_name = self.get_stock_name(stock_symbol)
            
            # 构建新的持仓列表（移除指定股票）
            new_holdings = []
            
            for holding in current_holdings:
                if holding.get('stock_symbol') != stock_symbol:
                    new_holdings.append(holding)
            
            # 执行调仓
            sell_comment = comment or f"清仓 {stock_name or stock_symbol}"
            return self.rebalance_cube(cube_symbol, new_holdings, sell_comment)
            
        except Exception as e:
            logger.error(f"❌ 移除股票失败: {e}")
            return False
    
    def get_current_holdings(self, cube_symbol: str) -> List[Dict]:
        """获取组合当前持仓"""
        try:
            url = f"{self.api_endpoints['rebalancing_current']}?cube_symbol={cube_symbol}"
            response = self.session.get(url)
            
            if response.status_code == 200:
                result = response.json()
                last_rb = result.get('last_rb', {})
                holdings = last_rb.get('holdings', [])
                return holdings
            else:
                logger.error(f"❌ 获取持仓失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"❌ 获取持仓异常: {e}")
            return []
    
    def get_stock_name(self, stock_symbol: str) -> Optional[str]:
        """获取股票名称"""
        try:
            url = f"{self.api_endpoints['stock_search']}?q={stock_symbol}"
            response = self.session.get(url)
            
            if response.status_code == 200:
                result = response.json()
                if 'data' in result and result['data']:
                    for stock in result['data']:
                        if stock.get('code') == stock_symbol:
                            return stock.get('query', stock_symbol)
                    # 如果没有完全匹配，返回第一个结果
                    return result['data'][0].get('query', stock_symbol)
            
            return stock_symbol
            
        except Exception as e:
            logger.error(f"❌ 获取股票名称失败: {e}")
            return stock_symbol
    
    def get_cube_list(self) -> List[Dict]:
        """获取用户的投资组合列表"""
        try:
            if not self.is_logged_in:
                logger.error("❌ 请先登录")
                return []
            
            response = self.session.get(self.api_endpoints['cube_list'])
            
            if response.status_code == 200:
                result = response.json()
                cubes = result.get('list', [])
                logger.info(f"✅ 获取到 {len(cubes)} 个组合")
                return cubes
            else:
                logger.error(f"❌ 获取组合列表失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"❌ 获取组合列表异常: {e}")
            return []
    
    def test_all_operations(self):
        """测试所有操作"""
        logger.info("🧪 开始测试雪球投资组合完整API操作")
        logger.info("=" * 60)
        
        if not self.is_logged_in:
            logger.error("❌ 请先登录")
            return False
        
        # 1. 获取现有组合列表
        logger.info("📋 1. 获取组合列表")
        cubes = self.get_cube_list()
        
        # 2. 创建测试组合
        logger.info("\n📊 2. 创建测试组合")
        test_cube_name = f"API测试组合_{datetime.now().strftime('%m%d_%H%M')}"
        cube_symbol = self.create_cube(test_cube_name, "通过API创建的测试组合")
        
        if not cube_symbol:
            logger.error("❌ 创建组合失败，停止测试")
            return False
        
        # 3. 添加股票到组合
        logger.info("\n📈 3. 添加股票到组合")
        success = self.add_stock_to_cube(cube_symbol, "SH600519", 20.0, "测试买入贵州茅台")
        if success:
            logger.info("✅ 股票添加成功")
        
        # 4. 再添加一只股票
        logger.info("\n📈 4. 再添加一只股票")
        success = self.add_stock_to_cube(cube_symbol, "SZ000001", 15.0, "测试买入平安银行")
        if success:
            logger.info("✅ 股票添加成功")
        
        # 5. 获取当前持仓
        logger.info("\n💼 5. 获取当前持仓")
        holdings = self.get_current_holdings(cube_symbol)
        logger.info(f"   当前持仓: {len(holdings)} 只股票")
        for holding in holdings:
            logger.info(f"     {holding.get('stock_name', 'N/A')} "
                      f"({holding.get('stock_symbol', 'N/A')}): "
                      f"{holding.get('weight', 0):.1f}%")
        
        # 6. 移除一只股票
        logger.info("\n📉 6. 移除股票")
        success = self.remove_stock_from_cube(cube_symbol, "SZ000001", "测试清仓平安银行")
        if success:
            logger.info("✅ 股票移除成功")
        
        # 7. 更新组合信息
        logger.info("\n📝 7. 更新组合信息")
        success = self.update_cube(cube_symbol, 
                                 name=f"{test_cube_name}_已更新",
                                 description="组合信息已通过API更新")
        if success:
            logger.info("✅ 组合信息更新成功")
        
        # 8. 删除测试组合（可选）
        logger.info("\n🗑️ 8. 删除测试组合")
        choice = input("是否删除测试组合？(y/n): ").strip().lower()
        if choice == 'y':
            success = self.delete_cube(cube_symbol)
            if success:
                logger.info("✅ 组合删除成功")
        else:
            logger.info(f"⚠️ 保留测试组合: {cube_symbol}")
        
        logger.info("\n🎉 API测试完成！")
        return True

def main():
    """主函数"""
    print("🚀 雪球投资组合完整API操作测试")
    print("基于CSDN博客研究的正确API调用方式")
    print("=" * 60)
    
    # 创建API实例
    api = XueqiuPortfolioAPI()
    
    # 检查登录状态
    if not api.is_logged_in:
        print("⚠️ 未登录，请先登录")
        phone = input("请输入手机号码: ")
        if api.login_with_sms(phone):
            print("✅ 登录成功")
        else:
            print("❌ 登录失败")
            return
    
    # 运行完整测试
    api.test_all_operations()

if __name__ == '__main__':
    main()
