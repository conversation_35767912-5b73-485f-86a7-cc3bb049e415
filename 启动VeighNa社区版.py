#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa Studio 社区版启动脚本
便于开发和调试的Python启动器
"""

import os
import sys
import subprocess
import winreg
import logging
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VeighNaStudioLauncher:
    """VeighNa Studio启动器"""
    
    def __init__(self):
        self.possible_paths = [
            r"C:\Program Files\VeighNa Studio\VeighNa Studio.exe",
            r"C:\Program Files (x86)\VeighNa Studio\VeighNa Studio.exe", 
            r"C:\Users\<USER>\AppData\Local\VeighNa Studio\VeighNa Studio.exe".format(os.getenv('USERNAME')),
            r"C:\Users\<USER>\AppData\Roaming\VeighNa Studio\VeighNa Studio.exe".format(os.getenv('USERNAME')),
            r"C:\VeighNa Studio\VeighNa Studio.exe",
            r"D:\Program Files\VeighNa Studio\VeighNa Studio.exe",
            r"D:\VeighNa Studio\VeighNa Studio.exe",
        ]
        
        self.found_path = None
    
    def print_header(self):
        """打印启动器标题"""
        print("🚀 VeighNa Studio 社区版启动器")
        print("=" * 60)
        print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Python版本: {sys.version}")
        print(f"工作目录: {os.getcwd()}")
        print("=" * 60)
    
    def check_file_paths(self):
        """检查文件路径"""
        logger.info("🔍 正在查找VeighNa Studio安装位置...")
        
        for i, path in enumerate(self.possible_paths):
            logger.info(f"检查路径 {i+1}: {path}")
            
            if os.path.exists(path):
                logger.info(f"✅ 找到VeighNa Studio: {path}")
                self.found_path = path
                return True
            else:
                logger.info(f"⚪ 路径不存在")
        
        return False
    
    def check_registry(self):
        """通过注册表查找"""
        logger.info("📋 尝试通过注册表查找...")
        
        try:
            # 查找卸载信息中的VeighNa
            uninstall_key = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"
            
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, uninstall_key) as key:
                i = 0
                while True:
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        subkey_path = f"{uninstall_key}\\{subkey_name}"
                        
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, subkey_path) as subkey:
                            try:
                                display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                if "VeighNa" in display_name:
                                    try:
                                        install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                        exe_path = os.path.join(install_location, "VeighNa Studio.exe")
                                        
                                        if os.path.exists(exe_path):
                                            logger.info(f"✅ 通过注册表找到: {exe_path}")
                                            self.found_path = exe_path
                                            return True
                                    except FileNotFoundError:
                                        pass
                            except FileNotFoundError:
                                pass
                        
                        i += 1
                    except OSError:
                        break
        
        except Exception as e:
            logger.warning(f"注册表查找失败: {e}")
        
        return False
    
    def check_start_menu(self):
        """检查开始菜单快捷方式"""
        logger.info("🔗 检查开始菜单快捷方式...")
        
        start_menu_paths = [
            os.path.expandvars(r"%APPDATA%\Microsoft\Windows\Start Menu\Programs"),
            os.path.expandvars(r"%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs"),
        ]
        
        for start_path in start_menu_paths:
            if os.path.exists(start_path):
                for root, dirs, files in os.walk(start_path):
                    for file in files:
                        if "VeighNa" in file and file.endswith(".lnk"):
                            shortcut_path = os.path.join(root, file)
                            logger.info(f"✅ 找到快捷方式: {shortcut_path}")
                            
                            # 尝试通过快捷方式启动
                            try:
                                os.startfile(shortcut_path)
                                return True
                            except Exception as e:
                                logger.warning(f"快捷方式启动失败: {e}")
        
        return False
    
    def launch_executable(self):
        """启动可执行文件"""
        if not self.found_path:
            return False
        
        try:
            logger.info(f"🚀 正在启动: {self.found_path}")
            
            # 使用subprocess启动，不阻塞当前进程
            process = subprocess.Popen([self.found_path], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE)
            
            logger.info(f"✅ VeighNa Studio 启动成功! PID: {process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动失败: {e}")
            return False
    
    def launch_python_version(self):
        """启动Python版本作为备用"""
        logger.info("🐍 尝试启动Python版本...")
        
        try:
            # 检查是否有我们的Python启动脚本
            if os.path.exists("run_vnpy_trader.py"):
                logger.info("找到Python启动脚本: run_vnpy_trader.py")
                
                # 启动Python版本
                process = subprocess.Popen([sys.executable, "run_vnpy_trader.py"],
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE)
                
                logger.info(f"✅ Python版本启动成功! PID: {process.pid}")
                return True
            else:
                logger.warning("未找到Python启动脚本")
                return False
                
        except Exception as e:
            logger.error(f"❌ Python版本启动失败: {e}")
            return False
    
    def show_manual_instructions(self):
        """显示手动操作指导"""
        print("\n💡 手动查找VeighNa Studio指导:")
        print("=" * 60)
        print("1. 按Win键搜索 'VeighNa Studio'")
        print("2. 查看桌面是否有VeighNa Studio图标")
        print("3. 检查以下目录:")
        for path in self.possible_paths:
            print(f"   - {path}")
        
        print("\n📥 如果没有安装VeighNa Studio:")
        print("1. 访问官网: https://www.vnpy.com/")
        print("2. 下载VeighNa Studio社区版")
        print("3. 安装后重新运行此脚本")
        
        print("\n🔄 备用方案:")
        print("1. 使用Python版本: python run_vnpy_trader.py")
        print("2. 使用pip安装: pip install vnpy[full]")
    
    def show_usage_tips(self):
        """显示使用提示"""
        print("\n💡 VeighNa Studio 使用提示:")
        print("=" * 60)
        print("📊 主要功能模块:")
        print("   - CTA策略: 趋势跟踪策略开发")
        print("   - CTA回测: 策略历史回测")
        print("   - 模拟账户: 虚拟资金交易")
        print("   - 数据管理: 历史数据下载")
        print("   - 风险管理: 实时风控监控")
        
        print("\n🎯 ATR RSI策略测试:")
        print("   1. 打开 功能 → CTA回测")
        print("   2. 加载 AtrRsiStrategy 策略")
        print("   3. 配置回测参数")
        print("   4. 运行回测分析")
        
        print("\n📖 相关文档:")
        print("   - ATR_RSI_模拟交易指南.md")
        print("   - VeighNa_操作步骤.md")
        print("   - 找到模拟账户设置.md")
    
    def run(self):
        """运行启动器"""
        self.print_header()
        
        # 方法1: 检查文件路径
        if self.check_file_paths():
            if self.launch_executable():
                self.show_usage_tips()
                return True
        
        # 方法2: 检查注册表
        if self.check_registry():
            if self.launch_executable():
                self.show_usage_tips()
                return True
        
        # 方法3: 检查开始菜单
        if self.check_start_menu():
            logger.info("✅ 通过开始菜单启动成功")
            self.show_usage_tips()
            return True
        
        # 方法4: 启动Python版本
        logger.info("🔄 尝试备用方案...")
        if self.launch_python_version():
            self.show_usage_tips()
            return True
        
        # 所有方法都失败
        logger.error("❌ 所有启动方法都失败")
        self.show_manual_instructions()
        return False

def check_environment():
    """检查运行环境"""
    print("🔧 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f"⚠️ Python版本过低: {sys.version}")
        print("建议使用Python 3.8或更高版本")
    else:
        print(f"✅ Python版本: {sys.version}")
    
    # 检查vnpy是否安装
    try:
        import vnpy
        print(f"✅ VeighNa已安装: {vnpy.__version__}")
    except ImportError:
        print("⚠️ VeighNa Python包未安装")
        print("可以运行: pip install vnpy")
    
    # 检查相关文件
    files_to_check = [
        "atr_rsi_strategy.py",
        "run_vnpy_trader.py", 
        "ATR_RSI_模拟交易指南.md"
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            print(f"✅ 找到文件: {file}")
        else:
            print(f"⚪ 文件不存在: {file}")

def main():
    """主函数"""
    try:
        # 检查环境
        check_environment()
        print()
        
        # 创建启动器
        launcher = VeighNaStudioLauncher()
        
        # 运行启动器
        success = launcher.run()
        
        if success:
            print("\n🎉 VeighNa Studio 启动完成!")
            print("现在可以开始使用ATR RSI策略进行模拟交易了!")
        else:
            print("\n❌ VeighNa Studio 启动失败")
            print("请参考上述指导手动启动")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断启动")
    except Exception as e:
        print(f"\n❌ 启动器异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
