// SCSS变量定义文件

// 颜色变量
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 文本颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #c0c4cc;

// 边框颜色
$border-color-base: #dcdfe6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$border-color-extra-light: #f2f6fc;

// 背景颜色
$bg-color-base: #f5f7fa;
$bg-color-light: #fafafa;
$bg-color-lighter: #ffffff;

// 盈亏颜色
$profit-color: #00c851;
$loss-color: #ff4444;
$neutral-color: #6c757d;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 圆角
$border-radius-xs: 2px;
$border-radius-sm: 4px;
$border-radius-base: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// 阴影
$box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);

// 过渡动画
$transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-md-fade: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 布局变量
$header-height: 60px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;
$footer-height: 60px;

// 表格变量
$table-header-bg: #fafafa;
$table-row-hover-bg: #f5f7fa;
$table-border-color: #ebeef5;

// 卡片变量
$card-border-radius: 8px;
$card-padding: 20px;
$card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 按钮变量
$button-border-radius: 6px;
$button-padding-vertical: 8px;
$button-padding-horizontal: 16px;

// 输入框变量
$input-border-radius: 6px;
$input-padding-vertical: 8px;
$input-padding-horizontal: 12px;
$input-height: 40px;

// 菜单变量
$menu-item-height: 48px;
$menu-item-padding: 16px;
$menu-active-bg: rgba(64, 158, 255, 0.1);
$menu-hover-bg: rgba(64, 158, 255, 0.05);

// 图表颜色
$chart-colors: (
  #409eff,
  #67c23a,
  #e6a23c,
  #f56c6c,
  #909399,
  #c45656,
  #73767a,
  #3ba272,
  #fc8452,
  #9a60b4,
  #ea7ccc
);

// 暗色主题变量
$dark-bg-color: #1a1a1a;
$dark-bg-color-light: #2d2d2d;
$dark-text-color: #e5eaf3;
$dark-text-color-light: #a3a6ad;
$dark-border-color: #4c4d4f;

// 响应式混合器变量
$mobile-first: true; // 是否采用移动优先策略
