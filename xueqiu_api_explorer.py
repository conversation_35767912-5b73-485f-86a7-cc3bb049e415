#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
雪球API探索器
用于查找和测试雪球组合相关的API接口
"""

import requests
import json
import re
import time
from urllib.parse import urlencode

class XueqiuAPIExplorer:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://xueqiu.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        self.cookies = {}
        self.load_cookies()
    
    def load_cookies(self):
        """从配置文件加载cookies"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.cookies[key] = value
            
            print(f"✅ 已加载 {len(self.cookies)} 个cookies")
            
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
    
    def test_api_endpoint(self, url, params=None, method='GET'):
        """测试API端点"""
        try:
            print(f"\n🔍 测试API: {url}")
            if params:
                print(f"📋 参数: {params}")
            
            if method.upper() == 'GET':
                response = self.session.get(
                    url, 
                    headers=self.headers, 
                    cookies=self.cookies,
                    params=params,
                    timeout=10
                )
            else:
                response = self.session.post(
                    url, 
                    headers=self.headers, 
                    cookies=self.cookies,
                    data=params,
                    timeout=10
                )
            
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON响应成功")
                    return data
                except:
                    print(f"⚠️ 非JSON响应，内容长度: {len(response.text)}")
                    return response.text
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                return None
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def explore_portfolio_apis(self):
        """探索组合相关的API"""
        print("🔍 探索雪球组合API")
        print("=" * 50)
        
        # 常见的雪球API端点
        api_endpoints = [
            # 用户相关
            {
                'name': '用户信息',
                'url': f'{self.base_url}/v4/statuses/user_timeline.json',
                'params': {'user_id': self.cookies.get('u', '')}
            },
            {
                'name': '用户组合列表',
                'url': f'{self.base_url}/cubes/list.json',
                'params': {'user_id': self.cookies.get('u', '')}
            },
            {
                'name': '我的组合',
                'url': f'{self.base_url}/cubes/nav_daily/all.json',
                'params': {}
            },
            
            # 组合相关
            {
                'name': '组合详情',
                'url': f'{self.base_url}/cubes/nav_daily/all.json',
                'params': {'cube_symbol': 'ZH4380321271'}
            },
            {
                'name': '组合持仓',
                'url': f'{self.base_url}/cubes/rebalancing/history.json',
                'params': {'cube_symbol': 'ZH4380321271'}
            },
            {
                'name': '组合净值',
                'url': f'{self.base_url}/cubes/nav_daily/all.json',
                'params': {'symbol': 'ZH4380321271'}
            },
            
            # 投资组合API
            {
                'name': '投资组合信息',
                'url': f'{self.base_url}/v4/stock/portfolio.json',
                'params': {}
            },
            {
                'name': '投资组合股票',
                'url': f'{self.base_url}/v4/stock/portfolio/stocks.json',
                'params': {}
            },
            
            # 搜索相关
            {
                'name': '搜索组合',
                'url': f'{self.base_url}/cubes/discover/rank.json',
                'params': {'market': 'cn', 'cube_category': '', 'sort': 'annualized_gain_rate'}
            },
            
            # 新的API尝试
            {
                'name': '用户组合V2',
                'url': f'{self.base_url}/v2/cubes/nav_daily/all.json',
                'params': {}
            },
            {
                'name': '组合管理',
                'url': f'{self.base_url}/cubes/manage.json',
                'params': {}
            }
        ]
        
        results = {}
        
        for api in api_endpoints:
            print(f"\n{'='*20} {api['name']} {'='*20}")
            result = self.test_api_endpoint(api['url'], api['params'])
            results[api['name']] = result
            
            if result and isinstance(result, dict):
                # 分析响应结构
                self.analyze_response(api['name'], result)
            
            time.sleep(1)  # 避免请求过快
        
        return results
    
    def analyze_response(self, api_name, data):
        """分析API响应结构"""
        print(f"📊 {api_name} 响应分析:")
        
        if isinstance(data, dict):
            # 查找可能的组合信息
            if 'list' in data:
                print(f"   - 找到列表数据，长度: {len(data['list'])}")
                if data['list']:
                    print(f"   - 列表项示例: {list(data['list'][0].keys())}")
            
            if 'cubes' in data:
                print(f"   - 找到组合数据，数量: {len(data['cubes'])}")
                if data['cubes']:
                    print(f"   - 组合字段: {list(data['cubes'][0].keys())}")
            
            # 查找组合代码
            cube_symbols = self.extract_cube_symbols(str(data))
            if cube_symbols:
                print(f"   - 发现组合代码: {cube_symbols[:5]}...")  # 显示前5个
            
            # 显示主要字段
            main_keys = [k for k in data.keys() if not k.startswith('_')][:10]
            print(f"   - 主要字段: {main_keys}")
    
    def extract_cube_symbols(self, text):
        """从文本中提取组合代码"""
        pattern = r'ZH\d+'
        return list(set(re.findall(pattern, text)))
    
    def search_user_cubes(self):
        """搜索用户的组合"""
        print("\n🔍 搜索用户组合")
        print("=" * 30)
        
        user_id = self.cookies.get('u', '')
        if not user_id:
            print("❌ 无法获取用户ID")
            return None
        
        print(f"👤 用户ID: {user_id}")
        
        # 尝试多种方式获取用户组合
        search_urls = [
            f'{self.base_url}/u/{user_id}/cubes',
            f'{self.base_url}/u/{user_id}',
            f'{self.base_url}/cubes/list.json?user_id={user_id}',
            f'{self.base_url}/v4/statuses/user_timeline.json?user_id={user_id}',
        ]
        
        for url in search_urls:
            print(f"\n🌐 访问: {url}")
            result = self.test_api_endpoint(url)
            
            if result:
                if isinstance(result, str):
                    # 从HTML中提取组合代码
                    cube_symbols = self.extract_cube_symbols(result)
                    if cube_symbols:
                        print(f"✅ 从HTML中找到组合: {cube_symbols}")
                        return cube_symbols
                elif isinstance(result, dict):
                    # 从JSON中查找组合信息
                    cube_symbols = self.extract_cube_symbols(str(result))
                    if cube_symbols:
                        print(f"✅ 从JSON中找到组合: {cube_symbols}")
                        return cube_symbols
        
        return None
    
    def test_cube_exists(self, cube_symbol):
        """测试组合是否存在"""
        print(f"\n🧪 测试组合是否存在: {cube_symbol}")
        
        test_urls = [
            f'{self.base_url}/P/{cube_symbol}',
            f'{self.base_url}/cubes/nav_daily/all.json?cube_symbol={cube_symbol}',
            f'{self.base_url}/v4/stock/portfolio.json?cube_symbol={cube_symbol}',
        ]
        
        for url in test_urls:
            result = self.test_api_endpoint(url)
            if result:
                if isinstance(result, str) and 'error' not in result.lower():
                    print(f"✅ 组合存在 (通过 {url})")
                    return True
                elif isinstance(result, dict) and 'error_code' not in result:
                    print(f"✅ 组合存在 (通过 {url})")
                    return True
        
        print(f"❌ 组合不存在或无法访问")
        return False
    
    def generate_report(self, results):
        """生成API探索报告"""
        print("\n" + "="*60)
        print("📋 雪球API探索报告")
        print("="*60)
        
        print("\n🔍 API测试结果:")
        for api_name, result in results.items():
            status = "✅ 成功" if result else "❌ 失败"
            print(f"   {api_name}: {status}")
        
        print("\n💡 建议:")
        print("1. 如果找到了有效的组合代码，请更新配置文件")
        print("2. 如果没有找到组合，建议创建新的模拟组合")
        print("3. 检查cookies是否过期，必要时重新获取")
        print("4. 当前系统即使没有组合信息也可以正常使用")

def main():
    """主函数"""
    explorer = XueqiuAPIExplorer()
    
    print("🚀 雪球API探索器")
    print("帮助查找和测试雪球组合相关的API")
    
    # 探索API
    results = explorer.explore_portfolio_apis()
    
    # 搜索用户组合
    user_cubes = explorer.search_user_cubes()
    
    # 测试当前组合代码
    current_cube = 'ZH4380321271'
    explorer.test_cube_exists(current_cube)
    
    # 生成报告
    explorer.generate_report(results)
    
    # 如果找到了组合代码，询问是否更新配置
    if user_cubes:
        print(f"\n🎯 发现组合代码: {user_cubes}")
        choice = input("是否要更新配置文件？(y/n): ").strip().lower()
        if choice == 'y' and user_cubes:
            update_config_with_cube(user_cubes[0])

def update_config_with_cube(cube_symbol):
    """更新配置文件中的组合代码"""
    try:
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config['portfolio_code'] = cube_symbol
        
        with open('xq.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置已更新为: {cube_symbol}")
        
    except Exception as e:
        print(f"❌ 更新配置失败: {e}")

if __name__ == '__main__':
    main()
