#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
真实雪球交易界面
基于easytrader突破API限制，获取真实持仓数据
支持真实的余额、持仓、盈亏计算和交易功能
"""

from flask import Flask, request, jsonify
import json
import logging
import easytrader
import pysnowball as ball
from datetime import datetime

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

class RealXueqiuTrader:
    """真实雪球交易器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.valid_portfolios = [
            'ZH001001', 'ZH001002', 'ZH001003',
            'ZH100001', 'ZH100002', 'ZH100003'
        ]
        self.current_portfolio = 'ZH001002'  # 默认使用有阿里巴巴等的组合
        self.trader = None
        self.load_config()
        self.init_trader()
    
    def load_config(self):
        """加载配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.cookies = config.get('cookies', '')
            
            # 设置pysnowball token
            cookies_str = config.get('cookies', '')
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logging.info(f"✅ Token设置成功: u={u}")
            
        except Exception as e:
            logging.error(f"❌ 加载配置失败: {e}")
    
    def init_trader(self):
        """初始化交易器"""
        try:
            self.trader = easytrader.use('xq')
            
            login_info = {
                'cookies': self.cookies,
                'portfolio_code': self.current_portfolio,
                'portfolio_market': 'cn'
            }
            
            self.trader.prepare(**login_info)
            logging.info(f"✅ 交易器初始化成功: {self.current_portfolio}")
            
        except Exception as e:
            logging.error(f"❌ 交易器初始化失败: {e}")
    
    def get_all_portfolios_data(self):
        """获取所有有效组合的数据"""
        all_data = []
        
        for portfolio_code in self.valid_portfolios:
            try:
                # 为每个组合创建独立的交易器
                trader = easytrader.use('xq')
                login_info = {
                    'cookies': self.cookies,
                    'portfolio_code': portfolio_code,
                    'portfolio_market': 'cn'
                }
                trader.prepare(**login_info)
                
                # 获取余额和持仓
                balance = trader.balance
                position = trader.position
                
                # 计算组合统计
                total_asset = balance[0]['asset_balance'] if balance else 0
                current_balance = balance[0]['current_balance'] if balance else 0
                market_value = balance[0]['market_value'] if balance else 0
                
                # 计算持仓盈亏
                total_cost = 0
                total_current_value = 0
                
                for pos in position:
                    cost_price = pos.get('cost_price', 0)
                    last_price = pos.get('last_price', 0)
                    amount = pos.get('current_amount', 0)
                    
                    total_cost += cost_price * amount
                    total_current_value += last_price * amount
                
                unrealized_pnl = total_current_value - total_cost
                unrealized_pnl_percent = (unrealized_pnl / total_cost * 100) if total_cost > 0 else 0
                
                portfolio_data = {
                    'portfolio_code': portfolio_code,
                    'balance': balance,
                    'position': position,
                    'statistics': {
                        'total_asset': total_asset,
                        'current_balance': current_balance,
                        'market_value': market_value,
                        'total_cost': total_cost,
                        'total_current_value': total_current_value,
                        'unrealized_pnl': unrealized_pnl,
                        'unrealized_pnl_percent': unrealized_pnl_percent,
                        'position_count': len(position)
                    }
                }
                
                all_data.append(portfolio_data)
                
            except Exception as e:
                logging.error(f"❌ 获取组合 {portfolio_code} 数据失败: {e}")
        
        return all_data
    
    def get_current_portfolio_data(self):
        """获取当前组合数据"""
        try:
            if not self.trader:
                self.init_trader()
            
            balance = self.trader.balance
            position = self.trader.position
            
            # 为每个持仓添加实时行情
            enhanced_position = []
            for pos in position:
                stock_code = pos.get('stock_code', '')
                
                # 获取实时行情
                try:
                    quote = ball.quotec(stock_code)
                    if quote and 'data' in quote and quote['data']:
                        quote_data = quote['data'][0]
                        pos['real_time_price'] = quote_data.get('current', pos.get('last_price', 0))
                        pos['chg'] = quote_data.get('chg', 0)
                        pos['percent'] = quote_data.get('percent', 0)
                    else:
                        pos['real_time_price'] = pos.get('last_price', 0)
                        pos['chg'] = 0
                        pos['percent'] = 0
                except:
                    pos['real_time_price'] = pos.get('last_price', 0)
                    pos['chg'] = 0
                    pos['percent'] = 0
                
                # 计算盈亏
                cost_price = pos.get('cost_price', 0)
                current_price = pos['real_time_price']
                amount = pos.get('current_amount', 0)
                
                if cost_price > 0 and amount > 0:
                    cost_value = cost_price * amount
                    current_value = current_price * amount
                    pnl = current_value - cost_value
                    pnl_percent = (pnl / cost_value * 100) if cost_value > 0 else 0
                    
                    pos['cost_value'] = cost_value
                    pos['current_value'] = current_value
                    pos['pnl'] = pnl
                    pos['pnl_percent'] = pnl_percent
                else:
                    pos['cost_value'] = 0
                    pos['current_value'] = 0
                    pos['pnl'] = 0
                    pos['pnl_percent'] = 0
                
                enhanced_position.append(pos)
            
            return {
                'portfolio_code': self.current_portfolio,
                'balance': balance,
                'position': enhanced_position
            }
            
        except Exception as e:
            logging.error(f"❌ 获取当前组合数据失败: {e}")
            return None
    
    def switch_portfolio(self, portfolio_code):
        """切换组合"""
        try:
            if portfolio_code in self.valid_portfolios:
                self.current_portfolio = portfolio_code
                self.init_trader()
                return True
            return False
        except Exception as e:
            logging.error(f"❌ 切换组合失败: {e}")
            return False
    
    def execute_trade(self, stock_code, action, amount, price=None):
        """执行真实交易"""
        try:
            if not self.trader:
                self.init_trader()
            
            if action == 'buy':
                result = self.trader.buy(stock_code, price, amount)
            elif action == 'sell':
                result = self.trader.sell(stock_code, price, amount)
            else:
                return {'success': False, 'error': '无效的交易类型'}
            
            return {'success': True, 'result': result}
            
        except Exception as e:
            logging.error(f"❌ 执行交易失败: {e}")
            return {'success': False, 'error': str(e)}

# 全局交易器实例
real_trader = RealXueqiuTrader()

@app.route('/')
def index():
    """主页"""
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实雪球交易系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            color: #333;
        }
        .container { 
            max-width: 1800px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header { 
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4); 
            color: white; 
            padding: 20px; 
            border-radius: 15px; 
            text-align: center; 
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .portfolio-selector {
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .portfolio-selector select {
            padding: 10px 20px;
            border-radius: 5px;
            border: 2px solid #ddd;
            font-size: 16px;
            margin: 0 10px;
        }
        .trading-grid { 
            display: grid; 
            grid-template-columns: 2fr 1fr 1fr; 
            gap: 20px; 
            margin-bottom: 20px;
        }
        .panel { 
            background: rgba(255,255,255,0.95); 
            border-radius: 15px; 
            padding: 20px; 
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .panel h3 { 
            color: #2c3e50; 
            margin-bottom: 15px; 
            padding-bottom: 10px; 
            border-bottom: 3px solid #3498db;
            font-size: 18px;
        }
        .btn { 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 14px; 
            font-weight: bold;
            transition: all 0.3s;
            margin: 5px;
        }
        .btn-primary { background: linear-gradient(45deg, #3498db, #2980b9); color: white; }
        .btn-success { background: linear-gradient(45deg, #2ecc71, #27ae60); color: white; }
        .btn-danger { background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0,0,0,0.2); }
        
        .holdings-table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 15px; 
            font-size: 12px;
        }
        .holdings-table th, .holdings-table td { 
            padding: 8px 4px; 
            text-align: center; 
            border-bottom: 1px solid #eee; 
        }
        .holdings-table th { 
            background: linear-gradient(45deg, #34495e, #2c3e50); 
            color: white; 
            font-weight: bold;
            font-size: 11px;
        }
        .holdings-table tr:hover { background: #f8f9fa; }
        
        .price-up { color: #e74c3c; font-weight: bold; }
        .price-down { color: #27ae60; font-weight: bold; }
        .price-flat { color: #7f8c8d; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            margin-top: 5px;
        }
        
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; }
        .form-group input, .form-group select { 
            width: 100%; 
            padding: 12px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            font-size: 14px;
        }
        
        .alert { 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 8px; 
            display: none;
        }
        .alert.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 真实雪球交易系统</h1>
            <p>基于easytrader突破API限制，获取真实持仓数据</p>
            <div>
                <span style="color: #2ecc71;">●</span>
                <span>已连接到雪球 | 真实数据 | 支持真实交易</span>
            </div>
        </div>
        
        <div class="portfolio-selector">
            <label for="portfolioSelect"><strong>选择组合:</strong></label>
            <select id="portfolioSelect" onchange="switchPortfolio()">
                <option value="ZH001002">ZH001002 - 阿里巴巴组合</option>
                <option value="ZH001001">ZH001001 - 英威腾组合</option>
                <option value="ZH001003">ZH001003 - 分级基金组合</option>
                <option value="ZH100001">ZH100001 - 港股组合</option>
                <option value="ZH100002">ZH100002 - A股组合</option>
                <option value="ZH100003">ZH100003 - 农业组合</option>
            </select>
            <button class="btn btn-primary" onclick="refreshData()">刷新数据</button>
            <button class="btn btn-success" onclick="showAllPortfolios()">查看所有组合</button>
        </div>
        
        <div class="trading-grid">
            <!-- 左侧：真实持仓 -->
            <div class="panel">
                <h3>💼 真实持仓数据</h3>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div>总资产</div>
                        <div class="stat-value" id="totalAsset">--</div>
                    </div>
                    <div class="stat-item">
                        <div>可用资金</div>
                        <div class="stat-value" id="availableCash">--</div>
                    </div>
                    <div class="stat-item">
                        <div>持仓市值</div>
                        <div class="stat-value" id="marketValue">--</div>
                    </div>
                    <div class="stat-item">
                        <div>浮动盈亏</div>
                        <div class="stat-value" id="unrealizedPnL">--</div>
                    </div>
                </div>
                
                <div id="holdingsContainer">
                    <div style="text-align: center; padding: 20px; color: #7f8c8d;">加载中...</div>
                </div>
            </div>
            
            <!-- 中间：行情查看 -->
            <div class="panel">
                <h3>📊 实时行情</h3>
                
                <div class="form-group">
                    <label>股票代码:</label>
                    <input type="text" id="stockCode" placeholder="如: SH600519">
                    <button class="btn btn-primary" onclick="loadQuote()">查看行情</button>
                </div>
                
                <div id="quoteDisplay">
                    <div style="text-align: center; padding: 20px; color: #7f8c8d;">请输入股票代码</div>
                </div>
            </div>
            
            <!-- 右侧：真实交易 -->
            <div class="panel">
                <h3>💰 真实交易</h3>
                
                <div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin-bottom: 15px; font-size: 12px;">
                    <strong>⚠️ 注意:</strong> 这是真实的雪球组合交易，会影响实际持仓！
                </div>
                
                <div class="form-group">
                    <label>股票代码:</label>
                    <input type="text" id="tradeStock" placeholder="股票代码">
                </div>
                
                <div class="form-group">
                    <label>交易类型:</label>
                    <select id="tradeAction">
                        <option value="buy">买入</option>
                        <option value="sell">卖出</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>数量:</label>
                    <input type="number" id="tradeAmount" placeholder="股数" min="100" step="100">
                </div>
                
                <div class="form-group">
                    <label>价格:</label>
                    <input type="number" id="tradePrice" placeholder="价格" step="0.01">
                </div>
                
                <div class="form-group">
                    <button class="btn btn-success" onclick="submitRealTrade()">提交真实交易</button>
                    <button class="btn btn-danger" onclick="clearTradeForm()">清空</button>
                </div>
            </div>
        </div>
        
        <div id="alertBox" class="alert"></div>
    </div>

    <script>
        let currentPortfolio = 'ZH001002';
        
        function showAlert(message, type = 'info') {
            const alertBox = document.getElementById('alertBox');
            alertBox.className = `alert ${type}`;
            alertBox.textContent = message;
            alertBox.style.display = 'block';
            
            setTimeout(() => {
                alertBox.style.display = 'none';
            }, 5000);
        }
        
        function switchPortfolio() {
            const select = document.getElementById('portfolioSelect');
            const newPortfolio = select.value;
            
            fetch('/api/switch_portfolio', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ portfolio_code: newPortfolio })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentPortfolio = newPortfolio;
                    showAlert(`已切换到组合 ${newPortfolio}`, 'success');
                    refreshData();
                } else {
                    showAlert(`切换失败: ${data.error}`, 'error');
                }
            });
        }
        
        function refreshData() {
            fetch('/api/current_portfolio')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateHoldingsDisplay(data.data);
                        showAlert('数据刷新成功', 'success');
                    } else {
                        showAlert(`刷新失败: ${data.error}`, 'error');
                    }
                });
        }
        
        function updateHoldingsDisplay(portfolioData) {
            const container = document.getElementById('holdingsContainer');
            const balance = portfolioData.balance[0];
            const positions = portfolioData.position;
            
            // 更新统计信息
            document.getElementById('totalAsset').textContent = `¥${balance.asset_balance.toLocaleString()}`;
            document.getElementById('availableCash').textContent = `¥${balance.current_balance.toLocaleString()}`;
            document.getElementById('marketValue').textContent = `¥${balance.market_value.toLocaleString()}`;
            
            // 计算总盈亏
            let totalPnL = 0;
            positions.forEach(pos => {
                totalPnL += pos.pnl || 0;
            });
            
            const pnlClass = totalPnL > 0 ? 'price-up' : totalPnL < 0 ? 'price-down' : 'price-flat';
            document.getElementById('unrealizedPnL').innerHTML = `<span class="${pnlClass}">¥${totalPnL.toLocaleString()}</span>`;
            
            // 更新持仓表格
            let html = '<table class="holdings-table">';
            html += '<tr><th>股票</th><th>代码</th><th>数量</th><th>成本价</th><th>现价</th><th>市值</th><th>盈亏</th><th>涨跌幅</th></tr>';
            
            positions.forEach(pos => {
                const pnlClass = pos.pnl > 0 ? 'price-up' : pos.pnl < 0 ? 'price-down' : 'price-flat';
                const percentClass = pos.percent > 0 ? 'price-up' : pos.percent < 0 ? 'price-down' : 'price-flat';
                
                html += '<tr>';
                html += `<td><strong>${pos.stock_name}</strong></td>`;
                html += `<td>${pos.stock_code}</td>`;
                html += `<td>${pos.current_amount}</td>`;
                html += `<td>¥${pos.cost_price.toFixed(2)}</td>`;
                html += `<td class="${percentClass}">¥${pos.real_time_price.toFixed(2)}</td>`;
                html += `<td>¥${pos.current_value.toLocaleString()}</td>`;
                html += `<td class="${pnlClass}">¥${pos.pnl.toFixed(2)}<br><small>(${pos.pnl_percent.toFixed(2)}%)</small></td>`;
                html += `<td class="${percentClass}">${pos.percent.toFixed(2)}%</td>`;
                html += '</tr>';
            });
            
            html += '</table>';
            container.innerHTML = html;
        }
        
        function loadQuote() {
            const stockCode = document.getElementById('stockCode').value;
            if (!stockCode) {
                showAlert('请输入股票代码', 'error');
                return;
            }
            
            fetch(`/api/quote/${stockCode}`)
                .then(response => response.json())
                .then(data => {
                    const quoteDiv = document.getElementById('quoteDisplay');
                    
                    if (data.success) {
                        const quote = data.quote;
                        const priceClass = quote.percent > 0 ? 'price-up' : 
                                         quote.percent < 0 ? 'price-down' : 'price-flat';
                        
                        quoteDiv.innerHTML = `
                            <h4>${quote.name || stockCode}</h4>
                            <div style="font-size: 24px; margin: 10px 0;">
                                <span class="${priceClass}">¥${quote.current}</span>
                                <span class="${priceClass}" style="font-size: 16px;">
                                    ${quote.chg} (${quote.percent}%)
                                </span>
                            </div>
                            <div style="font-size: 12px;">
                                <div>开盘: ¥${quote.open} | 最高: ¥${quote.high}</div>
                                <div>最低: ¥${quote.low} | 昨收: ¥${quote.last_close}</div>
                                <div>成交量: ${(quote.volume / 10000).toFixed(0)}万</div>
                            </div>
                        `;
                        
                        // 自动填入交易价格
                        document.getElementById('tradeStock').value = stockCode;
                        document.getElementById('tradePrice').value = quote.current;
                        
                    } else {
                        quoteDiv.innerHTML = `<div style="color: red;">${data.error}</div>`;
                    }
                });
        }
        
        function submitRealTrade() {
            const stockCode = document.getElementById('tradeStock').value;
            const action = document.getElementById('tradeAction').value;
            const amount = document.getElementById('tradeAmount').value;
            const price = document.getElementById('tradePrice').value;
            
            if (!stockCode || !amount || !price) {
                showAlert('请填写完整的交易信息', 'error');
                return;
            }
            
            if (!confirm(`确认要${action === 'buy' ? '买入' : '卖出'} ${amount}股 ${stockCode} 吗？这是真实交易！`)) {
                return;
            }
            
            const tradeData = {
                stock_code: stockCode,
                action: action,
                amount: parseInt(amount),
                price: parseFloat(price)
            };
            
            fetch('/api/real_trade', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(tradeData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`${action === 'buy' ? '买入' : '卖出'}成功！`, 'success');
                    clearTradeForm();
                    refreshData();
                } else {
                    showAlert(`交易失败: ${data.error}`, 'error');
                }
            });
        }
        
        function clearTradeForm() {
            document.getElementById('tradeStock').value = '';
            document.getElementById('tradeAmount').value = '';
            document.getElementById('tradePrice').value = '';
        }
        
        function showAllPortfolios() {
            fetch('/api/all_portfolios')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let summary = '所有组合概览:\\n\\n';
                        data.portfolios.forEach(p => {
                            const stats = p.statistics;
                            summary += `${p.portfolio_code}:\\n`;
                            summary += `  总资产: ¥${stats.total_asset.toLocaleString()}\\n`;
                            summary += `  持仓数: ${stats.position_count}只\\n`;
                            summary += `  盈亏: ¥${stats.unrealized_pnl.toFixed(2)} (${stats.unrealized_pnl_percent.toFixed(2)}%)\\n\\n`;
                        });
                        alert(summary);
                    }
                });
        }
        
        // 页面加载时初始化
        window.onload = function() {
            refreshData();
        };
        
        // 定时刷新（每60秒）
        setInterval(refreshData, 60000);
    </script>
</body>
</html>
    '''

# API路由
@app.route('/api/current_portfolio')
def get_current_portfolio():
    """获取当前组合数据"""
    try:
        data = real_trader.get_current_portfolio_data()
        if data:
            return jsonify({'success': True, 'data': data})
        else:
            return jsonify({'success': False, 'error': '无法获取组合数据'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/all_portfolios')
def get_all_portfolios():
    """获取所有组合数据"""
    try:
        data = real_trader.get_all_portfolios_data()
        return jsonify({'success': True, 'portfolios': data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/switch_portfolio', methods=['POST'])
def switch_portfolio():
    """切换组合"""
    try:
        data = request.get_json()
        portfolio_code = data.get('portfolio_code')
        
        if real_trader.switch_portfolio(portfolio_code):
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': '无效的组合代码'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/quote/<symbol>')
def get_quote(symbol):
    """获取股票行情"""
    try:
        quote = ball.quotec(symbol)
        if quote and 'data' in quote and quote['data']:
            return jsonify({'success': True, 'quote': quote['data'][0]})
        else:
            return jsonify({'success': False, 'error': '无法获取行情数据'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/real_trade', methods=['POST'])
def real_trade():
    """执行真实交易"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        action = data.get('action')
        amount = data.get('amount')
        price = data.get('price')
        
        result = real_trader.execute_trade(stock_code, action, amount, price)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def main():
    """主函数"""
    print("🚀 真实雪球交易界面")
    print("基于easytrader突破API限制")
    print("=" * 60)
    
    print("\n🌐 启动Web服务器...")
    print("访问地址: http://localhost:5007")
    print("功能特点:")
    print("  💰 真实余额数据")
    print("  📊 真实持仓信息")
    print("  💹 准确盈亏计算")
    print("  🔄 真实交易功能")
    print("  📈 实时行情数据")
    print("  🔀 多组合切换")
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5007, debug=True)

if __name__ == '__main__':
    main()
