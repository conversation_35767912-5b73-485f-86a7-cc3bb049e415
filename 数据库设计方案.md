# A股量化交易平台 - 数据库设计方案

## 🎯 数据库架构选择

### 推荐方案：SQLite + Redis组合

#### SQLite (主数据库)
- **用途**: 持久化数据存储
- **优势**: 零配置、轻量级、高性能、跨平台
- **数据**: 股票数据、用户数据、策略数据、交易记录

#### Redis (缓存数据库，可选)
- **用途**: 实时数据缓存和会话管理
- **优势**: 高速读写、数据结构丰富
- **数据**: 实时行情、计算缓存、用户会话

## 📋 数据库表结构设计

### 1. 用户管理相关表

#### users (用户表)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    subscription_type VARCHAR(20) DEFAULT 'basic', -- basic/premium
    subscription_start DATE,
    subscription_end DATE,
    license_key VARCHAR(255) UNIQUE,
    device_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### user_settings (用户设置表)
```sql
CREATE TABLE user_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 股票数据相关表

#### stocks (股票基础信息表)
```sql
CREATE TABLE stocks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ts_code VARCHAR(20) UNIQUE NOT NULL, -- TuShare代码
    symbol VARCHAR(10) NOT NULL,         -- 股票代码
    name VARCHAR(50) NOT NULL,           -- 股票名称
    area VARCHAR(20),                    -- 地域
    industry VARCHAR(50),                -- 行业
    market VARCHAR(20),                  -- 市场类型
    list_date DATE,                      -- 上市日期
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### daily_quotes (日线行情表)
```sql
CREATE TABLE daily_quotes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ts_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    open_price DECIMAL(10,3),
    high_price DECIMAL(10,3),
    low_price DECIMAL(10,3),
    close_price DECIMAL(10,3),
    pre_close DECIMAL(10,3),
    change_amount DECIMAL(10,3),
    change_pct DECIMAL(8,4),
    volume BIGINT,                       -- 成交量(手)
    amount DECIMAL(20,2),                -- 成交额(千元)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(ts_code, trade_date)
);
```

#### technical_indicators (技术指标表)
```sql
CREATE TABLE technical_indicators (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ts_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    indicator_name VARCHAR(50) NOT NULL, -- MA5, MA10, RSI, MACD等
    indicator_value DECIMAL(15,6),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(ts_code, trade_date, indicator_name)
);
```

### 3. 策略管理相关表

#### strategies (策略表)
```sql
CREATE TABLE strategies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    strategy_type VARCHAR(50),           -- system/custom
    strategy_code TEXT,                  -- 加密存储的策略代码
    parameters JSON,                     -- 策略参数
    is_active BOOLEAN DEFAULT TRUE,
    is_system BOOLEAN DEFAULT FALSE,     -- 是否为系统策略
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### strategy_templates (策略模板表)
```sql
CREATE TABLE strategy_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),                -- 策略分类
    description TEXT,
    template_code TEXT,                  -- 加密的模板代码
    default_parameters JSON,
    subscription_level VARCHAR(20),      -- basic/premium
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. 回测相关表

#### backtests (回测记录表)
```sql
CREATE TABLE backtests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    strategy_id INTEGER REFERENCES strategies(id),
    name VARCHAR(100),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    initial_capital DECIMAL(15,2),
    final_capital DECIMAL(15,2),
    total_return DECIMAL(8,4),
    annual_return DECIMAL(8,4),
    max_drawdown DECIMAL(8,4),
    sharpe_ratio DECIMAL(8,4),
    win_rate DECIMAL(8,4),
    total_trades INTEGER,
    status VARCHAR(20) DEFAULT 'pending', -- pending/running/completed/failed
    result_data JSON,                     -- 详细回测结果
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);
```

#### backtest_trades (回测交易记录表)
```sql
CREATE TABLE backtest_trades (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    backtest_id INTEGER REFERENCES backtests(id),
    ts_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    action VARCHAR(10) NOT NULL,         -- buy/sell
    quantity INTEGER NOT NULL,
    price DECIMAL(10,3) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    commission DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. 模拟交易相关表

#### virtual_accounts (虚拟账户表)
```sql
CREATE TABLE virtual_accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    account_name VARCHAR(100),
    initial_capital DECIMAL(15,2),
    current_capital DECIMAL(15,2),
    available_cash DECIMAL(15,2),
    market_value DECIMAL(15,2),
    total_asset DECIMAL(15,2),
    total_return DECIMAL(8,4),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### virtual_positions (虚拟持仓表)
```sql
CREATE TABLE virtual_positions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER REFERENCES virtual_accounts(id),
    ts_code VARCHAR(20) NOT NULL,
    quantity INTEGER NOT NULL,
    avg_cost DECIMAL(10,3) NOT NULL,
    current_price DECIMAL(10,3),
    market_value DECIMAL(15,2),
    unrealized_pnl DECIMAL(15,2),
    realized_pnl DECIMAL(15,2),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(account_id, ts_code)
);
```

#### virtual_orders (虚拟订单表)
```sql
CREATE TABLE virtual_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER REFERENCES virtual_accounts(id),
    strategy_id INTEGER REFERENCES strategies(id),
    ts_code VARCHAR(20) NOT NULL,
    order_type VARCHAR(20) NOT NULL,     -- market/limit
    action VARCHAR(10) NOT NULL,         -- buy/sell
    quantity INTEGER NOT NULL,
    price DECIMAL(10,3),
    status VARCHAR(20) DEFAULT 'pending', -- pending/filled/cancelled/rejected
    filled_quantity INTEGER DEFAULT 0,
    filled_price DECIMAL(10,3),
    commission DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    filled_at TIMESTAMP
);
```

### 6. 系统管理相关表

#### system_logs (系统日志表)
```sql
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER REFERENCES users(id),
    log_level VARCHAR(20),               -- DEBUG/INFO/WARNING/ERROR
    module VARCHAR(50),
    message TEXT,
    extra_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### data_updates (数据更新记录表)
```sql
CREATE TABLE data_updates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    data_type VARCHAR(50),               -- daily_quotes/stocks/indicators
    update_date DATE,
    status VARCHAR(20),                  -- success/failed
    records_count INTEGER,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 数据库优化策略

### 索引设计
```sql
-- 股票数据查询优化
CREATE INDEX idx_daily_quotes_ts_code_date ON daily_quotes(ts_code, trade_date);
CREATE INDEX idx_technical_indicators_ts_code_date ON technical_indicators(ts_code, trade_date);

-- 用户数据查询优化
CREATE INDEX idx_strategies_user_id ON strategies(user_id);
CREATE INDEX idx_backtests_user_id ON backtests(user_id);
CREATE INDEX idx_virtual_accounts_user_id ON virtual_accounts(user_id);

-- 交易数据查询优化
CREATE INDEX idx_virtual_orders_account_id ON virtual_orders(account_id);
CREATE INDEX idx_virtual_positions_account_id ON virtual_positions(account_id);
```

### 数据分区策略
```sql
-- 按年份分区存储历史数据（SQLite不直接支持分区，可通过表命名实现）
-- daily_quotes_2023, daily_quotes_2024 等
```

## 🔒 数据安全策略

### 1. 数据加密
- **数据库文件加密**: 使用SQLCipher加密整个数据库
- **敏感字段加密**: 策略代码、用户密码等敏感信息
- **传输加密**: API通信使用HTTPS

### 2. 策略代码保护
```python
# 策略代码加密存储示例
import cryptography.fernet

def encrypt_strategy_code(code: str, key: bytes) -> str:
    """加密策略代码"""
    f = Fernet(key)
    encrypted_code = f.encrypt(code.encode())
    return encrypted_code.decode()

def decrypt_strategy_code(encrypted_code: str, key: bytes) -> str:
    """解密策略代码"""
    f = Fernet(key)
    decrypted_code = f.decrypt(encrypted_code.encode())
    return decrypted_code.decode()
```

### 3. 访问控制
- **用户权限验证**: 基于订阅级别的功能访问控制
- **策略访问控制**: 系统策略只读，用户策略可编辑
- **数据访问日志**: 记录所有数据访问操作

## 📊 数据存储估算

### 存储空间预估
- **股票基础信息**: ~5MB (5000只股票)
- **日线数据**: ~500MB/年 (5000只股票 × 250个交易日)
- **技术指标**: ~200MB/年
- **用户数据**: ~10MB/1000用户
- **总计**: 约1GB/年 (单用户本地部署)

### 性能预估
- **数据查询**: <100ms (单股票年度数据)
- **策略回测**: <30s (单策略年度回测)
- **实时计算**: <1s (技术指标计算)

## 🚀 实施建议

### Phase 1: 基础数据库
1. 搭建SQLite数据库
2. 实现基础表结构
3. 数据导入和更新机制
4. 基础查询API

### Phase 2: 业务功能
1. 用户管理系统
2. 策略管理功能
3. 回测数据存储
4. 模拟交易记录

### Phase 3: 优化和安全
1. 性能优化
2. 数据加密
3. 备份恢复
4. 监控日志

这个数据库设计方案能够满足您的所有需求，既保证了数据安全，又确保了高性能。您觉得这个方案如何？
