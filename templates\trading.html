<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EasyQuant Web交易界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .status-bar {
            background: #f5f5f5;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
        }
        
        .status-dot.running {
            background: #44ff44;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }
        
        .panel {
            background: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e0e0e0;
        }
        
        .panel h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 5px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            margin: 5px;
        }
        
        .btn-primary {
            background: #2196F3;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1976D2;
        }
        
        .btn-success {
            background: #4CAF50;
            color: white;
        }
        
        .btn-success:hover {
            background: #45a049;
        }
        
        .btn-danger {
            background: #f44336;
            color: white;
        }
        
        .btn-danger:hover {
            background: #da190b;
        }
        
        .btn-warning {
            background: #ff9800;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e68900;
        }
        
        .account-info {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .account-info h4 {
            margin-bottom: 10px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .info-item {
            background: rgba(255,255,255,0.2);
            padding: 8px;
            border-radius: 5px;
        }
        
        .log-panel {
            grid-column: 1 / -1;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-content {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 250px;
            overflow-y: auto;
        }
        
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            display: none;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .positions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .positions-table th,
        .positions-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .positions-table th {
            background: #f2f2f2;
            font-weight: bold;
        }
        
        .quote-display {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .quote-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        .quote-price {
            font-size: 1.5em;
            font-weight: bold;
        }

        .quote-price.up {
            color: #f44336;
        }

        .quote-price.down {
            color: #4CAF50;
        }

        .quote-change {
            font-size: 0.9em;
        }

        .five-levels {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .level-column {
            background: #f9f9f9;
            border-radius: 5px;
            padding: 10px;
        }

        .level-column h5 {
            text-align: center;
            margin-bottom: 8px;
            color: #666;
        }

        .level-item {
            display: flex;
            justify-content: space-between;
            padding: 3px 5px;
            margin: 2px 0;
            border-radius: 3px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .level-item:hover {
            background: #e0e0e0;
        }

        .level-item.buy {
            background: #ffebee;
        }

        .level-item.buy:hover {
            background: #ffcdd2;
        }

        .level-item.sell {
            background: #e8f5e8;
        }

        .level-item.sell:hover {
            background: #c8e6c9;
        }

        .level-price {
            font-weight: bold;
        }

        .level-volume {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 EasyQuant Web交易界面</h1>
            <p>基于Python的量化交易平台</p>
        </div>
        
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">引擎未启动</span>
            </div>
            <div>
                <button class="btn btn-success" onclick="startEngine()">启动引擎</button>
                <button class="btn btn-danger" onclick="stopEngine()">停止引擎</button>
            </div>
        </div>
        
        <div id="messageArea"></div>
        
        <div class="main-content">
            <!-- 账户信息面板 -->
            <div class="panel">
                <h3>📊 账户信息</h3>
                <div class="account-info" id="accountInfo">
                    <h4>账户状态</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>总资产:</strong> <span id="totalAsset">--</span>
                        </div>
                        <div class="info-item">
                            <strong>可用余额:</strong> <span id="availableBalance">--</span>
                        </div>
                        <div class="info-item">
                            <strong>市值:</strong> <span id="marketValue">--</span>
                        </div>
                        <div class="info-item">
                            <strong>币种:</strong> <span id="currency">--</span>
                        </div>
                    </div>
                </div>
                
                <h4>持仓信息</h4>
                <table class="positions-table">
                    <thead>
                        <tr>
                            <th>股票代码</th>
                            <th>数量</th>
                            <th>成本价</th>
                            <th>现价</th>
                        </tr>
                    </thead>
                    <tbody id="positionsTable">
                        <tr>
                            <td colspan="4" style="text-align: center; color: #999;">暂无持仓</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 交易面板 -->
            <div class="panel">
                <h3>💰 股票交易</h3>
                
                <div class="form-group">
                    <label for="stockCode">股票代码:</label>
                    <input type="text" id="stockCode" placeholder="例如: 000001" onblur="getQuote()">
                </div>

                <div class="quote-display" id="quoteDisplay" style="display: none;">
                    <div class="quote-header">
                        <div>
                            <strong id="stockName">--</strong> (<span id="stockCodeDisplay">--</span>)
                        </div>
                        <div class="quote-price" id="currentPrice">--</div>
                    </div>

                    <div class="quote-change">
                        涨跌: <span id="priceChange">--</span>
                        涨跌幅: <span id="changePercent">--</span>
                        成交量: <span id="volume">--</span>
                    </div>

                    <div class="five-levels">
                        <div class="level-column">
                            <h5>卖盘 (Ask)</h5>
                            <div id="sellLevels"></div>
                        </div>
                        <div class="level-column">
                            <h5>买盘 (Bid)</h5>
                            <div id="buyLevels"></div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="price">价格:</label>
                    <input type="number" id="price" step="0.01" placeholder="0.00">
                </div>
                
                <div class="form-group">
                    <label for="amount">数量:</label>
                    <input type="number" id="amount" placeholder="100">
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="buyStock()">🔥 买入</button>
                    <button class="btn btn-warning" onclick="sellStock()">💸 卖出</button>
                </div>
            </div>
            
            <!-- 日志面板 -->
            <div class="panel log-panel">
                <h3>📝 系统日志</h3>
                <div class="log-content" id="logContent">
                    等待系统启动...
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let updateInterval;
        
        // 页面加载完成后开始更新状态
        window.onload = function() {
            updateStatus();
            updateInterval = setInterval(updateStatus, 2000); // 每2秒更新一次
        };
        
        // 更新系统状态
        function updateStatus() {
            fetch('/get_status')
                .then(response => response.json())
                .then(data => {
                    // 更新状态指示器
                    const statusDot = document.getElementById('statusDot');
                    const statusText = document.getElementById('statusText');
                    
                    if (data.is_running) {
                        statusDot.classList.add('running');
                        statusText.textContent = '引擎运行中';
                    } else {
                        statusDot.classList.remove('running');
                        statusText.textContent = '引擎未启动';
                    }
                    
                    // 更新账户信息
                    if (data.account_info) {
                        document.getElementById('totalAsset').textContent = 
                            (data.account_info.asset_balance || 0).toLocaleString() + ' 元';
                        document.getElementById('availableBalance').textContent = 
                            (data.account_info.enable_balance || 0).toLocaleString() + ' 元';
                        document.getElementById('marketValue').textContent = 
                            (data.account_info.market_value || 0).toLocaleString() + ' 元';
                        document.getElementById('currency').textContent = 
                            data.account_info.money_type || '--';
                    }
                    
                    // 更新持仓信息
                    updatePositions(data.positions);
                    
                    // 更新日志
                    if (data.logs && data.logs.length > 0) {
                        const logContent = document.getElementById('logContent');
                        logContent.innerHTML = data.logs.join('<br>');
                        logContent.scrollTop = logContent.scrollHeight;
                    }
                })
                .catch(error => {
                    console.error('更新状态失败:', error);
                });
        }
        
        // 更新持仓表格
        function updatePositions(positions) {
            const tbody = document.getElementById('positionsTable');
            if (!positions || positions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #999;">暂无持仓</td></tr>';
                return;
            }
            
            let html = '';
            positions.forEach(pos => {
                html += `
                    <tr>
                        <td>${pos.stock_code || '--'}</td>
                        <td>${pos.current_amount || 0}</td>
                        <td>${pos.cost_price || '--'}</td>
                        <td>${pos.current_price || '--'}</td>
                    </tr>
                `;
            });
            tbody.innerHTML = html;
        }
        
        // 显示消息
        function showMessage(message, type = 'success') {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            messageDiv.style.display = 'block';
            
            messageArea.appendChild(messageDiv);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }
        
        // 启动引擎
        function startEngine() {
            fetch('/start_engine', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('启动引擎失败: ' + error, 'error');
            });
        }
        
        // 停止引擎
        function stopEngine() {
            fetch('/stop_engine', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('停止引擎失败: ' + error, 'error');
            });
        }
        
        // 获取股票行情
        function getQuote() {
            const stockCode = document.getElementById('stockCode').value.trim();
            if (!stockCode) return;

            fetch(`/get_quote/${stockCode}`)
                .then(response => response.json())
                .then(data => {
                    const quoteDisplay = document.getElementById('quoteDisplay');

                    if (data.success && data.quote) {
                        const quote = data.quote;

                        // 更新基本信息
                        document.getElementById('stockName').textContent = quote.name;
                        document.getElementById('stockCodeDisplay').textContent = quote.code;
                        document.getElementById('currentPrice').textContent = quote.price.toFixed(2);

                        // 设置价格颜色
                        const priceElement = document.getElementById('currentPrice');
                        if (quote.change > 0) {
                            priceElement.className = 'quote-price up';
                        } else if (quote.change < 0) {
                            priceElement.className = 'quote-price down';
                        } else {
                            priceElement.className = 'quote-price';
                        }

                        // 更新涨跌信息
                        document.getElementById('priceChange').textContent =
                            (quote.change > 0 ? '+' : '') + quote.change.toFixed(2);
                        document.getElementById('changePercent').textContent =
                            (quote.change_percent > 0 ? '+' : '') + quote.change_percent.toFixed(2) + '%';
                        document.getElementById('volume').textContent = quote.volume.toLocaleString();

                        // 更新五档行情
                        updateFiveLevels(quote.five_levels);

                        quoteDisplay.style.display = 'block';

                        // 自动填入当前价格
                        document.getElementById('price').value = quote.price.toFixed(2);
                    } else {
                        quoteDisplay.style.display = 'none';
                        showMessage(data.message || '获取行情失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('获取行情失败:', error);
                    showMessage('获取行情失败: ' + error, 'error');
                });
        }

        // 更新五档行情
        function updateFiveLevels(levels) {
            const sellLevels = document.getElementById('sellLevels');
            const buyLevels = document.getElementById('buyLevels');

            let sellHtml = '';
            let buyHtml = '';

            // 卖盘（从卖5到卖1，价格从高到低）
            for (let i = levels.length - 1; i >= 0; i--) {
                const level = levels[i];
                sellHtml += `
                    <div class="level-item sell" onclick="selectPrice(${level.sell_price})">
                        <span class="level-price">${level.sell_price.toFixed(2)}</span>
                        <span class="level-volume">${level.sell_volume}</span>
                    </div>
                `;
            }

            // 买盘（从买1到买5，价格从高到低）
            for (let i = 0; i < levels.length; i++) {
                const level = levels[i];
                buyHtml += `
                    <div class="level-item buy" onclick="selectPrice(${level.buy_price})">
                        <span class="level-price">${level.buy_price.toFixed(2)}</span>
                        <span class="level-volume">${level.buy_volume}</span>
                    </div>
                `;
            }

            sellLevels.innerHTML = sellHtml;
            buyLevels.innerHTML = buyHtml;
        }

        // 选择价格
        function selectPrice(price) {
            document.getElementById('price').value = price.toFixed(2);
            showMessage(`已选择价格: ${price.toFixed(2)}`, 'success');
        }
        
        // 买入股票
        function buyStock() {
            const stockCode = document.getElementById('stockCode').value.trim();
            const price = parseFloat(document.getElementById('price').value);
            const amount = parseInt(document.getElementById('amount').value);
            
            if (!stockCode || !price || !amount) {
                showMessage('请填写完整的交易信息', 'error');
                return;
            }
            
            fetch('/buy_stock', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    stock_code: stockCode,
                    price: price,
                    amount: amount
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('买入操作失败: ' + error, 'error');
            });
        }
        
        // 卖出股票
        function sellStock() {
            const stockCode = document.getElementById('stockCode').value.trim();
            const price = parseFloat(document.getElementById('price').value);
            const amount = parseInt(document.getElementById('amount').value);
            
            if (!stockCode || !price || !amount) {
                showMessage('请填写完整的交易信息', 'error');
                return;
            }
            
            fetch('/sell_stock', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    stock_code: stockCode,
                    price: price,
                    amount: amount
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('卖出操作失败: ' + error, 'error');
            });
        }
    </script>
</body>
</html>
