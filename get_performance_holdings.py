#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
从雪球performance页面获取真实持仓数据
https://xueqiu.com/performance 包含用户的真实持仓信息
"""

import json
import logging
import requests
import re
from datetime import datetime
import pysnowball as ball

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceDataGetter:
    """雪球performance数据获取器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ Token设置成功: u={u}")
            
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
    
    def get_performance_page(self):
        """获取performance页面"""
        logger.info("🌐 访问雪球performance页面")
        logger.info("=" * 60)
        
        try:
            url = "https://xueqiu.com/performance"
            logger.info(f"📡 访问: {url}")
            
            response = self.session.get(url, timeout=30)
            logger.info(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"✅ 页面获取成功，内容长度: {len(response.text)}")
                
                # 保存页面内容
                with open('performance_page.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                logger.info("💾 页面内容已保存到 performance_page.html")
                
                return response.text
            else:
                logger.error(f"❌ 页面访问失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 访问页面异常: {e}")
            return None
    
    def extract_data_from_page(self, page_content):
        """从页面内容中提取数据"""
        logger.info("🔍 从页面内容中提取持仓数据")
        logger.info("=" * 60)
        
        if not page_content:
            return {}
        
        extracted_data = {}
        
        # 1. 查找JavaScript中的数据
        logger.info("🔍 搜索JavaScript数据...")
        
        # 常见的数据模式
        js_patterns = [
            r'window\.SNB\s*=\s*({.+?});',
            r'window\.performance\s*=\s*({.+?});',
            r'window\.portfolioData\s*=\s*({.+?});',
            r'window\.holdingsData\s*=\s*({.+?});',
            r'window\.stockData\s*=\s*({.+?});',
            r'var\s+performanceData\s*=\s*({.+?});',
            r'var\s+portfolioData\s*=\s*({.+?});',
            r'var\s+holdingsData\s*=\s*({.+?});',
        ]
        
        for pattern in js_patterns:
            matches = re.findall(pattern, page_content, re.DOTALL)
            if matches:
                for i, match in enumerate(matches):
                    try:
                        data = json.loads(match)
                        key = f"js_data_{pattern.split('\\')[0]}_{i}"
                        extracted_data[key] = data
                        logger.info(f"   ✅ 找到JS数据: {key} (长度: {len(str(data))})")
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ JS数据解析失败: {pattern}")
        
        # 2. 查找JSON数据块
        logger.info("🔍 搜索JSON数据块...")
        
        json_patterns = [
            r'"holdings":\s*\[([^\]]+)\]',
            r'"positions":\s*\[([^\]]+)\]',
            r'"stocks":\s*\[([^\]]+)\]',
            r'"portfolio":\s*{([^}]+)}',
            r'"performance":\s*{([^}]+)}',
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, page_content)
            if matches:
                logger.info(f"   ✅ 找到JSON模式: {pattern} ({len(matches)} 个匹配)")
                extracted_data[f"json_{pattern}"] = matches
        
        # 3. 查找股票代码和名称
        logger.info("🔍 搜索股票信息...")
        
        # 股票代码模式 (SH/SZ + 6位数字)
        stock_codes = re.findall(r'["\']?(SH|SZ)\d{6}["\']?', page_content)
        if stock_codes:
            unique_codes = list(set(stock_codes))
            logger.info(f"   📊 找到股票代码: {unique_codes}")
            extracted_data['stock_codes'] = unique_codes
        
        # 4. 查找数字金额 (可能是持仓金额)
        logger.info("🔍 搜索金额数据...")
        
        amount_patterns = [
            r'"amount":\s*([0-9.]+)',
            r'"value":\s*([0-9.]+)',
            r'"market_value":\s*([0-9.]+)',
            r'"cost":\s*([0-9.]+)',
            r'"shares":\s*([0-9.]+)',
        ]
        
        for pattern in amount_patterns:
            matches = re.findall(pattern, page_content)
            if matches:
                logger.info(f"   💰 找到金额数据: {pattern} ({len(matches)} 个)")
                extracted_data[f"amounts_{pattern}"] = matches
        
        # 5. 查找特定关键词周围的内容
        logger.info("🔍 搜索关键词上下文...")
        
        keywords = ['持仓', '股票', '市值', '成本', '盈亏', 'holdings', 'positions', 'stocks', 'portfolio']
        
        for keyword in keywords:
            pattern = f'.{{0,100}}{keyword}.{{0,100}}'
            matches = re.findall(pattern, page_content, re.IGNORECASE)
            if matches:
                logger.info(f"   🎯 找到关键词 '{keyword}': {len(matches)} 个上下文")
                extracted_data[f"context_{keyword}"] = matches[:5]  # 只保留前5个
        
        return extracted_data
    
    def try_performance_apis(self):
        """尝试performance相关的API"""
        logger.info("🔗 尝试performance相关API")
        logger.info("=" * 60)
        
        # 可能的performance API端点
        api_endpoints = [
            "https://xueqiu.com/v4/stock/portfolio/performance.json",
            "https://xueqiu.com/v4/stock/portfolio/holdings.json",
            "https://xueqiu.com/v4/stock/portfolio/positions.json",
            "https://xueqiu.com/performance/api/holdings.json",
            "https://xueqiu.com/performance/api/portfolio.json",
            "https://xueqiu.com/performance/api/positions.json",
            "https://xueqiu.com/api/performance/holdings.json",
            "https://xueqiu.com/api/performance/portfolio.json",
            "https://xueqiu.com/api/v1/performance/holdings.json",
            "https://xueqiu.com/api/v1/performance/portfolio.json",
        ]
        
        api_results = {}
        
        for endpoint in api_endpoints:
            try:
                logger.info(f"\n🔗 测试API: {endpoint}")
                response = self.session.get(endpoint, timeout=10)
                
                logger.info(f"   状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   ✅ 成功获取JSON数据")
                        
                        # 检查是否包含持仓信息
                        data_str = json.dumps(data, ensure_ascii=False)
                        if any(keyword in data_str.lower() for keyword in ['holdings', 'positions', 'stocks', '持仓', '股票']):
                            logger.info(f"   🎯 包含持仓相关信息！")
                            api_results[endpoint] = {
                                'data': data,
                                'has_holdings': True
                            }
                        else:
                            logger.info(f"   📊 数据预览: {data_str[:200]}...")
                            api_results[endpoint] = {
                                'data': data,
                                'has_holdings': False
                            }
                            
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON响应")
                        api_results[endpoint] = {
                            'content': response.text[:500],
                            'is_json': False
                        }
                        
                elif response.status_code in [401, 403]:
                    logger.info(f"   🔒 需要认证")
                else:
                    logger.info(f"   ❌ 失败")
                    
            except Exception as e:
                logger.info(f"   ❌ 异常: {e}")
        
        return api_results
    
    def analyze_extracted_data(self, extracted_data):
        """分析提取的数据"""
        logger.info("📊 分析提取的数据")
        logger.info("=" * 60)
        
        holdings_info = []
        
        # 分析股票代码
        if 'stock_codes' in extracted_data:
            codes = extracted_data['stock_codes']
            logger.info(f"📊 发现股票代码: {codes}")
            
            # 为每个股票代码获取详细信息
            for code in codes:
                try:
                    # 清理代码格式
                    clean_code = code.replace('"', '').replace("'", "")
                    
                    # 获取股票信息
                    quote = ball.quotec(clean_code)
                    if quote and 'data' in quote and quote['data']:
                        stock_data = quote['data'][0]
                        holdings_info.append({
                            'stock_code': clean_code,
                            'stock_name': stock_data.get('name', ''),
                            'current_price': stock_data.get('current', 0),
                            'chg': stock_data.get('chg', 0),
                            'percent': stock_data.get('percent', 0),
                            'source': 'page_extraction'
                        })
                        logger.info(f"   📈 {stock_data.get('name', '')} ({clean_code}): ¥{stock_data.get('current', 0)}")
                        
                except Exception as e:
                    logger.warning(f"   ⚠️ 获取 {code} 信息失败: {e}")
        
        # 分析JS数据
        for key, data in extracted_data.items():
            if key.startswith('js_data_') and isinstance(data, dict):
                logger.info(f"🔍 分析JS数据: {key}")
                
                # 递归搜索持仓相关数据
                def search_holdings(obj, path=""):
                    if isinstance(obj, dict):
                        for k, v in obj.items():
                            current_path = f"{path}.{k}" if path else k
                            
                            # 检查键名是否包含持仓相关词汇
                            if any(keyword in k.lower() for keyword in ['holding', 'position', 'stock', 'portfolio']):
                                logger.info(f"   🎯 找到可能的持仓数据: {current_path}")
                                if isinstance(v, (list, dict)):
                                    logger.info(f"      数据类型: {type(v)}, 长度: {len(v) if hasattr(v, '__len__') else 'N/A'}")
                            
                            search_holdings(v, current_path)
                    elif isinstance(obj, list):
                        for i, item in enumerate(obj):
                            search_holdings(item, f"{path}[{i}]")
                
                search_holdings(data)
        
        return holdings_info

def main():
    """主函数"""
    print("🎯 从雪球performance页面获取真实持仓数据")
    print("访问 https://xueqiu.com/performance")
    print("=" * 80)
    
    getter = PerformanceDataGetter()
    
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'page_content_length': 0,
        'extracted_data': {},
        'api_results': {},
        'holdings_info': []
    }
    
    # 1. 获取performance页面
    logger.info("\n🌐 1. 获取performance页面")
    page_content = getter.get_performance_page()
    all_results['page_content_length'] = len(page_content) if page_content else 0
    
    # 2. 从页面提取数据
    if page_content:
        logger.info("\n🔍 2. 从页面提取数据")
        extracted_data = getter.extract_data_from_page(page_content)
        all_results['extracted_data'] = extracted_data
        
        # 3. 分析提取的数据
        logger.info("\n📊 3. 分析提取的数据")
        holdings_info = getter.analyze_extracted_data(extracted_data)
        all_results['holdings_info'] = holdings_info
    
    # 4. 尝试performance API
    logger.info("\n🔗 4. 尝试performance相关API")
    api_results = getter.try_performance_apis()
    all_results['api_results'] = api_results
    
    # 5. 汇总结果
    logger.info("\n📈 5. 结果汇总")
    logger.info("=" * 60)
    
    if page_content:
        logger.info(f"✅ 页面内容: 获取成功 ({len(page_content)} 字符)")
    else:
        logger.warning("⚠️ 页面内容: 获取失败")
    
    if all_results['extracted_data']:
        logger.info(f"✅ 数据提取: 找到 {len(all_results['extracted_data'])} 个数据项")
    else:
        logger.warning("⚠️ 数据提取: 未找到有效数据")
    
    if all_results['holdings_info']:
        logger.info(f"✅ 持仓信息: 识别出 {len(all_results['holdings_info'])} 只股票")
        for holding in all_results['holdings_info']:
            logger.info(f"   📊 {holding['stock_name']} ({holding['stock_code']}): ¥{holding['current_price']}")
    else:
        logger.warning("⚠️ 持仓信息: 未识别出具体持仓")
    
    target_apis = [endpoint for endpoint, result in api_results.items() if result.get('has_holdings', False)]
    if target_apis:
        logger.info(f"✅ API发现: 找到 {len(target_apis)} 个包含持仓信息的API")
        for api in target_apis:
            logger.info(f"   🎯 {api}")
    else:
        logger.warning("⚠️ API发现: 未找到包含持仓信息的API")
    
    # 6. 保存结果
    try:
        with open('performance_holdings_data.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        logger.info(f"\n💾 完整结果已保存到: performance_holdings_data.json")
    except Exception as e:
        logger.error(f"\n❌ 保存结果失败: {e}")
    
    print(f"\n🎉 performance数据获取完成！")
    
    if all_results['holdings_info']:
        print(f"🎯 成功识别出 {len(all_results['holdings_info'])} 只股票的持仓信息")
    elif page_content:
        print(f"📄 页面内容已获取，请查看 performance_page.html 和 performance_holdings_data.json")
    else:
        print(f"⚠️ 无法访问performance页面，请检查登录状态")

if __name__ == '__main__':
    main()
