#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试股票信息获取问题
"""

import json
import logging
import requests
import pysnowball as ball

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_token():
    """加载token"""
    try:
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        cookies_str = config.get('cookies', '')
        xq_a_token = ''
        u = ''
        
        for cookie in cookies_str.split(';'):
            if '=' in cookie:
                key, value = cookie.strip().split('=', 1)
                if key == 'xq_a_token':
                    xq_a_token = value
                elif key == 'u':
                    u = value
        
        if xq_a_token and u:
            token = f"xq_a_token={xq_a_token};u={u}"
            ball.set_token(token)
            logger.info(f"✅ Token设置成功: u={u}")
            return True, xq_a_token, u
        return False, None, None
    except Exception as e:
        logger.error(f"❌ 加载token失败: {e}")
        return False, None, None

def test_stock_apis():
    """测试各种股票API"""
    print("🧪 测试股票信息获取API")
    print("=" * 50)
    
    # 加载token
    success, token, u = load_token()
    if not success:
        print("❌ Token加载失败")
        return
    
    # 测试股票列表
    test_stocks = [
        'SH600519',  # 贵州茅台
        '600519',    # 不带前缀
        'SZ000001',  # 平安银行
        '000001',    # 不带前缀
        'SH000001',  # 上证指数
    ]
    
    for stock_symbol in test_stocks:
        print(f"\n📊 测试股票: {stock_symbol}")
        print("-" * 30)
        
        # 1. 测试pysnowball的quotec
        try:
            result = ball.quotec(stock_symbol)
            print(f"1. ball.quotec() 结果:")
            if result and 'data' in result and result['data']:
                quote_data = result['data'][0]
                print(f"   ✅ 成功: {quote_data.get('name', 'N/A')} "
                      f"价格: {quote_data.get('current', 'N/A')}")
                print(f"   完整数据: {quote_data}")
            else:
                print(f"   ❌ 失败或数据为空: {result}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        # 2. 测试直接API调用
        try:
            url = f"https://stock.xueqiu.com/v5/stock/realtime/quotec.json?symbol={stock_symbol}"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Cookie': f'xq_a_token={token};u={u}',
                'Referer': 'https://xueqiu.com/'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            print(f"2. 直接API调用:")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data and 'data' in data and data['data']:
                    quote_data = data['data'][0]
                    print(f"   ✅ 成功: {quote_data.get('name', 'N/A')} "
                          f"价格: {quote_data.get('current', 'N/A')}")
                else:
                    print(f"   ❌ 数据为空: {data}")
            else:
                print(f"   ❌ 请求失败: {response.text[:200]}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
        
        # 3. 测试搜索API
        try:
            search_result = ball.suggest_stock(stock_symbol)
            print(f"3. ball.suggest_stock() 结果:")
            if search_result and 'data' in search_result:
                stocks = search_result['data']
                print(f"   ✅ 搜索到 {len(stocks)} 个结果")
                for stock in stocks[:3]:
                    print(f"     {stock.get('query', 'N/A')} ({stock.get('code', 'N/A')})")
            else:
                print(f"   ❌ 搜索失败: {search_result}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def test_different_formats():
    """测试不同的股票代码格式"""
    print("\n🔍 测试不同股票代码格式")
    print("=" * 50)
    
    # 茅台的各种格式
    formats = [
        'SH600519',
        '600519',
        'SH.600519',
        '600519.SH',
        'CN_600519',
        'SHA:600519'
    ]
    
    for fmt in formats:
        print(f"\n测试格式: {fmt}")
        try:
            result = ball.quotec(fmt)
            if result and 'data' in result and result['data']:
                quote_data = result['data'][0]
                print(f"✅ 成功: {quote_data.get('name', 'N/A')} - {quote_data.get('symbol', 'N/A')}")
            else:
                print(f"❌ 失败: {result}")
        except Exception as e:
            print(f"❌ 异常: {e}")

def test_search_function():
    """测试搜索功能"""
    print("\n🔍 测试股票搜索功能")
    print("=" * 50)
    
    search_terms = ['茅台', '平安', '腾讯', '阿里', '600519']
    
    for term in search_terms:
        print(f"\n搜索: {term}")
        try:
            result = ball.suggest_stock(term)
            if result and 'data' in result:
                stocks = result['data']
                print(f"✅ 找到 {len(stocks)} 个结果:")
                for i, stock in enumerate(stocks[:5]):
                    print(f"  {i+1}. {stock.get('query', 'N/A')} ({stock.get('code', 'N/A')})")
            else:
                print(f"❌ 搜索失败: {result}")
        except Exception as e:
            print(f"❌ 异常: {e}")

def test_manual_request():
    """手动测试请求"""
    print("\n🔧 手动测试HTTP请求")
    print("=" * 50)
    
    success, token, u = load_token()
    if not success:
        return
    
    # 构建请求
    url = "https://stock.xueqiu.com/v5/stock/realtime/quotec.json?symbol=SH600519"
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://xueqiu.com/',
        'Origin': 'https://xueqiu.com'
    })
    
    # 设置cookies
    session.cookies.set('xq_a_token', token)
    session.cookies.set('u', u)
    
    try:
        # 先访问主页获取必要的cookies
        print("1. 访问雪球主页...")
        main_response = session.get('https://xueqiu.com/', timeout=10)
        print(f"   主页状态: {main_response.status_code}")
        
        # 再请求股票数据
        print("2. 请求股票数据...")
        response = session.get(url, timeout=10)
        print(f"   API状态: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ 响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主函数"""
    print("🐛 股票信息获取调试工具")
    print("=" * 60)
    
    # 运行各种测试
    test_stock_apis()
    test_different_formats()
    test_search_function()
    test_manual_request()
    
    print("\n📋 调试完成")
    print("请查看上述输出，找出股票信息获取失败的原因")

if __name__ == '__main__':
    main()
