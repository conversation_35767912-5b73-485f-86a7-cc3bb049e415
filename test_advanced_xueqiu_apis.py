#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试高级雪球API
基于GitHub发现的easytrader和poshare库
突破API权限限制，获取真实持仓数据
"""

import json
import logging
import easytrader
from poshare import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedXueqiuTester:
    """高级雪球API测试器"""
    
    def __init__(self):
        self.user_id = "4380321271"
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.cookies = config.get('cookies', '')
            logger.info(f"✅ 配置加载成功")
        except Exception as e:
            logger.error(f"❌ 配置加载失败: {e}")
            self.cookies = ''
    
    def test_easytrader_with_portfolio_codes(self):
        """测试easytrader与不同组合代码"""
        logger.info("🧪 测试easytrader与不同组合代码")
        logger.info("=" * 60)
        
        # 尝试不同的组合代码格式
        portfolio_codes = [
            # 基于用户ID的可能组合代码
            f"ZH{self.user_id}",  # 直接用户ID
            f"ZH{int(self.user_id):06d}",  # 6位格式
            f"ZH{int(self.user_id):08d}",  # 8位格式
            
            # 常见的组合ID范围
            "ZH000001", "ZH000002", "ZH000003",
            "ZH001001", "ZH001002", "ZH001003",
            "ZH100001", "ZH100002", "ZH100003",
            
            # 基于用户ID的变体
            f"ZH{int(self.user_id) + 1:06d}",
            f"ZH{int(self.user_id) + 2:06d}",
            f"ZH{int(self.user_id) - 1:06d}",
            f"ZH{int(self.user_id) - 2:06d}",
        ]
        
        successful_portfolios = []
        
        for portfolio_code in portfolio_codes:
            try:
                logger.info(f"\n🔍 测试组合代码: {portfolio_code}")
                
                # 创建easytrader实例
                xq = easytrader.use('xq')
                
                # 准备登录信息
                login_info = {
                    'cookies': self.cookies,
                    'portfolio_code': portfolio_code,
                    'portfolio_market': 'cn'
                }
                
                # 尝试登录
                xq.prepare(**login_info)
                logger.info(f"   ✅ 登录成功")
                
                # 测试各种功能
                test_results = {}
                
                # 测试余额
                try:
                    balance = xq.balance
                    test_results['balance'] = balance
                    logger.info(f"   💰 余额: {balance}")
                except Exception as e:
                    test_results['balance'] = f"失败: {e}"
                    logger.info(f"   ❌ 余额获取失败: {e}")
                
                # 测试持仓
                try:
                    position = xq.position
                    test_results['position'] = position
                    logger.info(f"   📊 持仓: {position}")
                except Exception as e:
                    test_results['position'] = f"失败: {e}"
                    logger.info(f"   ❌ 持仓获取失败: {e}")
                
                # 测试今日委托
                try:
                    entrusts = xq.today_entrusts
                    test_results['today_entrusts'] = entrusts
                    logger.info(f"   📋 今日委托: {entrusts}")
                except Exception as e:
                    test_results['today_entrusts'] = f"失败: {e}"
                    logger.info(f"   ❌ 今日委托获取失败: {e}")
                
                # 测试今日成交
                try:
                    trades = xq.today_trades
                    test_results['today_trades'] = trades
                    logger.info(f"   💼 今日成交: {trades}")
                except Exception as e:
                    test_results['today_trades'] = f"失败: {e}"
                    logger.info(f"   ❌ 今日成交获取失败: {e}")
                
                # 如果有任何成功的结果，记录这个组合
                if any(not str(result).startswith('失败') for result in test_results.values()):
                    successful_portfolios.append({
                        'portfolio_code': portfolio_code,
                        'results': test_results
                    })
                    logger.info(f"   🎉 组合 {portfolio_code} 有成功的API调用！")
                
            except Exception as e:
                logger.info(f"   ❌ 组合 {portfolio_code} 测试失败: {e}")
        
        return successful_portfolios
    
    def test_poshare_library(self):
        """测试poshare库"""
        logger.info("🧪 测试poshare库")
        logger.info("=" * 60)
        
        try:
            # 配置poshare
            Xueqiu.config({
                'cookie': self.cookies
            })
            logger.info("✅ poshare配置成功")
            
            # 尝试不同的组合symbol
            test_symbols = [
                'ZH1254937',  # 博客示例
                f'ZH{self.user_id}',  # 用户ID
                f'ZH{int(self.user_id):06d}',  # 6位格式
                'ZH000001', 'ZH000002', 'ZH000003',  # 常见ID
            ]
            
            successful_data = []
            
            for symbol in test_symbols:
                try:
                    logger.info(f"\n🔍 测试poshare组合: {symbol}")
                    
                    xq = Xueqiu(symbol=symbol)
                    
                    # 测试各种属性和方法
                    test_methods = [
                        ('组合名称', lambda: xq.cube_name),
                        ('组合信息', lambda: xq.cube_info),
                        ('股票配置', lambda: xq.cube_pie_data),
                        ('详细仓位', lambda: xq.cube_tree_data),
                        ('调仓历史', lambda: xq.history()),
                        ('收益率走势', lambda: xq.all()),
                        ('业绩评级', lambda: xq.summary()),
                        ('最新调仓', lambda: xq.show_origin()),
                    ]
                    
                    symbol_results = {}
                    
                    for method_name, method_func in test_methods:
                        try:
                            result = method_func()
                            symbol_results[method_name] = result
                            logger.info(f"   ✅ {method_name}: 成功获取数据")
                            
                            # 显示部分数据
                            if isinstance(result, (dict, list)):
                                preview = str(result)[:200] + "..." if len(str(result)) > 200 else str(result)
                                logger.info(f"      数据预览: {preview}")
                            else:
                                logger.info(f"      数据: {result}")
                                
                        except Exception as e:
                            symbol_results[method_name] = f"失败: {e}"
                            logger.info(f"   ❌ {method_name}: {e}")
                    
                    # 如果有成功的方法，记录这个组合
                    if any(not str(result).startswith('失败') for result in symbol_results.values()):
                        successful_data.append({
                            'symbol': symbol,
                            'results': symbol_results
                        })
                        logger.info(f"   🎉 组合 {symbol} 有成功的数据获取！")
                
                except Exception as e:
                    logger.info(f"   ❌ 组合 {symbol} 初始化失败: {e}")
            
            return successful_data
            
        except Exception as e:
            logger.error(f"❌ poshare测试失败: {e}")
            return []
    
    def search_my_portfolios_advanced(self):
        """高级搜索我的组合"""
        logger.info("🔍 高级搜索我的组合")
        logger.info("=" * 60)
        
        found_portfolios = []
        
        # 方法1: 通过easytrader搜索
        try:
            logger.info("📋 方法1: 通过easytrader搜索")
            
            # 尝试创建一个基础的easytrader实例
            xq = easytrader.use('xq')
            
            # 使用基础配置
            basic_login = {
                'cookies': self.cookies,
                'portfolio_code': 'ZH000001',  # 使用一个基础代码
                'portfolio_market': 'cn'
            }
            
            xq.prepare(**basic_login)
            
            # 尝试搜索股票功能（可能会暴露组合信息）
            try:
                search_result = xq.search_stock('方大特钢')
                logger.info(f"   股票搜索结果: {search_result}")
            except Exception as e:
                logger.info(f"   股票搜索失败: {e}")
            
        except Exception as e:
            logger.info(f"   easytrader搜索失败: {e}")
        
        # 方法2: 通过poshare搜索
        try:
            logger.info("\n📋 方法2: 通过poshare搜索")
            
            # 配置poshare
            Xueqiu.config({'cookie': self.cookies})
            
            # 尝试一些可能的组合ID
            for i in range(1, 20):
                try:
                    test_symbol = f"ZH{i:06d}"
                    xq = Xueqiu(symbol=test_symbol)
                    
                    # 尝试获取组合名称
                    name = xq.cube_name
                    if name and ('测试' in name or 'test' in name.lower() or '模拟' in name):
                        logger.info(f"   ✅ 找到可能的组合: {test_symbol} - {name}")
                        found_portfolios.append({
                            'symbol': test_symbol,
                            'name': name,
                            'source': 'poshare'
                        })
                
                except:
                    continue
        
        except Exception as e:
            logger.info(f"   poshare搜索失败: {e}")
        
        return found_portfolios

def main():
    """主函数"""
    print("🚀 高级雪球API测试")
    print("基于GitHub发现的easytrader和poshare库")
    print("尝试突破API权限限制")
    print("=" * 80)
    
    tester = AdvancedXueqiuTester()
    
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'user_id': tester.user_id,
        'easytrader_results': [],
        'poshare_results': [],
        'found_portfolios': []
    }
    
    # 1. 测试easytrader
    logger.info("\n🔧 1. 测试easytrader功能")
    easytrader_results = tester.test_easytrader_with_portfolio_codes()
    all_results['easytrader_results'] = easytrader_results
    
    # 2. 测试poshare
    logger.info("\n📊 2. 测试poshare功能")
    poshare_results = tester.test_poshare_library()
    all_results['poshare_results'] = poshare_results
    
    # 3. 高级搜索
    logger.info("\n🔍 3. 高级搜索组合")
    found_portfolios = tester.search_my_portfolios_advanced()
    all_results['found_portfolios'] = found_portfolios
    
    # 4. 汇总结果
    logger.info("\n📈 4. 测试结果汇总")
    logger.info("=" * 60)
    
    if easytrader_results:
        logger.info(f"✅ easytrader: 找到 {len(easytrader_results)} 个有效组合")
        for result in easytrader_results:
            logger.info(f"   📊 {result['portfolio_code']}: {list(result['results'].keys())}")
    else:
        logger.info("⚠️ easytrader: 没有找到有效组合")
    
    if poshare_results:
        logger.info(f"✅ poshare: 找到 {len(poshare_results)} 个有效组合")
        for result in poshare_results:
            logger.info(f"   📊 {result['symbol']}: {list(result['results'].keys())}")
    else:
        logger.info("⚠️ poshare: 没有找到有效组合")
    
    if found_portfolios:
        logger.info(f"✅ 搜索: 找到 {len(found_portfolios)} 个可能的目标组合")
        for portfolio in found_portfolios:
            logger.info(f"   🎯 {portfolio['symbol']}: {portfolio['name']}")
    else:
        logger.info("⚠️ 搜索: 没有找到目标组合")
    
    # 5. 保存结果
    try:
        with open('advanced_xueqiu_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        logger.info(f"\n💾 完整结果已保存到: advanced_xueqiu_test_results.json")
    except Exception as e:
        logger.error(f"\n❌ 保存结果失败: {e}")
    
    print(f"\n🎉 高级API测试完成！")
    
    # 总结发现
    total_success = len(easytrader_results) + len(poshare_results) + len(found_portfolios)
    if total_success > 0:
        print(f"🎯 成功突破API限制！找到 {total_success} 个有效的数据源")
        print("💡 建议: 使用找到的有效组合代码来获取详细的持仓数据")
    else:
        print("⚠️ 暂未找到有效的数据源，可能需要:")
        print("   1. 创建公开的雪球组合")
        print("   2. 确认组合代码格式")
        print("   3. 检查API权限设置")

if __name__ == '__main__':
    main()
