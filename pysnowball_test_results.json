{"timestamp": "2025-08-18T14:53:07.396951", "pysnowball_available": true, "token_set": true, "results": {"实时行情": {"status": "success", "data": {"symbol": "SH600519", "current": 1428.76, "percent": 0.47, "chg": 6.68, "timestamp": 1755499978140, "volume": 4531798, "amount": 6479154147.0, "market_capital": 1794805168728.0, "float_market_capital": 1794805168728.0, "turnover_rate": 0.36, "amplitude": 0.95, "open": 1426.99, "last_close": 1422.08, "high": 1436.64, "low": 1423.1, "avg_price": 1429.709, "trade_volume": 1500, "side": 1, "is_trade": true, "level": 1, "trade_session": null, "trade_type": null, "current_year_percent": -4.41, "trade_unique_id": "4531798", "type": 11, "bid_appl_seq_num": null, "offer_appl_seq_num": null, "volume_ext": null, "traded_amount_ext": null, "trade_type_v2": null, "yield_to_maturity": null}, "message": "实时行情获取成功"}, "五档行情": {"status": "failed", "error": "返回数据格式错误", "message": "五档行情测试失败"}, "股票搜索": {"status": "success", "data": [{"code": "SH600519", "label": "11", "query": "贵州茅台", "state": 1, "stock_type": 11, "type": 1}], "message": "股票搜索成功"}, "K线数据": {"status": "success", "data": {"symbol": "SH600519", "column": ["timestamp", "volume", "open", "high", "low", "close", "chg", "percent", "turnoverrate", "amount", "volume_post", "amount_post", "pe", "pb", "ps", "pcf", "market_capital", "balance", "hold_volume_cn", "hold_ratio_cn", "net_volume_cn", "hold_volume_hk", "hold_ratio_hk", "net_volume_hk"], "item": [[1754928000000, 4201923, 1449.0, 1465.07, 1436.0, 1437.04, -7.96, -0.55, 0.33, 6095342036.0, null, null, 20.0722, 6.987, 9.9304, 26.1772, 1805206486512.0, 16772860734.8, 72866199, 5.8, 0, null, null, null], [1755014400000, 6552754, 1425.0, 1433.68, 1420.0, 1420.05, -16.99, -1.18, 0.52, 9325460101.0, null, null, 19.8349, 7.475, 9.813, 25.8677, 1783863685890.0, 17233073126.25, 72866199, 5.8, 0, null, null, null], [1755100800000, 4812930, 1420.94, 1447.51, 1420.94, 1426.99, 6.94, 0.49, 0.38, 6897535330.0, null, null, 19.9319, 7.512, 9.8609, 25.9942, 1792581698622.0, 17038450462.55, 72866199, 5.8, 0, null, null, null], [1755187200000, 4758165, 1426.01, 1428.66, 1420.22, 1422.08, -4.91, -0.34, 0.38, 6772436997.0, null, null, 19.863, 7.486, 9.826978999053228, 25.904726666932948, 1786413767424.0, 17181900971.6, 72866199, 5.8, 0, null, null, null], [1755446400000, 4532498, 1426.99, 1436.64, 1423.1, 1428.82, 6.74, 0.47, 0.36, 6480154275.0, null, null, 19.957, 7.521, 9.873139707110212, 26.02641009833984, 1794805168728.0, null, null, null, null, null, null, null]]}, "message": "K线数据获取成功"}, "自选股列表": {"status": "success", "data": {"cubes": [{"id": -120, "name": "全部", "order_id": -50, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -27, "name": "沪深", "order_id": -40, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -26, "name": "港股", "order_id": -30, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -25, "name": "美股", "order_id": -20, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -24, "name": "我的", "order_id": -10, "category": 3, "include": false, "symbol_count": 0, "type": -1}, {"id": -23, "name": "基金", "order_id": -7, "category": 3, "include": false, "symbol_count": 0, "type": -1}], "funds": [{"id": -110, "name": "全部", "order_id": -30, "category": 2, "include": false, "symbol_count": 0, "type": -1}, {"id": -17, "name": "基金", "order_id": -20, "category": 2, "include": false, "symbol_count": 0, "type": -1}, {"id": -16, "name": "私募", "order_id": -10, "category": 2, "include": false, "symbol_count": 0, "type": -1}], "stocks": [{"id": -10, "name": "实盘", "order_id": -60, "category": 1, "include": false, "symbol_count": 0, "type": -1}, {"id": -1, "name": "全部", "order_id": -50, "category": 1, "include": false, "symbol_count": 0, "type": -1}, {"id": -5, "name": "沪深", "order_id": -40, "category": 1, "include": false, "symbol_count": 0, "type": -1}, {"id": -7, "name": "港股", "order_id": -30, "category": 1, "include": false, "symbol_count": 0, "type": -1}, {"id": -6, "name": "美股", "order_id": -20, "category": 1, "include": false, "symbol_count": 0, "type": -1}, {"id": -4, "name": "模拟", "order_id": -10, "category": 1, "include": false, "symbol_count": 0, "type": -1}]}, "message": "自选股列表获取成功"}, "自选股详情": {"status": "success", "data": {"pid": -1, "stocks": []}, "message": "自选股详情获取成功"}}, "summary": {"total_tests": 6, "successful_tests": 5, "failed_tests": 1}}