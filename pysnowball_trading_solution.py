#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于pysnowball的正确雪球交易解决方案
解决 'portfolio_market' 错误的根本方案
"""

import json
import requests
import logging
from datetime import datetime

class PySnowballTrader:
    """
    基于pysnowball的雪球交易器
    正确实现雪球API调用，避免easytrader的问题
    """
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.token = None
        self.cookies = {}
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
        }
        self.load_config()
        logging.basicConfig(level=logging.INFO)
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 解析cookies
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.cookies[key] = value
            
            # 设置token (pysnowball格式)
            xq_a_token = self.cookies.get('xq_a_token', '')
            u = self.cookies.get('u', '')
            if xq_a_token and u:
                self.token = f"xq_a_token={xq_a_token};u={u}"
                logging.info("✅ 配置加载成功")
            else:
                logging.error("❌ Token格式不正确")
                
        except Exception as e:
            logging.error(f"❌ 加载配置失败: {e}")
    
    def fetch_api(self, url, method='GET', data=None, host="xueqiu.com"):
        """通用API请求方法"""
        try:
            full_url = f"https://{host}/{url}" if not url.startswith('http') else url
            
            if method.upper() == 'GET':
                response = requests.get(
                    full_url,
                    headers=self.headers,
                    cookies=self.cookies,
                    timeout=10
                )
            else:
                response = requests.post(
                    full_url,
                    headers=self.headers,
                    cookies=self.cookies,
                    json=data,
                    timeout=10
                )
            
            if response.status_code == 200:
                return True, response.json()
            else:
                return False, f"HTTP {response.status_code}: {response.text}"
                
        except Exception as e:
            return False, f"请求异常: {str(e)}"
    
    def get_portfolio_list(self):
        """获取投资组合列表"""
        url = "v5/stock/portfolio/list.json?system=true"
        success, result = self.fetch_api(url)
        
        if success:
            portfolios = result.get('list', [])
            logging.info(f"✅ 获取到 {len(portfolios)} 个投资组合")
            return portfolios
        else:
            logging.error(f"❌ 获取投资组合失败: {result}")
            return []
    
    def get_portfolio_stocks(self, portfolio_id):
        """获取投资组合中的股票"""
        url = f"v5/stock/portfolio/stock/list.json?size=1000&category=1&pid={portfolio_id}"
        success, result = self.fetch_api(url)
        
        if success:
            stocks = result.get('list', [])
            logging.info(f"✅ 组合 {portfolio_id} 包含 {len(stocks)} 只股票")
            return stocks
        else:
            logging.error(f"❌ 获取组合股票失败: {result}")
            return []
    
    def get_stock_quote(self, symbol):
        """获取股票实时行情"""
        url = f"v5/stock/realtime/quotec.json?symbol={symbol}"
        success, result = self.fetch_api(url)
        
        if success:
            quote_data = result.get('data', [])
            if quote_data:
                logging.info(f"✅ 获取 {symbol} 行情成功")
                return quote_data[0]
            else:
                logging.error(f"❌ {symbol} 行情数据为空")
                return None
        else:
            logging.error(f"❌ 获取 {symbol} 行情失败: {result}")
            return None
    
    def get_stock_pankou(self, symbol):
        """获取股票五档行情"""
        url = f"v5/stock/realtime/pankou.json?symbol={symbol}"
        success, result = self.fetch_api(url)
        
        if success:
            pankou_data = result.get('data', {})
            logging.info(f"✅ 获取 {symbol} 五档行情成功")
            return pankou_data
        else:
            logging.error(f"❌ 获取 {symbol} 五档行情失败: {result}")
            return None
    
    def add_to_portfolio(self, portfolio_id, symbol, weight=None):
        """添加股票到投资组合 (模拟买入)"""
        try:
            # 这里实现添加股票到雪球投资组合的逻辑
            # 注意: 雪球的投资组合是虚拟的，不是真实交易
            
            url = f"v5/stock/portfolio/stock/add.json"
            data = {
                'portfolio_id': portfolio_id,
                'symbol': symbol,
                'weight': weight or 10.0,  # 默认权重10%
                'note': f'通过PySnowball添加于 {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
            }
            
            success, result = self.fetch_api(url, method='POST', data=data)
            
            if success:
                logging.info(f"✅ 成功添加 {symbol} 到组合 {portfolio_id}")
                return True, "添加成功"
            else:
                logging.error(f"❌ 添加 {symbol} 失败: {result}")
                return False, result
                
        except Exception as e:
            error_msg = f"添加股票异常: {str(e)}"
            logging.error(f"❌ {error_msg}")
            return False, error_msg
    
    def remove_from_portfolio(self, portfolio_id, symbol):
        """从投资组合移除股票 (模拟卖出)"""
        try:
            url = f"v5/stock/portfolio/stock/remove.json"
            data = {
                'portfolio_id': portfolio_id,
                'symbol': symbol,
                'note': f'通过PySnowball移除于 {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
            }
            
            success, result = self.fetch_api(url, method='POST', data=data)
            
            if success:
                logging.info(f"✅ 成功从组合 {portfolio_id} 移除 {symbol}")
                return True, "移除成功"
            else:
                logging.error(f"❌ 移除 {symbol} 失败: {result}")
                return False, result
                
        except Exception as e:
            error_msg = f"移除股票异常: {str(e)}"
            logging.error(f"❌ {error_msg}")
            return False, error_msg
    
    def get_account_info(self):
        """获取账户信息 (投资组合信息)"""
        try:
            portfolios = self.get_portfolio_list()
            
            if portfolios:
                # 计算总的投资组合信息
                total_value = 0
                total_gain = 0
                
                for portfolio in portfolios:
                    total_value += portfolio.get('net_value', 0)
                    total_gain += portfolio.get('gain_value', 0)
                
                account_info = {
                    'total_portfolios': len(portfolios),
                    'total_value': total_value,
                    'total_gain': total_gain,
                    'gain_percent': (total_gain / total_value * 100) if total_value > 0 else 0,
                    'portfolios': portfolios
                }
                
                logging.info("✅ 账户信息获取成功")
                return account_info
            else:
                logging.warning("⚠️ 没有找到投资组合")
                return None
                
        except Exception as e:
            logging.error(f"❌ 获取账户信息失败: {str(e)}")
            return None
    
    def test_connection(self):
        """测试连接"""
        try:
            portfolios = self.get_portfolio_list()
            if portfolios is not None:
                return True, f"连接成功，找到 {len(portfolios)} 个投资组合"
            else:
                return False, "连接失败，无法获取投资组合"
        except Exception as e:
            return False, f"连接测试失败: {str(e)}"

def create_pysnowball_config():
    """创建pysnowball配置指导"""
    return {
        'title': 'PySnowball 配置指导',
        'description': '基于pysnowball项目的正确雪球API使用方法',
        'github_url': 'https://github.com/uname-yang/pysnowball',
        'steps': [
            {
                'step': 1,
                'title': '安装pysnowball',
                'command': 'pip install pysnowball',
                'description': '安装pysnowball库'
            },
            {
                'step': 2,
                'title': '获取雪球token',
                'description': '登录雪球网站，获取xq_a_token和u参数',
                'details': [
                    '打开雪球网站 https://xueqiu.com',
                    '登录你的账户',
                    '按F12打开开发者工具',
                    '在Application -> Cookies中找到xq_a_token和u',
                    '复制这两个值'
                ]
            },
            {
                'step': 3,
                'title': '设置token',
                'code': '''
import pysnowball as ball

# 设置token
ball.set_token("xq_a_token=your_token_here;u=your_user_id_here")
                ''',
                'description': '在代码中设置token'
            },
            {
                'step': 4,
                'title': '使用API',
                'code': '''
# 获取自选股列表
portfolios = ball.watch_list()

# 获取股票行情
quote = ball.realtime_quote("SH600519")

# 获取组合净值
nav = ball.nav_daily("ZH123456")
                ''',
                'description': '使用pysnowball的各种API'
            }
        ],
        'advantages': [
            '✅ 完整的雪球API覆盖',
            '✅ 正确的token认证机制',
            '✅ 稳定的接口实现',
            '✅ 持续维护和更新',
            '✅ 避免easytrader的portfolio_market错误'
        ]
    }

def main():
    """主函数 - 演示pysnowball的使用"""
    print("🐍 PySnowball 雪球交易解决方案")
    print("=" * 50)
    
    # 创建交易器实例
    trader = PySnowballTrader()
    
    # 测试连接
    print("\n🧪 测试连接...")
    success, message = trader.test_connection()
    print(f"结果: {message}")
    
    if success:
        # 获取账户信息
        print("\n📊 获取账户信息...")
        account_info = trader.get_account_info()
        if account_info:
            print(f"投资组合数量: {account_info['total_portfolios']}")
            print(f"总价值: {account_info['total_value']:.2f}")
            print(f"总收益: {account_info['total_gain']:.2f}")
            print(f"收益率: {account_info['gain_percent']:.2f}%")
        
        # 获取股票行情示例
        print("\n📈 获取股票行情示例...")
        quote = trader.get_stock_quote("SH600519")  # 贵州茅台
        if quote:
            print(f"股票: {quote.get('name', 'N/A')}")
            print(f"价格: {quote.get('current', 'N/A')}")
            print(f"涨跌: {quote.get('chg', 'N/A')}")
    
    # 显示配置指导
    print("\n📋 配置指导:")
    config_guide = create_pysnowball_config()
    print(f"项目地址: {config_guide['github_url']}")
    print("详细步骤请查看PySnowball管理界面: http://localhost:5003")

if __name__ == '__main__':
    main()
