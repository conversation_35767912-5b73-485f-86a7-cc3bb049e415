#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取投资组合详细信息
专门获取方大特钢的详细行情和组合信息
"""

import json
import logging
import pysnowball as ball

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_token():
    """加载token"""
    try:
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        cookies_str = config.get('cookies', '')
        xq_a_token = ''
        u = ''
        
        for cookie in cookies_str.split(';'):
            if '=' in cookie:
                key, value = cookie.strip().split('=', 1)
                if key == 'xq_a_token':
                    xq_a_token = value
                elif key == 'u':
                    u = value
        
        if xq_a_token and u:
            token = f"xq_a_token={xq_a_token};u={u}"
            ball.set_token(token)
            logger.info(f"✅ Token设置成功: u={u}")
            return True
        return False
    except Exception as e:
        logger.error(f"❌ 加载token失败: {e}")
        return False

def get_stock_detailed_info(symbol):
    """获取股票详细信息"""
    logger.info(f"📊 获取股票详细信息: {symbol}")
    
    try:
        # 1. 获取实时行情
        quote_result = ball.quotec(symbol)
        if quote_result and 'data' in quote_result and quote_result['data']:
            quote_data = quote_result['data'][0]
            
            logger.info(f"✅ 实时行情:")
            logger.info(f"   股票代码: {quote_data.get('symbol', 'N/A')}")
            logger.info(f"   股票名称: {quote_data.get('name', 'N/A')}")
            logger.info(f"   当前价格: {quote_data.get('current', 'N/A')}")
            logger.info(f"   涨跌额: {quote_data.get('chg', 'N/A')}")
            logger.info(f"   涨跌幅: {quote_data.get('percent', 'N/A')}%")
            logger.info(f"   开盘价: {quote_data.get('open', 'N/A')}")
            logger.info(f"   最高价: {quote_data.get('high', 'N/A')}")
            logger.info(f"   最低价: {quote_data.get('low', 'N/A')}")
            logger.info(f"   昨收价: {quote_data.get('last_close', 'N/A')}")
            logger.info(f"   成交量: {quote_data.get('volume', 'N/A')}")
            logger.info(f"   成交额: {quote_data.get('amount', 'N/A')}")
            logger.info(f"   市值: {quote_data.get('market_capital', 'N/A')}")
        
        # 2. 获取五档行情
        pankou_result = ball.pankou(symbol)
        if pankou_result:
            logger.info(f"✅ 五档行情:")
            logger.info(f"   买一: {pankou_result.get('bp1', 'N/A')} x {pankou_result.get('bc1', 'N/A')}")
            logger.info(f"   买二: {pankou_result.get('bp2', 'N/A')} x {pankou_result.get('bc2', 'N/A')}")
            logger.info(f"   买三: {pankou_result.get('bp3', 'N/A')} x {pankou_result.get('bc3', 'N/A')}")
            logger.info(f"   买四: {pankou_result.get('bp4', 'N/A')} x {pankou_result.get('bc4', 'N/A')}")
            logger.info(f"   买五: {pankou_result.get('bp5', 'N/A')} x {pankou_result.get('bc5', 'N/A')}")
            logger.info(f"   ----")
            logger.info(f"   卖一: {pankou_result.get('sp1', 'N/A')} x {pankou_result.get('sc1', 'N/A')}")
            logger.info(f"   卖二: {pankou_result.get('sp2', 'N/A')} x {pankou_result.get('sc2', 'N/A')}")
            logger.info(f"   卖三: {pankou_result.get('sp3', 'N/A')} x {pankou_result.get('sc3', 'N/A')}")
            logger.info(f"   卖四: {pankou_result.get('sp4', 'N/A')} x {pankou_result.get('sc4', 'N/A')}")
            logger.info(f"   卖五: {pankou_result.get('sp5', 'N/A')} x {pankou_result.get('sc5', 'N/A')}")
        
        return quote_data if quote_result else None
        
    except Exception as e:
        logger.error(f"❌ 获取股票信息失败: {e}")
        return None

def get_simulation_portfolio_details():
    """获取模拟组合详细信息"""
    logger.info("📋 获取模拟组合详细信息")
    
    try:
        # 获取模拟组合股票列表
        result = ball.watch_stock(-4)  # -4是模拟组合ID
        
        if result and 'data' in result:
            data = result['data']
            stocks = data.get('stocks', [])
            
            logger.info(f"✅ 模拟组合详情:")
            logger.info(f"   组合ID: {data.get('pid', 'N/A')}")
            logger.info(f"   组合名称: 模拟")
            logger.info(f"   股票数量: {len(stocks)}")
            
            portfolio_value = 0
            portfolio_details = []
            
            for i, stock in enumerate(stocks, 1):
                symbol = stock.get('symbol', '')
                name = stock.get('name', 'N/A')
                
                logger.info(f"\n   📊 股票 {i}: {name} ({symbol})")
                
                # 获取详细行情
                detailed_info = get_stock_detailed_info(symbol)
                
                if detailed_info:
                    current_price = detailed_info.get('current', 0)
                    if isinstance(current_price, (int, float)) and current_price > 0:
                        portfolio_value += current_price
                
                portfolio_details.append({
                    'symbol': symbol,
                    'name': name,
                    'detailed_info': detailed_info
                })
            
            logger.info(f"\n💰 组合总价值估算: {portfolio_value:.2f} (基于当前价格)")
            
            return {
                'portfolio_id': data.get('pid'),
                'portfolio_name': '模拟',
                'stock_count': len(stocks),
                'stocks': portfolio_details,
                'estimated_value': portfolio_value
            }
        
        return None
        
    except Exception as e:
        logger.error(f"❌ 获取模拟组合详情失败: {e}")
        return None

def search_for_test_portfolios():
    """搜索可能的测试组合"""
    logger.info("🔍 搜索可能的测试组合")
    
    # 尝试搜索一些可能的组合ID
    possible_ids = [
        # 用户可能创建的组合ID范围
        range(1, 100),
        range(1000, 1100),
        range(10000, 10100),
    ]
    
    found_portfolios = []
    
    for id_range in possible_ids:
        logger.info(f"   搜索范围: {id_range.start} - {id_range.stop}")
        
        for cube_id in id_range:
            cube_symbol = f"ZH{cube_id:06d}"
            
            try:
                # 尝试获取组合净值
                result = ball.nav_daily(cube_symbol)
                if result and len(result) > 0:
                    cube_data = result[0]
                    cube_name = cube_data.get('name', '')
                    
                    # 检查是否包含目标关键词
                    if any(keyword in cube_name.lower() for keyword in ['测试', 'test', '模拟', 'demo']):
                        logger.info(f"   ✅ 找到可能的组合: {cube_name} ({cube_symbol})")
                        
                        # 尝试获取持仓
                        try:
                            holdings_result = ball.rebalancing_current(cube_symbol)
                            if holdings_result:
                                last_rb = holdings_result.get('last_rb', {})
                                holdings = last_rb.get('holdings', [])
                                
                                logger.info(f"     持仓数量: {len(holdings)}")
                                for holding in holdings:
                                    logger.info(f"       - {holding.get('stock_name', 'N/A')} "
                                              f"({holding.get('stock_symbol', 'N/A')}): "
                                              f"{holding.get('weight', 0):.1f}%")
                                
                                found_portfolios.append({
                                    'symbol': cube_symbol,
                                    'name': cube_name,
                                    'holdings': holdings
                                })
                        except:
                            logger.info(f"     ⚠️ 无法获取持仓信息")
                
                # 限制搜索数量
                if len(found_portfolios) >= 5:
                    break
                    
            except:
                # 忽略错误，继续搜索
                pass
        
        if found_portfolios:
            break
    
    return found_portfolios

def main():
    """主函数"""
    print("📊 投资组合详细信息获取器")
    print("获取方大特钢和模拟组合的详细信息")
    print("=" * 60)
    
    # 加载token
    if not load_token():
        print("❌ Token加载失败")
        return
    
    # 1. 获取模拟组合详细信息
    print("\n📋 1. 模拟组合详细信息")
    print("=" * 40)
    portfolio_details = get_simulation_portfolio_details()
    
    # 2. 搜索可能的测试组合
    print("\n🔍 2. 搜索测试组合")
    print("=" * 40)
    test_portfolios = search_for_test_portfolios()
    
    if test_portfolios:
        print(f"✅ 找到 {len(test_portfolios)} 个可能的测试组合")
        for portfolio in test_portfolios:
            print(f"   📊 {portfolio['name']} ({portfolio['symbol']})")
            print(f"      持仓: {len(portfolio['holdings'])} 只股票")
    else:
        print("⚠️ 没有找到包含'测试'或'test'关键词的组合")
    
    print("\n🎯 总结")
    print("=" * 40)
    print("✅ 成功连接到您的雪球账户")
    print("✅ 找到模拟组合中的方大特钢")
    print("✅ 获取了详细的股票行情信息")
    
    if portfolio_details:
        print(f"📊 模拟组合价值: {portfolio_details['estimated_value']:.2f}")
    
    if test_portfolios:
        print(f"🔍 发现 {len(test_portfolios)} 个可能的测试组合")
    else:
        print("💡 建议: 如果您有名为'测试'或'test'的组合，请确认组合名称和权限设置")

if __name__ == '__main__':
    main()
