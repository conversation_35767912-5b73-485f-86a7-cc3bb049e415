<template>
  <el-dialog
    v-model="visible"
    title="退款详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="refund-detail-container" v-if="refund">
      <div class="detail-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>退款单号：</label>
                <span>{{ refund.id }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>订单号：</label>
                <span>{{ refund.order_id }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>用户：</label>
                <span>{{ refund.user_name }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>状态：</label>
                <el-tag :type="getStatusTagType(refund.status)">
                  {{ getStatusText(refund.status) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 金额信息 -->
        <div class="info-section">
          <h3>金额信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>订单金额：</label>
                <span class="amount">¥{{ refund.order_amount.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>退款金额：</label>
                <span class="refund-amount">¥{{ refund.refund_amount.toFixed(2) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 退款原因 -->
        <div class="info-section">
          <h3>退款原因</h3>
          <div class="reason-content">
            {{ refund.refund_reason }}
          </div>
        </div>
        
        <!-- 时间信息 -->
        <div class="info-section">
          <h3>时间信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>申请时间：</label>
                <span>{{ formatDateTime(refund.apply_time) }}</span>
              </div>
            </el-col>
            <el-col :span="12" v-if="refund.process_time">
              <div class="info-item">
                <label>处理时间：</label>
                <span>{{ formatDateTime(refund.process_time) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 处理信息 -->
        <div class="info-section" v-if="refund.processor || refund.remark">
          <h3>处理信息</h3>
          <el-row :gutter="20" v-if="refund.processor">
            <el-col :span="12">
              <div class="info-item">
                <label>处理人：</label>
                <span>{{ refund.processor }}</span>
              </div>
            </el-col>
          </el-row>
          
          <div v-if="refund.remark" class="remark-content">
            <label>处理备注：</label>
            <div class="remark-text">{{ refund.remark }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="refund?.status === 'pending'"
          type="success" 
          @click="handleApprove"
        >
          批准退款
        </el-button>
        <el-button 
          v-if="refund?.status === 'pending'"
          type="danger" 
          @click="handleReject"
        >
          拒绝退款
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { financeService } from '@/fuwu/financeService'

// Props
interface Props {
  modelValue: boolean
  refund?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  refund: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 工具函数
const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    completed: '已完成'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    completed: 'info'
  }
  return statusMap[status] || 'info'
}

// 方法
const handleApprove = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要批准退款申请吗？\n退款金额：¥${props.refund.refund_amount}`,
      '批准退款',
      {
        confirmButtonText: '确定批准',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    const success = await financeService.processRefund(props.refund.id, 'approve', '管理员批准退款')
    
    if (success) {
      ElMessage.success('退款申请已批准')
      emit('refresh')
      handleClose()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleReject = async () => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒绝理由：',
      '拒绝退款',
      {
        confirmButtonText: '确定拒绝',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入拒绝理由...'
      }
    )
    
    const success = await financeService.processRefund(props.refund.id, 'reject', reason)
    
    if (success) {
      ElMessage.success('退款申请已拒绝')
      emit('refresh')
      handleClose()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.refund-detail-container {
  .detail-content {
    .info-section {
      margin-bottom: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      h3 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }
      
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        label {
          font-weight: 500;
          color: var(--el-text-color-regular);
          margin-right: 8px;
          min-width: 80px;
        }
        
        .amount {
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
        
        .refund-amount {
          font-weight: 600;
          color: #f56c6c;
        }
      }
      
      .reason-content,
      .remark-text {
        padding: 12px;
        background: white;
        border-radius: 4px;
        border: 1px solid var(--el-border-color-lighter);
        color: var(--el-text-color-regular);
        line-height: 1.5;
      }
      
      .remark-content {
        label {
          font-weight: 500;
          color: var(--el-text-color-regular);
          margin-bottom: 8px;
          display: block;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
