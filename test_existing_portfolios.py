#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试现有雪球组合的访问和操作
先验证基本功能，再尝试创建新组合
"""

import json
import logging
import requests
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExistingPortfolioTester:
    """现有组合测试器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ Token设置成功: u={u}")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            return False
    
    def test_user_cubes_api(self):
        """测试用户组合API的各种端点"""
        logger.info("🔍 测试用户组合API端点")
        logger.info("=" * 50)
        
        # 尝试不同的API端点
        api_endpoints = [
            'https://xueqiu.com/cubes/list.json',
            'https://xueqiu.com/v4/stock/portfolio/list.json',
            'https://xueqiu.com/stock/portfolio/list.json',
            'https://xueqiu.com/v5/stock/portfolio/list.json',
            'https://xueqiu.com/cubes/mine.json',
            'https://xueqiu.com/cubes/user.json',
        ]
        
        for url in api_endpoints:
            try:
                logger.info(f"   测试: {url}")
                response = self.session.get(url, timeout=10)
                
                logger.info(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   ✅ 响应成功，数据类型: {type(data)}")
                        
                        # 检查数据结构
                        if isinstance(data, dict):
                            keys = list(data.keys())
                            logger.info(f"   数据键: {keys}")
                            
                            # 查找可能的组合数据
                            for key in ['list', 'data', 'cubes', 'portfolios']:
                                if key in data:
                                    items = data[key]
                                    logger.info(f"   找到 {key}: {len(items) if isinstance(items, list) else type(items)}")
                                    
                                    if isinstance(items, list) and items:
                                        sample = items[0]
                                        logger.info(f"   样本数据: {sample}")
                        
                        logger.info(f"   完整响应: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
                        
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON响应: {response.text[:200]}...")
                else:
                    logger.info(f"   ❌ 请求失败: {response.text[:200]}...")
                
                logger.info("-" * 30)
                
            except Exception as e:
                logger.error(f"   ❌ 请求异常: {e}")
                logger.info("-" * 30)
    
    def test_pysnowball_watch_list(self):
        """测试pysnowball的watch_list功能"""
        logger.info("🔍 测试pysnowball的watch_list")
        logger.info("=" * 50)
        
        try:
            result = ball.watch_list()
            logger.info(f"✅ watch_list响应成功")
            logger.info(f"完整数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result and 'data' in result:
                data = result['data']
                
                # 检查各种数据类型
                for key in ['cubes', 'stocks', 'portfolios']:
                    if key in data:
                        items = data[key]
                        logger.info(f"找到 {key}: {len(items) if isinstance(items, list) else type(items)}")
                        
                        if isinstance(items, list):
                            for i, item in enumerate(items):
                                logger.info(f"  {key}[{i}]: {item}")
                                
                                # 如果是组合，尝试获取详细信息
                                if key == 'cubes' and 'id' in item:
                                    cube_id = item['id']
                                    cube_symbol = f"ZH{cube_id:06d}"
                                    logger.info(f"    尝试获取组合详情: {cube_symbol}")
                                    
                                    try:
                                        cube_detail = ball.rebalancing_current(cube_symbol)
                                        if cube_detail:
                                            logger.info(f"    ✅ 组合详情获取成功")
                                            holdings = cube_detail.get('last_rb', {}).get('holdings', [])
                                            logger.info(f"    持仓数量: {len(holdings)}")
                                        else:
                                            logger.info(f"    ⚠️ 组合详情为空")
                                    except Exception as e:
                                        logger.info(f"    ❌ 获取组合详情失败: {e}")
            
        except Exception as e:
            logger.error(f"❌ watch_list测试失败: {e}")
    
    def search_user_portfolios(self):
        """搜索用户的投资组合"""
        logger.info("🔍 搜索用户投资组合")
        logger.info("=" * 50)
        
        # 获取用户ID
        u = self.session.cookies.get('u', '')
        logger.info(f"用户ID: {u}")
        
        if u:
            # 尝试通过用户ID搜索组合
            search_urls = [
                f"https://xueqiu.com/cubes/list.json?user_id={u}",
                f"https://xueqiu.com/u/{u}/cubes.json",
                f"https://xueqiu.com/v4/stock/portfolio/list.json?user_id={u}",
            ]
            
            for url in search_urls:
                try:
                    logger.info(f"   尝试: {url}")
                    response = self.session.get(url, timeout=10)
                    
                    if response.status_code == 200:
                        data = response.json()
                        logger.info(f"   ✅ 成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    else:
                        logger.info(f"   ❌ 失败: {response.status_code}")
                        
                except Exception as e:
                    logger.info(f"   ❌ 异常: {e}")
    
    def test_portfolio_creation_prerequisites(self):
        """测试创建组合的前置条件"""
        logger.info("🔍 测试创建组合的前置条件")
        logger.info("=" * 50)
        
        # 1. 检查用户信息
        try:
            user_info_url = "https://xueqiu.com/account/info.json"
            response = self.session.get(user_info_url, timeout=10)
            
            if response.status_code == 200:
                user_data = response.json()
                logger.info(f"✅ 用户信息: {json.dumps(user_data, indent=2, ensure_ascii=False)}")
            else:
                logger.info(f"❌ 用户信息获取失败: {response.status_code}")
                
        except Exception as e:
            logger.info(f"❌ 用户信息获取异常: {e}")
        
        # 2. 检查创建组合页面
        try:
            create_page_url = "https://xueqiu.com/cubes/create"
            response = self.session.get(create_page_url, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"✅ 创建组合页面访问成功")
                
                # 查找可能的CSRF token
                page_content = response.text
                if 'csrf' in page_content.lower():
                    logger.info("   页面包含CSRF相关内容")
                if 'token' in page_content.lower():
                    logger.info("   页面包含token相关内容")
                    
            else:
                logger.info(f"❌ 创建组合页面访问失败: {response.status_code}")
                
        except Exception as e:
            logger.info(f"❌ 创建组合页面访问异常: {e}")
    
    def test_simple_portfolio_creation(self):
        """测试简单的组合创建"""
        logger.info("🔍 测试简单组合创建")
        logger.info("=" * 50)
        
        # 尝试最简单的创建请求
        create_url = "https://xueqiu.com/cubes/create.json"
        
        simple_data = {
            'name': f'测试组合_{datetime.now().strftime("%H%M%S")}',
            'description': '通过API测试创建',
            'market': 'cn'
        }
        
        try:
            # 先访问创建页面
            self.session.get("https://xueqiu.com/cubes/create", timeout=10)
            
            # 再尝试创建
            response = self.session.post(create_url, data=simple_data, timeout=10)
            
            logger.info(f"创建响应状态: {response.status_code}")
            logger.info(f"创建响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if 'id' in result:
                    cube_id = result['id']
                    cube_symbol = f"ZH{cube_id:06d}"
                    logger.info(f"✅ 组合创建成功: {cube_symbol}")
                    return cube_symbol
                else:
                    logger.info(f"⚠️ 创建响应无ID: {result}")
            else:
                logger.info(f"❌ 创建失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ 创建异常: {e}")
        
        return None

def main():
    """主函数"""
    print("🧪 雪球现有组合测试")
    print("验证基本功能，寻找正确的API调用方式")
    print("=" * 60)
    
    tester = ExistingPortfolioTester()
    
    # 1. 测试各种用户组合API端点
    tester.test_user_cubes_api()
    
    # 2. 测试pysnowball的watch_list
    tester.test_pysnowball_watch_list()
    
    # 3. 搜索用户投资组合
    tester.search_user_portfolios()
    
    # 4. 测试创建组合的前置条件
    tester.test_portfolio_creation_prerequisites()
    
    # 5. 尝试简单的组合创建
    cube_symbol = tester.test_simple_portfolio_creation()
    
    if cube_symbol:
        print(f"\n🎉 成功创建组合: {cube_symbol}")
    else:
        print(f"\n⚠️ 组合创建失败，需要进一步研究API")

if __name__ == '__main__':
    main()
