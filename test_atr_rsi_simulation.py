#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ATR RSI策略模拟交易测试
"""

import sys
from datetime import datetime, timedelta
from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

# 导入必要的模块
try:
    from vnpy_ctastrategy import CtaStrategyApp
    from vnpy_ctabacktester import CtaBacktesterApp
    from vnpy_paperaccount import PaperAccountApp
    from vnpy_datamanager import DataManagerApp
    from vnpy_riskmanager import RiskManagerApp
except ImportError as e:
    print(f"⚠️ 模块导入失败: {e}")
    print("请先安装相关模块:")
    print("pip install vnpy-ctastrategy vnpy-ctabacktester vnpy-paperaccount vnpy-datamanager vnpy-riskmanager")

# 导入我们的策略
from atr_rsi_strategy import AtrRsiStrategy

class SimulationTester:
    """模拟交易测试器"""
    
    def __init__(self):
        self.main_engine = None
        self.event_engine = None
        self.main_window = None
        
    def setup_vnpy(self):
        """设置VeighNa环境"""
        print("🚀 初始化VeighNa模拟交易环境")
        print("=" * 60)
        
        # 创建Qt应用
        self.qapp = create_qapp()
        
        # 创建事件引擎
        self.event_engine = EventEngine()
        
        # 创建主引擎
        self.main_engine = MainEngine(self.event_engine)
        
        # 添加应用模块
        try:
            self.main_engine.add_app(CtaStrategyApp)
            print("✅ CTA策略模块加载成功")
        except:
            print("❌ CTA策略模块加载失败")
        
        try:
            self.main_engine.add_app(CtaBacktesterApp)
            print("✅ CTA回测模块加载成功")
        except:
            print("❌ CTA回测模块加载失败")
        
        try:
            self.main_engine.add_app(PaperAccountApp)
            print("✅ 模拟账户模块加载成功")
        except:
            print("❌ 模拟账户模块加载失败")
        
        try:
            self.main_engine.add_app(DataManagerApp)
            print("✅ 数据管理模块加载成功")
        except:
            print("❌ 数据管理模块加载失败")
        
        try:
            self.main_engine.add_app(RiskManagerApp)
            print("✅ 风险管理模块加载成功")
        except:
            print("❌ 风险管理模块加载失败")
        
        # 创建主窗口
        self.main_window = MainWindow(self.main_engine, self.event_engine)
        
        print("\n💡 VeighNa环境设置完成")
        
    def show_strategy_info(self):
        """显示策略信息"""
        print("\n📊 ATR RSI策略信息")
        print("=" * 60)
        
        print("📈 策略原理:")
        print("   • ATR (平均真实波幅): 衡量市场波动性")
        print("   • RSI (相对强弱指数): 判断超买超卖")
        print("   • 趋势判断: ATR > ATR均线时为趋势市场")
        print("   • 入场信号: 趋势市场中RSI极值反转")
        print("   • 止损方式: 移动止损跟踪最高/最低价")
        
        print("\n🔧 策略参数:")
        print("   • ATR周期: 14")
        print("   • ATR均线周期: 30") 
        print("   • RSI周期: 14")
        print("   • RSI入场阈值: 16 (买入>66, 卖出<34)")
        print("   • 移动止损: 0.8%")
        print("   • 固定手数: 1")
        
        print("\n📋 交易逻辑:")
        print("   1. 判断市场状态 (趋势 vs 震荡)")
        print("   2. 趋势市场中等待RSI极值")
        print("   3. RSI反转时入场")
        print("   4. 使用移动止损保护利润")
        print("   5. 震荡市场中平仓观望")
    
    def create_backtest_config(self):
        """创建回测配置"""
        print("\n🔙 创建回测配置")
        print("=" * 60)
        
        # 回测参数
        backtest_setting = {
            "symbol": "IF2312.CFFEX",  # 沪深300期货主力合约
            "interval": "1m",          # 1分钟K线
            "start": datetime(2023, 1, 1),
            "end": datetime(2023, 12, 31),
            "rate": 0.0001,           # 手续费率
            "slippage": 0.2,          # 滑点
            "size": 300,              # 合约乘数
            "pricetick": 0.2,         # 最小价格变动
            "capital": 1000000,       # 初始资金
        }
        
        # 策略参数
        strategy_setting = {
            "atr_length": 14,
            "atr_ma_length": 30,
            "rsi_length": 14,
            "rsi_entry": 16,
            "trailing_percent": 0.8,
            "fixed_size": 1
        }
        
        print("📊 回测配置:")
        print(f"   交易品种: {backtest_setting['symbol']}")
        print(f"   时间周期: {backtest_setting['start']} - {backtest_setting['end']}")
        print(f"   K线周期: {backtest_setting['interval']}")
        print(f"   初始资金: ¥{backtest_setting['capital']:,}")
        print(f"   手续费率: {backtest_setting['rate']*100}%")
        print(f"   滑点设置: {backtest_setting['slippage']}")
        
        return backtest_setting, strategy_setting
    
    def show_simulation_guide(self):
        """显示模拟交易指南"""
        print("\n🎯 模拟交易操作指南")
        print("=" * 60)
        
        print("📋 步骤1: 启动VeighNa")
        print("   • 运行: python test_atr_rsi_simulation.py")
        print("   • 等待VeighNa主界面打开")
        
        print("\n📋 步骤2: 配置模拟账户")
        print("   • 点击 '功能' -> '模拟账户'")
        print("   • 设置初始资金: 1,000,000")
        print("   • 设置手续费率: 0.01%")
        print("   • 启动模拟账户")
        
        print("\n📋 步骤3: 加载ATR RSI策略")
        print("   • 点击 '功能' -> 'CTA策略'")
        print("   • 点击 '新增策略'")
        print("   • 选择策略: AtrRsiStrategy")
        print("   • 设置交易品种: IF2312.CFFEX")
        print("   • 配置策略参数")
        
        print("\n📋 步骤4: 启动策略")
        print("   • 点击 '初始化' 初始化策略")
        print("   • 点击 '启动' 开始模拟交易")
        print("   • 观察策略运行状态")
        
        print("\n📋 步骤5: 监控交易")
        print("   • 查看持仓变化")
        print("   • 监控盈亏情况")
        print("   • 观察交易信号")
        
        print("\n⚠️ 注意事项:")
        print("   • 模拟交易使用虚拟资金，无真实风险")
        print("   • 建议先回测验证策略有效性")
        print("   • 观察策略在不同市场环境下的表现")
        print("   • 根据测试结果调整策略参数")
    
    def run_simulation(self):
        """运行模拟交易"""
        print("\n🚀 启动ATR RSI策略模拟交易")
        print("=" * 60)
        
        # 设置VeighNa环境
        self.setup_vnpy()
        
        # 显示策略信息
        self.show_strategy_info()
        
        # 创建回测配置
        backtest_setting, strategy_setting = self.create_backtest_config()
        
        # 显示操作指南
        self.show_simulation_guide()
        
        print("\n🌐 启动VeighNa图形界面...")
        print("请按照上述指南在界面中配置和运行策略")
        
        # 显示主窗口
        self.main_window.showMaximized()
        
        # 运行Qt应用
        self.qapp.exec()

def main():
    """主函数"""
    print("🎯 ATR RSI策略模拟交易测试")
    print("基于VeighNa框架的专业策略测试环境")
    print("=" * 80)
    
    # 创建测试器
    tester = SimulationTester()
    
    try:
        # 运行模拟交易
        tester.run_simulation()
        
    except Exception as e:
        print(f"\n❌ 模拟交易启动失败: {e}")
        print("\n💡 可能的解决方案:")
        print("1. 确保已安装VeighNa: pip install vnpy")
        print("2. 安装策略模块: pip install vnpy-ctastrategy")
        print("3. 安装模拟账户: pip install vnpy-paperaccount")
        print("4. 检查Python版本 >= 3.8")
        
        # 显示简化的策略信息
        tester.show_strategy_info()
        tester.show_simulation_guide()

if __name__ == "__main__":
    main()
