// 资金池服务 - 管理super_admin的资金池数据和交易记录

export interface FundPool {
  id: string
  name: string
  total_amount: number // 总资金
  available_amount: number // 可用资金
  frozen_amount: number // 冻结资金
  total_profit: number // 总收益
  total_profit_rate: number // 总收益率
  daily_profit: number // 日收益
  daily_profit_rate: number // 日收益率
  create_time: string
  update_time: string
  status: 'active' | 'suspended' | 'closed'
}

export interface TradeRecord {
  id: string
  fund_pool_id: string
  stock_code: string
  stock_name: string
  trade_type: 'buy' | 'sell'
  price: number
  quantity: number
  amount: number
  profit: number // 收益（卖出时计算）
  profit_rate: number // 收益率
  strategy_name: string
  trade_time: string
  status: 'pending' | 'completed' | 'cancelled'
}

export interface ProfitAnalysis {
  date: string
  daily_profit: number
  daily_profit_rate: number
  cumulative_profit: number
  cumulative_profit_rate: number
}

// 模拟资金池数据库
class FundPoolDatabase {
  private fundPools: FundPool[] = []
  private tradeRecords: TradeRecord[] = []
  private profitHistory: ProfitAnalysis[] = []

  constructor() {
    this.initializeData()
  }

  private initializeData() {
    // 初始化资金池数据
    this.fundPools = [
      {
        id: 'pool_001',
        name: 'AI量化主策略池',
        total_amount: 10000000, // 1000万
        available_amount: 2500000, // 250万可用
        frozen_amount: 7500000, // 750万持仓
        total_profit: 1250000, // 125万总收益
        total_profit_rate: 12.5, // 12.5%总收益率
        daily_profit: 25000, // 2.5万日收益
        daily_profit_rate: 0.25, // 0.25%日收益率
        create_time: '2024-01-01T00:00:00Z',
        update_time: new Date().toISOString(),
        status: 'active'
      },
      {
        id: 'pool_002',
        name: '稳健增长策略池',
        total_amount: 5000000, // 500万
        available_amount: 1000000, // 100万可用
        frozen_amount: 4000000, // 400万持仓
        total_profit: 400000, // 40万总收益
        total_profit_rate: 8.0, // 8%总收益率
        daily_profit: 8000, // 8千日收益
        daily_profit_rate: 0.16, // 0.16%日收益率
        create_time: '2024-01-01T00:00:00Z',
        update_time: new Date().toISOString(),
        status: 'active'
      }
    ]

    // 生成模拟交易记录
    this.generateMockTradeRecords()
    
    // 生成收益历史数据
    this.generateProfitHistory()
  }

  private generateMockTradeRecords() {
    const stocks = [
      { code: '000001', name: '平安银行' },
      { code: '000002', name: '万科A' },
      { code: '000858', name: '五粮液' },
      { code: '002415', name: '海康威视' },
      { code: '600036', name: '招商银行' },
      { code: '600519', name: '贵州茅台' },
      { code: '600887', name: '伊利股份' }
    ]

    const strategies = ['AI趋势策略', '均值回归策略', '动量突破策略', '套利策略']

    // 生成最近30天的交易记录
    for (let i = 0; i < 150; i++) {
      const stock = stocks[Math.floor(Math.random() * stocks.length)]
      const strategy = strategies[Math.floor(Math.random() * strategies.length)]
      const tradeType = Math.random() > 0.5 ? 'buy' : 'sell'
      const price = 10 + Math.random() * 90 // 10-100元
      const quantity = Math.floor(Math.random() * 10000) + 1000 // 1000-11000股
      const amount = price * quantity
      
      // 卖出时计算收益（模拟）
      const profit = tradeType === 'sell' ? amount * (Math.random() * 0.1 - 0.02) : 0
      const profitRate = tradeType === 'sell' ? (profit / amount) * 100 : 0

      const tradeTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

      this.tradeRecords.push({
        id: `trade_${Date.now()}_${i}`,
        fund_pool_id: Math.random() > 0.7 ? 'pool_001' : 'pool_002',
        stock_code: stock.code,
        stock_name: stock.name,
        trade_type: tradeType,
        price: Math.round(price * 100) / 100,
        quantity,
        amount: Math.round(amount * 100) / 100,
        profit: Math.round(profit * 100) / 100,
        profit_rate: Math.round(profitRate * 100) / 100,
        strategy_name: strategy,
        trade_time: tradeTime.toISOString(),
        status: 'completed'
      })
    }

    // 按时间排序
    this.tradeRecords.sort((a, b) => new Date(b.trade_time).getTime() - new Date(a.trade_time).getTime())
  }

  private generateProfitHistory() {
    // 生成最近90天的收益历史
    for (let i = 89; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
      const dailyProfitRate = (Math.random() - 0.3) * 2 // -0.6% 到 1.4%
      const dailyProfit = 15000000 * (dailyProfitRate / 100) // 基于总资金计算

      // 计算累计收益
      const daysSinceStart = 90 - i
      const cumulativeProfitRate = daysSinceStart * 0.12 + Math.random() * 2 - 1 // 模拟累计收益
      const cumulativeProfit = 15000000 * (cumulativeProfitRate / 100)

      this.profitHistory.push({
        date: date.toISOString().split('T')[0],
        daily_profit: Math.round(dailyProfit * 100) / 100,
        daily_profit_rate: Math.round(dailyProfitRate * 100) / 100,
        cumulative_profit: Math.round(cumulativeProfit * 100) / 100,
        cumulative_profit_rate: Math.round(cumulativeProfitRate * 100) / 100
      })
    }
  }

  // 获取所有资金池
  getFundPools(): FundPool[] {
    return this.fundPools
  }

  // 获取资金池详情
  getFundPoolById(id: string): FundPool | null {
    return this.fundPools.find(pool => pool.id === id) || null
  }

  // 获取交易记录（支持分页和筛选）
  getTradeRecords(options: {
    fundPoolId?: string
    tradeType?: 'buy' | 'sell'
    stockCode?: string
    page?: number
    pageSize?: number
    startDate?: string
    endDate?: string
  } = {}): { records: TradeRecord[], total: number } {
    let filtered = [...this.tradeRecords]

    // 筛选条件
    if (options.fundPoolId) {
      filtered = filtered.filter(record => record.fund_pool_id === options.fundPoolId)
    }
    if (options.tradeType) {
      filtered = filtered.filter(record => record.trade_type === options.tradeType)
    }
    if (options.stockCode) {
      filtered = filtered.filter(record => record.stock_code.includes(options.stockCode))
    }
    if (options.startDate) {
      filtered = filtered.filter(record => record.trade_time >= options.startDate!)
    }
    if (options.endDate) {
      filtered = filtered.filter(record => record.trade_time <= options.endDate!)
    }

    const total = filtered.length
    
    // 分页
    const page = options.page || 1
    const pageSize = options.pageSize || 20
    const start = (page - 1) * pageSize
    const records = filtered.slice(start, start + pageSize)

    return { records, total }
  }

  // 获取收益历史
  getProfitHistory(days: number = 30): ProfitAnalysis[] {
    return this.profitHistory.slice(-days)
  }

  // 获取资金池统计
  getFundPoolStats() {
    const totalAmount = this.fundPools.reduce((sum, pool) => sum + pool.total_amount, 0)
    const totalProfit = this.fundPools.reduce((sum, pool) => sum + pool.total_profit, 0)
    const totalProfitRate = totalAmount > 0 ? (totalProfit / totalAmount) * 100 : 0
    const dailyProfit = this.fundPools.reduce((sum, pool) => sum + pool.daily_profit, 0)
    const dailyProfitRate = totalAmount > 0 ? (dailyProfit / totalAmount) * 100 : 0

    return {
      totalAmount,
      totalProfit,
      totalProfitRate: Math.round(totalProfitRate * 100) / 100,
      dailyProfit,
      dailyProfitRate: Math.round(dailyProfitRate * 100) / 100,
      activePools: this.fundPools.filter(pool => pool.status === 'active').length,
      totalTrades: this.tradeRecords.length,
      profitableTrades: this.tradeRecords.filter(record => record.profit > 0).length
    }
  }
}

// 创建全局资金池数据库实例
const fundPoolDB = new FundPoolDatabase()

// 导出资金池服务
export const fundPoolService = {
  // 获取资金池列表
  getFundPools: async () => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return fundPoolDB.getFundPools()
  },

  // 获取资金池详情
  getFundPoolDetail: async (id: string) => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return fundPoolDB.getFundPoolById(id)
  },

  // 获取交易记录
  getTradeRecords: async (options: any = {}) => {
    await new Promise(resolve => setTimeout(resolve, 400))
    return fundPoolDB.getTradeRecords(options)
  },

  // 获取收益历史
  getProfitHistory: async (days: number = 30) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return fundPoolDB.getProfitHistory(days)
  },

  // 获取统计数据
  getFundPoolStats: async () => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return fundPoolDB.getFundPoolStats()
  }
}

export default fundPoolService
