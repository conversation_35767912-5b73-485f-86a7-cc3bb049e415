#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
注册ATR RSI策略到VeighNa系统
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入策略
try:
    from atr_rsi_strategy import AtrRsiStrategy
    print("✅ ATR RSI策略导入成功")
except ImportError as e:
    print(f"❌ ATR RSI策略导入失败: {e}")
    sys.exit(1)

def register_strategy():
    """注册策略到VeighNa"""
    try:
        # 检查策略类是否正确定义
        if hasattr(AtrRsiStrategy, '__name__'):
            print(f"✅ 策略类名: {AtrRsiStrategy.__name__}")
        
        if hasattr(AtrRsiStrategy, 'author'):
            print(f"✅ 策略作者: {AtrRsiStrategy.author}")
        
        if hasattr(AtrRsiStrategy, 'parameters'):
            print(f"✅ 策略参数: {AtrRsiStrategy.parameters}")
        
        if hasattr(AtrRsiStrategy, 'variables'):
            print(f"✅ 策略变量: {AtrRsiStrategy.variables}")
        
        print("\n📊 策略信息验证完成")
        print("策略已准备好在VeighNa中使用")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略注册失败: {e}")
        return False

def create_strategy_config():
    """创建策略配置文件"""
    config = {
        "strategy_class": "AtrRsiStrategy",
        "strategy_name": "atr_rsi_test",
        "vt_symbol": "IF2312.CFFEX",
        "setting": {
            "atr_length": 14,
            "atr_ma_length": 30,
            "rsi_length": 14,
            "rsi_entry": 16,
            "trailing_percent": 0.8,
            "fixed_size": 1
        }
    }
    
    import json
    with open('atr_rsi_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ 策略配置文件已创建: atr_rsi_config.json")

def main():
    """主函数"""
    print("🎯 注册ATR RSI策略到VeighNa系统")
    print("=" * 60)
    
    # 注册策略
    if register_strategy():
        print("✅ 策略注册成功")
        
        # 创建配置文件
        create_strategy_config()
        
        print("\n💡 使用说明:")
        print("1. 启动VeighNa: python run_vnpy_trader.py")
        print("2. 打开CTA策略模块")
        print("3. 新增策略: AtrRsiStrategy")
        print("4. 使用配置文件中的参数")
        
    else:
        print("❌ 策略注册失败")

if __name__ == "__main__":
    main()
