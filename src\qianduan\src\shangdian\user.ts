import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, LoginForm, RegisterForm } from '@/types/user'
import { userApi } from '@/fuwu/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>('')
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isVip = computed(() => userInfo.value?.dingyue_leixing === 'gaoji')
  const subscriptionExpired = computed(() => {
    if (!userInfo.value?.dingyue_jieshu) return false
    return new Date(userInfo.value.dingyue_jieshu) < new Date()
  })
  
  // 方法
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('access-token', newToken)
  }
  
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
    localStorage.setItem('user-info', JSON.stringify(info))
  }
  
  const setPermissions = (perms: string[]) => {
    permissions.value = perms
    localStorage.setItem('user-permissions', JSON.stringify(perms))
  }
  
  // 登录
  const login = async (loginForm: LoginForm) => {
    try {
      const response = await userApi.login(loginForm)
      const { token: accessToken, user, permissions: userPermissions } = response.data
      
      setToken(accessToken)
      setUserInfo(user)
      setPermissions(userPermissions || [])
      
      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }
  
  // 注册
  const register = async (registerForm: RegisterForm) => {
    try {
      const response = await userApi.register(registerForm)
      return response
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    }
  }
  
  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await userApi.getUserInfo()
      setUserInfo(response.data)
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }
  
  // 更新用户信息
  const updateUserInfo = async (updateData: Partial<UserInfo>) => {
    try {
      const response = await userApi.updateUserInfo(updateData)
      setUserInfo(response.data)
      return response
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }
  
  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      const response = await userApi.changePassword({
        old_password: oldPassword,
        new_password: newPassword
      })
      return response
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    }
  }
  
  // 登出
  const logout = async () => {
    try {
      // 调用登出API
      await userApi.logout()
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      userInfo.value = null
      permissions.value = []
      
      // 清除localStorage
      localStorage.removeItem('access-token')
      localStorage.removeItem('user-info')
      localStorage.removeItem('user-permissions')
      
      // 重定向到登录页
      window.location.href = '/login'
    }
  }
  
  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await userApi.refreshToken()
      setToken(response.data.token)
      return response
    } catch (error) {
      console.error('刷新token失败:', error)
      // token刷新失败，需要重新登录
      logout()
      throw error
    }
  }
  
  // 检查权限
  const hasPermission = (permission: string) => {
    return permissions.value.includes(permission)
  }
  
  // 检查多个权限（需要全部拥有）
  const hasAllPermissions = (perms: string[]) => {
    return perms.every(perm => permissions.value.includes(perm))
  }
  
  // 检查多个权限（拥有其中一个即可）
  const hasAnyPermission = (perms: string[]) => {
    return perms.some(perm => permissions.value.includes(perm))
  }
  
  // 初始化用户状态
  const initUser = () => {
    // 从localStorage恢复状态
    const savedToken = localStorage.getItem('access-token')
    const savedUserInfo = localStorage.getItem('user-info')
    const savedPermissions = localStorage.getItem('user-permissions')
    
    if (savedToken) {
      token.value = savedToken
    }
    
    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('user-info')
      }
    }
    
    if (savedPermissions) {
      try {
        permissions.value = JSON.parse(savedPermissions)
      } catch (error) {
        console.error('解析用户权限失败:', error)
        localStorage.removeItem('user-permissions')
      }
    }
  }
  
  // 重置用户状态
  const resetUser = () => {
    token.value = ''
    userInfo.value = null
    permissions.value = []
    
    localStorage.removeItem('access-token')
    localStorage.removeItem('user-info')
    localStorage.removeItem('user-permissions')
  }
  
  return {
    // 状态
    token,
    userInfo,
    permissions,
    
    // 计算属性
    isLoggedIn,
    isVip,
    subscriptionExpired,
    
    // 方法
    setToken,
    setUserInfo,
    setPermissions,
    login,
    register,
    getUserInfo,
    updateUserInfo,
    changePassword,
    logout,
    refreshToken,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    initUser,
    resetUser
  }
})

// 类型导出
export type UserStore = ReturnType<typeof useUserStore>
