#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
雪球组合交易测试脚本
探索pysnowball的真实模拟交易功能
"""

import json
import logging
import traceback
from datetime import datetime

try:
    import pysnowball as ball
    PYSNOWBALL_AVAILABLE = True
except ImportError:
    PYSNOWBALL_AVAILABLE = False
    print("⚠️ pysnowball未安装，请运行: pip install pysnowball")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class XueqiuCubeTester:
    """雪球组合交易测试器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.token_set = False
        self.test_results = {}
        
    def load_and_set_token(self):
        """从配置文件加载并设置token"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 解析cookies获取token
            cookies_str = config.get('cookies', '')
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                if PYSNOWBALL_AVAILABLE:
                    ball.set_token(token)
                    self.token_set = True
                    logger.info(f"✅ Token设置成功: u={u}")
                    return True
                else:
                    logger.error("❌ pysnowball未安装")
                    return False
            else:
                logger.error("❌ 无法从配置文件获取有效token")
                return False
                
        except Exception as e:
            logger.error(f"❌ 加载token失败: {e}")
            return False
    
    def test_watch_list(self):
        """测试获取自选股/组合列表"""
        test_name = "组合列表"
        logger.info(f"🧪 测试{test_name}")
        
        try:
            result = ball.watch_list()
            
            if result and 'data' in result:
                data = result['data']
                
                # 检查cubes（组合）
                cubes = data.get('cubes', [])
                logger.info(f"✅ {test_name}成功:")
                logger.info(f"   组合数量: {len(cubes)}")
                
                for cube in cubes:
                    logger.info(f"   组合: {cube.get('name', 'N/A')} "
                              f"ID: {cube.get('id', 'N/A')} "
                              f"股票数: {cube.get('symbol_count', 0)}")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': data,
                    'cubes': cubes
                }
                return cubes
            else:
                raise Exception("返回数据格式错误")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e)
            }
            return []
    
    def test_cube_current_position(self, cube_symbol):
        """测试获取组合当前持仓"""
        test_name = f"组合持仓-{cube_symbol}"
        logger.info(f"🧪 测试{test_name}")
        
        try:
            result = ball.rebalancing_current(cube_symbol)
            
            if result:
                logger.info(f"✅ {test_name}成功:")
                
                # 检查最后一次调仓
                last_rb = result.get('last_rb', {})
                if last_rb:
                    holdings = last_rb.get('holdings', [])
                    logger.info(f"   持仓数量: {len(holdings)}")
                    
                    for holding in holdings[:5]:  # 显示前5个持仓
                        logger.info(f"   股票: {holding.get('stock_name', 'N/A')} "
                                  f"({holding.get('stock_symbol', 'N/A')}) "
                                  f"权重: {holding.get('weight', 0):.2f}%")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': result
                }
                return result
            else:
                raise Exception("返回数据为空")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e)
            }
            return None
    
    def test_cube_history(self, cube_symbol):
        """测试获取组合调仓历史"""
        test_name = f"调仓历史-{cube_symbol}"
        logger.info(f"🧪 测试{test_name}")
        
        try:
            result = ball.rebalancing_history(cube_symbol)
            
            if result and 'list' in result:
                history_list = result['list']
                logger.info(f"✅ {test_name}成功:")
                logger.info(f"   历史记录数: {len(history_list)}")
                
                for record in history_list[:3]:  # 显示前3条记录
                    created_at = record.get('created_at', 0)
                    if created_at:
                        date_str = datetime.fromtimestamp(created_at/1000).strftime('%Y-%m-%d %H:%M')
                    else:
                        date_str = 'N/A'
                    
                    logger.info(f"   调仓时间: {date_str} "
                              f"状态: {record.get('status', 'N/A')}")
                    
                    # 显示调仓详情
                    histories = record.get('rebalancing_histories', [])
                    for hist in histories[:2]:  # 显示前2个调仓操作
                        logger.info(f"     股票: {hist.get('stock_name', 'N/A')} "
                                  f"权重: {hist.get('prev_weight', 0):.1f}% -> "
                                  f"{hist.get('target_weight', 0):.1f}%")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': result
                }
                return result
            else:
                raise Exception("返回数据格式错误")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e)
            }
            return None
    
    def test_cube_nav(self, cube_symbol):
        """测试获取组合净值"""
        test_name = f"组合净值-{cube_symbol}"
        logger.info(f"🧪 测试{test_name}")
        
        try:
            result = ball.nav_daily(cube_symbol)
            
            if result and len(result) > 0:
                cube_data = result[0]  # 第一个是组合数据
                nav_list = cube_data.get('list', [])
                
                logger.info(f"✅ {test_name}成功:")
                logger.info(f"   组合名称: {cube_data.get('name', 'N/A')}")
                logger.info(f"   净值数据点: {len(nav_list)}")
                
                if nav_list:
                    latest = nav_list[-1]
                    logger.info(f"   最新净值: {latest.get('value', 'N/A')} "
                              f"收益率: {latest.get('percent', 'N/A')}%")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': result
                }
                return result
            else:
                raise Exception("返回数据为空")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e)
            }
            return None
    
    def test_cube_quote(self, cube_symbol):
        """测试获取组合实时净值"""
        test_name = f"实时净值-{cube_symbol}"
        logger.info(f"🧪 测试{test_name}")
        
        try:
            result = ball.quote_current(cube_symbol)
            
            if result and cube_symbol in result:
                cube_quote = result[cube_symbol]
                logger.info(f"✅ {test_name}成功:")
                logger.info(f"   组合名称: {cube_quote.get('name', 'N/A')}")
                logger.info(f"   净值: {cube_quote.get('net_value', 'N/A')}")
                logger.info(f"   日收益: {cube_quote.get('daily_gain', 'N/A')}%")
                logger.info(f"   总收益: {cube_quote.get('total_gain', 'N/A')}%")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': result
                }
                return result
            else:
                raise Exception("返回数据格式错误")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e)
            }
            return None
    
    def explore_cube_apis(self):
        """探索组合相关的所有API"""
        logger.info("🔍 探索雪球组合API功能")
        logger.info("=" * 60)
        
        if not PYSNOWBALL_AVAILABLE:
            logger.error("❌ pysnowball未安装")
            return False
        
        # 设置token
        if not self.load_and_set_token():
            logger.error("❌ Token设置失败")
            return False
        
        # 1. 获取组合列表
        cubes = self.test_watch_list()
        
        if not cubes:
            logger.warning("⚠️ 没有找到组合，无法进行后续测试")
            return False
        
        # 2. 测试第一个有股票的组合
        test_cube = None
        for cube in cubes:
            if cube.get('symbol_count', 0) > 0:
                test_cube = cube
                break
        
        if not test_cube:
            logger.warning("⚠️ 没有找到包含股票的组合")
            # 尝试使用默认组合ID
            test_cube_symbol = "ZH000001"  # 尝试一个示例组合ID
        else:
            # 组合symbol通常是 ZH + cube_id
            cube_id = test_cube.get('id')
            if cube_id and cube_id > 0:
                test_cube_symbol = f"ZH{cube_id:06d}"
            else:
                test_cube_symbol = "ZH000001"
        
        logger.info(f"📊 测试组合: {test_cube_symbol}")
        logger.info("-" * 40)
        
        # 3. 测试各种组合API
        self.test_cube_current_position(test_cube_symbol)
        logger.info("-" * 40)
        
        self.test_cube_history(test_cube_symbol)
        logger.info("-" * 40)
        
        self.test_cube_nav(test_cube_symbol)
        logger.info("-" * 40)
        
        self.test_cube_quote(test_cube_symbol)
        logger.info("-" * 40)
        
        return True
    
    def save_results(self, filename='cube_api_test_results.json'):
        """保存测试结果"""
        try:
            results = {
                'timestamp': datetime.now().isoformat(),
                'test_results': self.test_results,
                'summary': {
                    'total_tests': len(self.test_results),
                    'successful_tests': len([r for r in self.test_results.values() if r['status'] == 'success']),
                    'failed_tests': len([r for r in self.test_results.values() if r['status'] == 'failed'])
                }
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📄 测试结果已保存到: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存测试结果失败: {e}")
            return False

def main():
    """主函数"""
    print("🐍 雪球组合交易API探索")
    print("基于pysnowball探索真实的雪球模拟交易功能")
    print("=" * 60)
    
    # 创建测试器
    tester = XueqiuCubeTester()
    
    try:
        # 探索组合API
        success = tester.explore_cube_apis()
        
        # 保存结果
        tester.save_results()
        
        if success:
            print("\n🎉 组合API探索完成！")
            print("📋 测试结果总结:")
            for test_name, result in tester.test_results.items():
                status_icon = "✅" if result['status'] == 'success' else "❌"
                print(f"   {status_icon} {test_name}")
        else:
            print("\n⚠️ 组合API探索遇到问题，请检查配置")
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程发生异常: {e}")
        traceback.print_exc()

if __name__ == '__main__':
    main()
