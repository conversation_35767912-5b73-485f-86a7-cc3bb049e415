// 用户服务 - 处理用户注册、登录、验证等功能

export interface User {
  id: number
  yonghuming: string
  youxiang: string
  mima: string
  dingyue_leixing: 'jiben' | 'gaoji'
  shi_huoyue: boolean
  chuangjian_shijian: string
  gengxin_shijian: string
  dingyue_kaishi: string
  dingyue_jieshu: string
}

export interface RegisterData {
  yonghuming: string
  youxiang: string
  mima: string
}

export interface LoginData {
  yonghuming: string
  mima: string
}

// 模拟用户数据库 - 实际项目中应该使用真实的数据库
class UserDatabase {
  private users: User[] = []
  private nextId = 1

  constructor() {
    // 初始化一些测试用户
    this.users = [
      {
        id: 1,
        yonghuming: 'admin',
        youxiang: '<EMAIL>',
        mima: 'admin123',
        dingyue_leixing: 'gaoji',
        shi_huoyue: true,
        chuangjian_shijian: '2024-01-01T00:00:00Z',
        gengxin_shijian: '2024-01-01T00:00:00Z',
        dingyue_kaishi: '2024-01-01',
        dingyue_jieshu: '2024-12-31'
      }
    ]
    this.nextId = 2
    
    // 从localStorage恢复用户数据
    this.loadFromStorage()
  }

  // 保存到localStorage
  private saveToStorage() {
    try {
      localStorage.setItem('users_database', JSON.stringify({
        users: this.users,
        nextId: this.nextId
      }))
    } catch (error) {
      console.error('保存用户数据失败:', error)
    }
  }

  // 从localStorage加载
  private loadFromStorage() {
    try {
      const data = localStorage.getItem('users_database')
      if (data) {
        const parsed = JSON.parse(data)
        if (parsed.users && Array.isArray(parsed.users)) {
          // 合并默认用户和存储的用户，避免重复
          const existingUsernames = this.users.map(u => u.yonghuming)
          const newUsers = parsed.users.filter((u: User) => !existingUsernames.includes(u.yonghuming))
          this.users.push(...newUsers)
          this.nextId = Math.max(this.nextId, parsed.nextId || 1)
        }
      }
    } catch (error) {
      console.error('加载用户数据失败:', error)
    }
  }

  // 检查用户名是否存在
  isUsernameExists(yonghuming: string): boolean {
    return this.users.some(user => user.yonghuming === yonghuming)
  }

  // 检查邮箱是否存在
  isEmailExists(youxiang: string): boolean {
    return this.users.some(user => user.youxiang === youxiang)
  }

  // 注册新用户
  registerUser(data: RegisterData): { success: boolean; message: string; user?: User } {
    // 检查用户名唯一性
    if (this.isUsernameExists(data.yonghuming)) {
      return { success: false, message: '用户名已存在，请选择其他用户名' }
    }

    // 检查邮箱唯一性
    if (this.isEmailExists(data.youxiang)) {
      return { success: false, message: '邮箱已被注册，请使用其他邮箱' }
    }

    // 创建新用户
    const newUser: User = {
      id: this.nextId++,
      yonghuming: data.yonghuming,
      youxiang: data.youxiang,
      mima: data.mima, // 实际项目中应该加密存储
      dingyue_leixing: 'jiben', // 新用户默认基础版
      shi_huoyue: true,
      chuangjian_shijian: new Date().toISOString(),
      gengxin_shijian: new Date().toISOString(),
      dingyue_kaishi: new Date().toISOString().split('T')[0],
      dingyue_jieshu: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 一年后过期
    }

    this.users.push(newUser)
    this.saveToStorage()

    return { 
      success: true, 
      message: '注册成功', 
      user: { ...newUser, mima: '' } // 返回时不包含密码
    }
  }

  // 用户登录验证
  loginUser(data: LoginData): { success: boolean; message: string; user?: User } {
    const user = this.users.find(u => u.yonghuming === data.yonghuming)
    
    if (!user) {
      return { success: false, message: '用户名不存在' }
    }

    if (user.mima !== data.mima) {
      return { success: false, message: '密码错误' }
    }

    if (!user.shi_huoyue) {
      return { success: false, message: '账户已被禁用，请联系管理员' }
    }

    // 更新最后登录时间
    user.gengxin_shijian = new Date().toISOString()
    this.saveToStorage()

    return { 
      success: true, 
      message: '登录成功', 
      user: { ...user, mima: '' } // 返回时不包含密码
    }
  }

  // 根据用户名获取用户信息
  getUserByUsername(yonghuming: string): User | null {
    const user = this.users.find(u => u.yonghuming === yonghuming)
    return user ? { ...user, mima: '' } : null // 不返回密码
  }

  // 获取所有用户（管理员功能）
  getAllUsers(): User[] {
    return this.users.map(user => ({ ...user, mima: '' })) // 不返回密码
  }

  // 更新用户信息
  updateUser(id: number, updates: Partial<User>): { success: boolean; message: string } {
    const userIndex = this.users.findIndex(u => u.id === id)
    
    if (userIndex === -1) {
      return { success: false, message: '用户不存在' }
    }

    // 如果更新用户名，检查唯一性
    if (updates.yonghuming && updates.yonghuming !== this.users[userIndex].yonghuming) {
      if (this.isUsernameExists(updates.yonghuming)) {
        return { success: false, message: '用户名已存在' }
      }
    }

    // 如果更新邮箱，检查唯一性
    if (updates.youxiang && updates.youxiang !== this.users[userIndex].youxiang) {
      if (this.isEmailExists(updates.youxiang)) {
        return { success: false, message: '邮箱已被使用' }
      }
    }

    // 更新用户信息
    this.users[userIndex] = {
      ...this.users[userIndex],
      ...updates,
      gengxin_shijian: new Date().toISOString()
    }

    this.saveToStorage()
    return { success: true, message: '用户信息更新成功' }
  }

  // 删除用户
  deleteUser(id: number): { success: boolean; message: string } {
    const userIndex = this.users.findIndex(u => u.id === id)
    
    if (userIndex === -1) {
      return { success: false, message: '用户不存在' }
    }

    this.users.splice(userIndex, 1)
    this.saveToStorage()
    return { success: true, message: '用户删除成功' }
  }

  // 获取用户统计信息
  getUserStats() {
    const total = this.users.length
    const active = this.users.filter(u => u.shi_huoyue).length
    const gaoji = this.users.filter(u => u.dingyue_leixing === 'gaoji').length
    const jiben = this.users.filter(u => u.dingyue_leixing === 'jiben').length

    return {
      total,
      active,
      inactive: total - active,
      gaoji,
      jiben
    }
  }
}

// 创建全局用户数据库实例
const userDB = new UserDatabase()

// 导出用户服务函数
export const userService = {
  // 注册用户
  register: async (data: RegisterData) => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 800))
    return userDB.registerUser(data)
  },

  // 用户登录
  login: async (data: LoginData) => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    return userDB.loginUser(data)
  },

  // 检查用户名是否存在
  checkUsername: async (yonghuming: string) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return userDB.isUsernameExists(yonghuming)
  },

  // 检查邮箱是否存在
  checkEmail: async (youxiang: string) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return userDB.isEmailExists(youxiang)
  },

  // 获取用户信息
  getUserInfo: async (yonghuming: string) => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return userDB.getUserByUsername(yonghuming)
  },

  // 获取所有用户（管理员功能）
  getAllUsers: async () => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return userDB.getAllUsers()
  },

  // 更新用户信息
  updateUser: async (id: number, updates: Partial<User>) => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return userDB.updateUser(id, updates)
  },

  // 删除用户
  deleteUser: async (id: number) => {
    await new Promise(resolve => setTimeout(resolve, 400))
    return userDB.deleteUser(id)
  },

  // 获取用户统计
  getUserStats: async () => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return userDB.getUserStats()
  }
}

export default userService
