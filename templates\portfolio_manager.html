<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雪球组合管理界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .nav-tab.active {
            background: white;
            border-bottom: 3px solid #FF6B6B;
            color: #FF6B6B;
        }
        
        .nav-tab:hover {
            background: #e9ecef;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #FF6B6B;
            padding-bottom: 5px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            margin: 5px;
        }
        
        .btn-primary { background: #FF6B6B; color: white; }
        .btn-success { background: #4ECDC4; color: white; }
        .btn-info { background: #45B7D1; color: white; }
        .btn-warning { background: #FFA726; color: white; }
        
        .btn:hover { opacity: 0.8; }
        
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            display: none;
        }
        
        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .portfolio-list {
            display: grid;
            gap: 15px;
        }
        
        .portfolio-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .portfolio-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .portfolio-item.selected {
            border-color: #FF6B6B;
            background: #fff5f5;
        }
        
        .portfolio-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .portfolio-code {
            color: #666;
            font-family: monospace;
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
            display: inline-block;
            margin-bottom: 8px;
        }
        
        .portfolio-stats {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            font-size: 0.9em;
        }
        
        .stat-item {
            text-align: center;
            padding: 5px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.8em;
        }
        
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        
        .guide-steps {
            counter-reset: step-counter;
        }
        
        .guide-step {
            counter-increment: step-counter;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #4ECDC4;
            position: relative;
        }
        
        .guide-step::before {
            content: counter(step-counter);
            position: absolute;
            left: -12px;
            top: 15px;
            background: #4ECDC4;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        
        .config-display {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.success { background: #28a745; }
        .status-indicator.error { background: #dc3545; }
        .status-indicator.warning { background: #ffc107; }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #FF6B6B;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 雪球组合管理界面</h1>
            <p>基于easytrader的雪球组合管理工具</p>
        </div>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">📊 概览</button>
            <button class="nav-tab" onclick="showTab('portfolios')">📋 我的组合</button>
            <button class="nav-tab" onclick="showTab('create')">➕ 创建指导</button>
            <button class="nav-tab" onclick="showTab('config')">⚙️ 配置管理</button>
        </div>
        
        <!-- 概览标签页 -->
        <div id="overview" class="tab-content active">
            <div class="card">
                <h3>📊 当前状态</h3>
                <div id="currentStatus">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>正在加载状态信息...</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>🚀 快速操作</h3>
                <button class="btn btn-primary" onclick="refreshStatus()">🔄 刷新状态</button>
                <button class="btn btn-success" onclick="showTab('portfolios')">📋 查看组合</button>
                <button class="btn btn-info" onclick="showTab('create')">➕ 创建组合</button>
                <button class="btn btn-warning" onclick="testConnection()">🧪 测试连接</button>
            </div>
            
            <div id="connectionTest" class="alert"></div>
        </div>
        
        <!-- 我的组合标签页 -->
        <div id="portfolios" class="tab-content">
            <div class="card">
                <h3>📋 我的雪球组合</h3>
                <button class="btn btn-primary" onclick="loadPortfolios()">🔄 刷新组合列表</button>
                
                <div id="portfoliosList">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>正在加载组合列表...</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>🎯 选择组合</h3>
                <div class="form-group">
                    <label for="selectedPortfolio">选中的组合代码:</label>
                    <input type="text" id="selectedPortfolio" placeholder="点击上方组合进行选择">
                </div>
                <button class="btn btn-success" onclick="useSelectedPortfolio()">✅ 使用此组合</button>
            </div>
            
            <div id="portfolioAlert" class="alert"></div>
        </div>
        
        <!-- 创建指导标签页 -->
        <div id="create" class="tab-content">
            <div class="card">
                <h3>📝 创建雪球模拟组合</h3>
                <p>如果你还没有雪球组合，请按照以下步骤创建：</p>
                
                <div id="createGuide">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>正在加载创建指导...</p>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-primary" onclick="openXueqiu()">🌐 打开雪球网站</button>
                </div>
            </div>
            
            <div class="card">
                <h3>🔍 验证组合</h3>
                <p>创建完成后，请输入组合代码进行验证：</p>
                
                <div class="form-group">
                    <label for="verifyCode">组合代码:</label>
                    <input type="text" id="verifyCode" placeholder="例如: ZH123456789">
                </div>
                
                <button class="btn btn-info" onclick="verifyPortfolio()">🧪 验证组合</button>
                
                <div id="verifyAlert" class="alert"></div>
            </div>
        </div>
        
        <!-- 配置管理标签页 -->
        <div id="config" class="tab-content">
            <div class="card">
                <h3>⚙️ 当前配置</h3>
                <div id="configDisplay">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>正在加载配置信息...</p>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>🔧 更新组合配置</h3>
                <div class="form-group">
                    <label for="newPortfolioCode">新的组合代码:</label>
                    <input type="text" id="newPortfolioCode" placeholder="例如: ZH123456789">
                </div>
                
                <button class="btn btn-success" onclick="updateConfig()">💾 更新配置</button>
                
                <div id="configAlert" class="alert"></div>
            </div>
            
            <div class="card">
                <h3>📋 配置说明</h3>
                <p><strong>组合代码格式:</strong> ZH + 数字，例如 ZH123456789</p>
                <p><strong>获取方式:</strong> 在雪球组合页面的URL中获取</p>
                <p><strong>示例URL:</strong> https://xueqiu.com/P/ZH123456789</p>
                <p><strong>注意事项:</strong> 组合必须是模拟组合类型</p>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签按钮的active类
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // 根据标签页加载相应数据
            if (tabName === 'overview') {
                loadOverview();
            } else if (tabName === 'portfolios') {
                loadPortfolios();
            } else if (tabName === 'create') {
                loadCreateGuide();
            } else if (tabName === 'config') {
                loadConfig();
            }
        }
        
        // 显示消息
        function showAlert(elementId, message, type = 'info') {
            const alertElement = document.getElementById(elementId);
            alertElement.className = `alert ${type}`;
            alertElement.textContent = message;
            alertElement.style.display = 'block';
            
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }
        
        // 加载概览
        function loadOverview() {
            fetch('/api/current_config')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('currentStatus');
                    
                    if (data.success) {
                        const config = data.config;
                        statusDiv.innerHTML = `
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div>
                                    <h4>📱 账户信息</h4>
                                    <p><span class="status-indicator ${config.account ? 'success' : 'error'}"></span>账户: ${config.account || '未设置'}</p>
                                    <p><span class="status-indicator ${config.user_id ? 'success' : 'error'}"></span>用户ID: ${config.user_id || '未获取'}</p>
                                </div>
                                <div>
                                    <h4>🎯 组合配置</h4>
                                    <p><span class="status-indicator ${config.portfolio_code && config.portfolio_code !== 'test' ? 'success' : 'warning'}"></span>组合代码: ${config.portfolio_code || '未设置'}</p>
                                    <p><span class="status-indicator ${config.cookies_status === 'valid' ? 'success' : 'error'}"></span>Cookies: ${config.cookies_status === 'valid' ? '有效' : '无效'}</p>
                                </div>
                            </div>
                        `;
                    } else {
                        statusDiv.innerHTML = `<p class="error">加载状态失败: ${data.error}</p>`;
                    }
                })
                .catch(error => {
                    document.getElementById('currentStatus').innerHTML = `<p class="error">加载失败: ${error}</p>`;
                });
        }
        
        // 加载组合列表
        function loadPortfolios() {
            const listDiv = document.getElementById('portfoliosList');
            listDiv.innerHTML = '<div class="loading"><div class="spinner"></div><p>正在加载组合列表...</p></div>';
            
            fetch('/api/portfolios')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.portfolios.length === 0) {
                            listDiv.innerHTML = `
                                <div style="text-align: center; padding: 40px; color: #666;">
                                    <h3>📭 暂无组合</h3>
                                    <p>你还没有创建任何雪球组合</p>
                                    <button class="btn btn-primary" onclick="showTab('create')">➕ 创建组合</button>
                                </div>
                            `;
                        } else {
                            let html = '<div class="portfolio-list">';
                            data.portfolios.forEach(portfolio => {
                                html += `
                                    <div class="portfolio-item" onclick="selectPortfolio('${portfolio.symbol}', '${portfolio.name}')">
                                        <div class="portfolio-name">${portfolio.name}</div>
                                        <div class="portfolio-code">${portfolio.symbol}</div>
                                        <div class="portfolio-stats">
                                            <div class="stat-item">
                                                <div class="stat-label">净值</div>
                                                <div class="stat-value">${portfolio.net_value || '--'}</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-label">收益率</div>
                                                <div class="stat-value">${portfolio.gain_percent || '--'}%</div>
                                            </div>
                                            <div class="stat-item">
                                                <div class="stat-label">关注者</div>
                                                <div class="stat-value">${portfolio.follower_count || 0}</div>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            });
                            html += '</div>';
                            listDiv.innerHTML = html;
                        }
                    } else {
                        listDiv.innerHTML = `<p class="error">加载失败: ${data.error}</p>`;
                    }
                })
                .catch(error => {
                    listDiv.innerHTML = `<p class="error">加载失败: ${error}</p>`;
                });
        }
        
        // 选择组合
        function selectPortfolio(symbol, name) {
            // 移除之前的选中状态
            document.querySelectorAll('.portfolio-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加选中状态
            event.currentTarget.classList.add('selected');
            
            // 设置选中的组合代码
            document.getElementById('selectedPortfolio').value = symbol;
            
            showAlert('portfolioAlert', `已选择组合: ${name} (${symbol})`, 'success');
        }
        
        // 使用选中的组合
        function useSelectedPortfolio() {
            const portfolioCode = document.getElementById('selectedPortfolio').value.trim();
            
            if (!portfolioCode) {
                showAlert('portfolioAlert', '请先选择一个组合', 'error');
                return;
            }
            
            // 更新配置
            fetch('/api/update_config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ portfolio_code: portfolioCode })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('portfolioAlert', `配置更新成功: ${data.message}`, 'success');
                    // 刷新概览
                    loadOverview();
                } else {
                    showAlert('portfolioAlert', `配置更新失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showAlert('portfolioAlert', `操作失败: ${error}`, 'error');
            });
        }
        
        // 加载创建指导
        function loadCreateGuide() {
            const guideDiv = document.getElementById('createGuide');
            
            fetch('/api/create_guide')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const guide = data.guide;
                        let html = '<div class="guide-steps">';
                        
                        guide.steps.forEach(step => {
                            html += `<div class="guide-step">${step}</div>`;
                        });
                        
                        html += '</div>';
                        html += '<div class="card"><h4>📝 表单字段说明</h4>';
                        
                        Object.entries(guide.form_fields).forEach(([key, value]) => {
                            html += `<p><strong>${value}</strong></p>`;
                        });
                        
                        html += '</div>';
                        guideDiv.innerHTML = html;
                    } else {
                        guideDiv.innerHTML = `<p class="error">加载失败: ${data.error}</p>`;
                    }
                })
                .catch(error => {
                    guideDiv.innerHTML = `<p class="error">加载失败: ${error}</p>`;
                });
        }
        
        // 打开雪球网站
        function openXueqiu() {
            window.open('https://xueqiu.com/cubes', '_blank');
        }
        
        // 验证组合
        function verifyPortfolio() {
            const portfolioCode = document.getElementById('verifyCode').value.trim();
            
            if (!portfolioCode) {
                showAlert('verifyAlert', '请输入组合代码', 'error');
                return;
            }
            
            fetch('/api/verify_portfolio', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ portfolio_code: portfolioCode })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('verifyAlert', `验证成功: ${data.message}`, 'success');
                    // 自动填入配置页面
                    document.getElementById('newPortfolioCode').value = portfolioCode;
                } else {
                    showAlert('verifyAlert', `验证失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showAlert('verifyAlert', `验证失败: ${error}`, 'error');
            });
        }
        
        // 加载配置
        function loadConfig() {
            const configDiv = document.getElementById('configDisplay');
            
            fetch('/api/current_config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const config = data.config;
                        configDiv.innerHTML = `
                            <div class="config-display">
                                <div><strong>账户:</strong> ${config.account}</div>
                                <div><strong>组合代码:</strong> ${config.portfolio_code}</div>
                                <div><strong>用户ID:</strong> ${config.user_id}</div>
                                <div><strong>Cookies状态:</strong> ${config.cookies_status}</div>
                            </div>
                        `;
                    } else {
                        configDiv.innerHTML = `<p class="error">加载失败: ${data.error}</p>`;
                    }
                })
                .catch(error => {
                    configDiv.innerHTML = `<p class="error">加载失败: ${error}</p>`;
                });
        }
        
        // 更新配置
        function updateConfig() {
            const portfolioCode = document.getElementById('newPortfolioCode').value.trim();
            
            if (!portfolioCode) {
                showAlert('configAlert', '请输入组合代码', 'error');
                return;
            }
            
            fetch('/api/update_config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ portfolio_code: portfolioCode })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('configAlert', `配置更新成功: ${data.message}`, 'success');
                    // 刷新配置显示
                    loadConfig();
                    // 刷新概览
                    loadOverview();
                } else {
                    showAlert('configAlert', `配置更新失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showAlert('configAlert', `操作失败: ${error}`, 'error');
            });
        }
        
        // 测试连接
        function testConnection() {
            const testDiv = document.getElementById('connectionTest');
            testDiv.innerHTML = '<div class="loading"><div class="spinner"></div><p>正在测试连接...</p></div>';
            testDiv.style.display = 'block';
            
            fetch('/api/test_connection', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const result = data.result;
                    testDiv.className = 'alert success';
                    testDiv.innerHTML = `
                        <h4>✅ 连接测试成功</h4>
                        <p>${result.message}</p>
                        <p>持仓数量: ${result.position_count}</p>
                    `;
                } else {
                    testDiv.className = 'alert error';
                    testDiv.innerHTML = `
                        <h4>❌ 连接测试失败</h4>
                        <p>${data.error}</p>
                    `;
                }
            })
            .catch(error => {
                testDiv.className = 'alert error';
                testDiv.innerHTML = `
                    <h4>❌ 连接测试失败</h4>
                    <p>${error}</p>
                `;
            });
        }
        
        // 刷新状态
        function refreshStatus() {
            loadOverview();
        }
        
        // 页面加载时初始化
        window.onload = function() {
            loadOverview();
        };
    </script>
</body>
</html>
