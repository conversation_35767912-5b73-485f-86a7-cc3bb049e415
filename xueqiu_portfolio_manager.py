#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
雪球组合管理界面
基于GitHub easytrader项目的雪球组合管理功能
"""

from flask import Flask, render_template, request, jsonify
import json
import requests
import easytrader
import logging
import traceback
from datetime import datetime

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

class XueqiuPortfolioManager:
    def __init__(self):
        self.config = self.load_config()
        self.user = None
        self.cookies = self.parse_cookies()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
        }
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"加载配置失败: {e}")
            return {}
    
    def parse_cookies(self):
        """解析cookies"""
        cookies = {}
        cookies_str = self.config.get('cookies', '')
        for cookie in cookies_str.split(';'):
            if '=' in cookie:
                key, value = cookie.strip().split('=', 1)
                cookies[key] = value
        return cookies
    
    def get_user_portfolios(self):
        """获取用户组合列表"""
        user_id = self.cookies.get('u', '')
        if not user_id:
            return []
        
        url = "https://xueqiu.com/cubes/list.json"
        params = {'user_id': user_id}
        
        try:
            response = requests.get(
                url, 
                params=params, 
                headers=self.headers, 
                cookies=self.cookies,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('list', [])
            else:
                logging.error(f"获取组合列表失败: {response.status_code}")
                return []
                
        except Exception as e:
            logging.error(f"获取组合列表异常: {e}")
            return []
    
    def create_portfolio_guide(self):
        """创建组合指导信息"""
        return {
            'steps': [
                '访问雪球网站: https://xueqiu.com',
                '登录你的雪球账户',
                '点击顶部菜单的「组合」',
                '点击「创建组合」按钮',
                '选择「模拟组合」类型',
                '填写组合信息',
                '点击「创建」完成'
            ],
            'form_fields': {
                'name': '组合名称 (如: 量化交易组合)',
                'description': '组合描述 (如: 用于量化交易测试)',
                'initial_cash': '初始资金 (建议: 1000000)',
                'benchmark': '基准指数 (建议: 沪深300)'
            },
            'create_url': 'https://xueqiu.com/cubes'
        }
    
    def verify_portfolio(self, portfolio_code):
        """验证组合是否存在"""
        url = "https://xueqiu.com/cubes/nav_daily/all.json"
        params = {'cube_symbol': portfolio_code}
        
        try:
            response = requests.get(
                url, 
                params=params, 
                headers=self.headers, 
                cookies=self.cookies,
                timeout=10
            )
            
            if response.status_code == 200:
                return True, "组合验证成功"
            elif response.status_code == 400:
                error_data = response.json()
                if error_data.get('error_code') == '20809':
                    return False, "组合不存在"
                else:
                    return False, f"验证失败: {error_data.get('error_description', '未知错误')}"
            else:
                return False, f"验证失败: HTTP {response.status_code}"
                
        except Exception as e:
            return False, f"验证异常: {str(e)}"
    
    def update_config(self, portfolio_code):
        """更新配置文件"""
        try:
            self.config['portfolio_code'] = portfolio_code
            
            with open('xq.json', 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            return True, "配置更新成功"
        except Exception as e:
            return False, f"配置更新失败: {str(e)}"
    
    def test_trading_connection(self):
        """测试交易连接"""
        try:
            # 创建雪球交易用户，添加portfolio_market参数
            user = easytrader.use('xq')
            
            # 临时修改配置以包含portfolio_market
            temp_config = self.config.copy()
            temp_config['portfolio_market'] = 'cn'  # 添加这个关键参数
            
            # 保存临时配置
            with open('temp_xq.json', 'w', encoding='utf-8') as f:
                json.dump(temp_config, f, indent=2, ensure_ascii=False)
            
            user.prepare('temp_xq.json')
            
            # 测试账户信息
            balance = user.balance
            
            # 测试持仓信息
            position = user.position
            
            # 清理临时文件
            import os
            if os.path.exists('temp_xq.json'):
                os.remove('temp_xq.json')
            
            return True, {
                'balance': balance,
                'position_count': len(position) if position else 0,
                'message': '交易连接测试成功'
            }
            
        except Exception as e:
            # 清理临时文件
            import os
            if os.path.exists('temp_xq.json'):
                os.remove('temp_xq.json')
            
            return False, f"交易连接测试失败: {str(e)}"

# 创建管理器实例
portfolio_manager = XueqiuPortfolioManager()

@app.route('/')
def index():
    """主页"""
    return render_template('portfolio_manager.html')

@app.route('/api/portfolios')
def get_portfolios():
    """获取用户组合列表"""
    try:
        portfolios = portfolio_manager.get_user_portfolios()
        return jsonify({
            'success': True,
            'portfolios': portfolios,
            'count': len(portfolios)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/create_guide')
def get_create_guide():
    """获取创建组合指导"""
    try:
        guide = portfolio_manager.create_portfolio_guide()
        return jsonify({
            'success': True,
            'guide': guide
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/verify_portfolio', methods=['POST'])
def verify_portfolio():
    """验证组合"""
    try:
        data = request.get_json()
        portfolio_code = data.get('portfolio_code', '').strip()
        
        if not portfolio_code:
            return jsonify({
                'success': False,
                'error': '组合代码不能为空'
            })
        
        success, message = portfolio_manager.verify_portfolio(portfolio_code)
        
        return jsonify({
            'success': success,
            'message': message,
            'portfolio_code': portfolio_code
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/update_config', methods=['POST'])
def update_config():
    """更新配置"""
    try:
        data = request.get_json()
        portfolio_code = data.get('portfolio_code', '').strip()
        
        if not portfolio_code:
            return jsonify({
                'success': False,
                'error': '组合代码不能为空'
            })
        
        # 先验证组合
        verify_success, verify_message = portfolio_manager.verify_portfolio(portfolio_code)
        if not verify_success:
            return jsonify({
                'success': False,
                'error': f'组合验证失败: {verify_message}'
            })
        
        # 更新配置
        update_success, update_message = portfolio_manager.update_config(portfolio_code)
        
        return jsonify({
            'success': update_success,
            'message': update_message,
            'portfolio_code': portfolio_code
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/test_connection', methods=['POST'])
def test_connection():
    """测试交易连接"""
    try:
        success, result = portfolio_manager.test_trading_connection()
        
        return jsonify({
            'success': success,
            'result': result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/current_config')
def get_current_config():
    """获取当前配置"""
    try:
        config = portfolio_manager.config
        cookies = portfolio_manager.cookies
        
        return jsonify({
            'success': True,
            'config': {
                'account': config.get('account', ''),
                'portfolio_code': config.get('portfolio_code', ''),
                'user_id': cookies.get('u', ''),
                'cookies_status': 'valid' if cookies else 'invalid'
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    print("🚀 雪球组合管理界面")
    print("访问地址: http://localhost:5002")
    print("功能: 组合查看、创建指导、配置管理")
    app.run(host='0.0.0.0', port=5002, debug=True)
