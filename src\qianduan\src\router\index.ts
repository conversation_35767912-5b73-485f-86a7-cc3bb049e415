import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/shangdian/user'
import { useAdminStore } from '@/shangdian/admin'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  easing: 'ease',
  speed: 500
})

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/yemian/denglu/DengluYemian.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/register',
    name: 'Register', 
    component: () => import('@/yemian/zhuce/ZhuceYemian.vue'),
    meta: {
      title: '用户注册',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    component: () => import('@/yemian/buju/ZhuBuju.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/yemian/yibiaopan/YibiaopanYemian.vue'),
        meta: {
          title: '仪表板',
          icon: 'DataBoard',
          keepAlive: true
        }
      },
      {
        path: 'fund-pool',
        name: 'FundPool',
        component: () => import('@/yemian/zijinchi/ZijinchiGuanmo.vue'),
        meta: {
          title: '资金池观摩',
          icon: 'Money',
          keepAlive: true,
          permission: 'fund-pool'
        }
      },
      {
        path: 'strategy',
        name: 'Strategy',
        meta: {
          title: '策略中心',
          icon: 'TrendCharts'
        },
        children: [
          {
            path: 'list',
            name: 'StrategyList',
            component: () => import('@/yemian/celue/CelueListYemian.vue'),
            meta: {
              title: '策略列表',
              keepAlive: true
            }
          },
          {
            path: 'create',
            name: 'StrategyCreate',
            component: () => import('@/yemian/celue/CelueCreateYemian.vue'),
            meta: {
              title: '创建策略'
            }
          },
          {
            path: 'template',
            name: 'StrategyTemplate',
            component: () => import('@/yemian/celue/CelueMobanYemian.vue'),
            meta: {
              title: '策略模板',
              keepAlive: true
            }
          }
        ]
      },
      {
        path: 'backtest',
        name: 'Backtest',
        meta: {
          title: '回测分析',
          icon: 'TrendCharts'
        },
        children: [
          {
            path: 'list',
            name: 'BacktestList',
            component: () => import('@/yemian/huice/HuiceListYemian.vue'),
            meta: {
              title: '回测列表',
              keepAlive: true
            }
          },
          {
            path: 'create',
            name: 'BacktestCreate',
            component: () => import('@/yemian/huice/HuiceCreateYemian.vue'),
            meta: {
              title: '创建回测'
            }
          },
          {
            path: 'result/:id',
            name: 'BacktestResult',
            component: () => import('@/yemian/huice/HuiceJieguoYemian.vue'),
            meta: {
              title: '回测结果'
            }
          }
        ]
      },
      {
        path: 'trading',
        name: 'Trading',
        meta: {
          title: '模拟交易',
          icon: 'TrendCharts'
        },
        children: [
          {
            path: 'account',
            name: 'TradingAccount',
            component: () => import('@/yemian/jiaoyi/ZhanghuYemian.vue'),
            meta: {
              title: '账户管理',
              keepAlive: true
            }
          },
          {
            path: 'position',
            name: 'TradingPosition',
            component: () => import('@/yemian/jiaoyi/ChicangYemian.vue'),
            meta: {
              title: '持仓管理',
              keepAlive: true
            }
          },
          {
            path: 'order',
            name: 'TradingOrder',
            component: () => import('@/yemian/jiaoyi/DingdanYemian.vue'),
            meta: {
              title: '订单管理',
              keepAlive: true
            }
          }
        ]
      },
      {
        path: 'data',
        name: 'Data',
        meta: {
          title: '数据中心',
          icon: 'DataAnalysis'
        },
        children: [
          {
            path: 'market',
            name: 'MarketData',
            component: () => import('@/yemian/shuju/ShichangYemian.vue'),
            meta: {
              title: '市场行情',
              keepAlive: true
            }
          },
          {
            path: 'stock',
            name: 'StockData',
            component: () => import('@/yemian/shuju/GupiaoYemian.vue'),
            meta: {
              title: '股票数据',
              keepAlive: true
            }
          },
          {
            path: 'indicator',
            name: 'IndicatorData',
            component: () => import('@/yemian/shuju/ZhibiaoYemian.vue'),
            meta: {
              title: '技术指标',
              keepAlive: true
            }
          }
        ]
      },
      {
        path: 'settings',
        name: 'Settings',
        meta: {
          title: '系统设置',
          icon: 'Setting'
        },
        children: [
          {
            path: 'profile',
            name: 'UserProfile',
            component: () => import('@/yemian/shezhi/YonghuYemian.vue'),
            meta: {
              title: '个人资料'
            }
          },
          {
            path: 'subscription',
            name: 'Subscription',
            component: () => import('@/yemian/shezhi/DingyueYemian.vue'),
            meta: {
              title: '订阅管理'
            }
          },
          {
            path: 'system',
            name: 'SystemSettings',
            component: () => import('@/yemian/shezhi/XitongYemian.vue'),
            meta: {
              title: '系统设置'
            }
          }
        ]
      }
    ]
  },
  // 管理员路由 - 使用安全路径
  {
    path: '/numendavid中国/login',
    name: 'AdminLogin',
    component: () => import('@/yemian/guanli/GuanliDenglu.vue'),
    meta: {
      title: '管理员登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/numendavid中国',
    redirect: '/numendavid中国/dashboard'
  },
  {
    path: '/numendavid中国',
    component: () => import('@/yemian/guanli/GuanliBuju.vue'),
    meta: {
      requiresAdminAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('@/yemian/guanli/GuanliYibiaopan.vue'),
        meta: {
          title: '管理员仪表板',
          icon: 'DataBoard',
          keepAlive: true
        }
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('@/yemian/guanli/YonghuGuanli.vue'),
        meta: {
          title: '用户管理',
          icon: 'User',
          permission: 'user_management'
        }
      },
      {
        path: 'system',
        name: 'SystemConfig',
        component: () => import('@/yemian/guanli/XitongPeizhi.vue'),
        meta: {
          title: '系统配置',
          icon: 'Setting',
          permission: 'system_config'
        }
      },
      {
        path: 'data',
        name: 'DataManagement',
        component: () => import('@/yemian/guanli/ShujuGuanli.vue'),
        meta: {
          title: '数据管理',
          icon: 'DataAnalysis',
          permission: 'data_management'
        }
      },
      {
        path: 'finance',
        name: 'FinanceManagement',
        component: () => import('@/yemian/guanli/CaiwuGuanli.vue'),
        meta: {
          title: '财务管理',
          icon: 'Money',
          permission: 'finance_management'
        }
      },
      {
        path: 'security',
        name: 'SecurityAudit',
        component: () => import('@/yemian/guanli/AnquanShenji.vue'),
        meta: {
          title: '安全审计',
          icon: 'Lock',
          permission: 'security_audit'
        }
      },
      {
        path: 'orders',
        name: 'OrderManagement',
        component: () => import('@/yemian/guanli/DingdanGuanli.vue'),
        meta: {
          title: '订单管理',
          icon: 'ShoppingCart',
          permission: 'order_management'
        }
      },
      {
        path: 'strategies',
        name: 'StrategyProducts',
        component: () => import('@/yemian/guanli/CelueShangpin.vue'),
        meta: {
          title: '策略商品',
          icon: 'TrendCharts',
          permission: 'strategy_management'
        }
      },
      {
        path: 'subscriptions',
        name: 'SubscriptionManagement',
        component: () => import('@/yemian/guanli/DingYueLeixing.vue'),
        meta: {
          title: '订阅类型管理',
          icon: 'CreditCard',
          permission: 'subscription_management'
        }
      },
      {
        path: 'debug',
        name: 'RouteDebug',
        component: () => import('@/yemian/guanli/LuyouCeshi.vue'),
        meta: {
          title: '路由调试',
          icon: 'Tools',
          hideInMenu: false
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/yemian/cuowu/404Yemian.vue'),
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - A股量化交易平台`
  }

  const userStore = useUserStore()
  const adminStore = useAdminStore()

  // 检查管理员认证
  if (to.meta.requiresAdminAuth) {
    if (!adminStore.isAdminLoggedIn) {
      // 管理员未登录，重定向到管理员登录页
      next({
        path: '/numendavid中国/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查管理员权限
    if (to.meta.permission && !adminStore.hasAdminPermission(to.meta.permission as any)) {
      ElMessage.error('权限不足')
      next('/numendavid中国/dashboard')
      return
    }
  }

  // 检查普通用户认证
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，重定向到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查用户信息是否完整
    if (!userStore.userInfo) {
      try {
        await userStore.getUserInfo()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        userStore.logout()
        next('/login')
        return
      }
    }
  }

  // 已登录用户访问登录页，重定向到仪表板
  if (to.path === '/login' && userStore.isLoggedIn) {
    next('/dashboard')
    return
  }

  // 已登录管理员访问管理员登录页，重定向到管理员仪表板
  if (to.path === '/numendavid中国/login' && adminStore.isAdminLoggedIn) {
    next('/numendavid中国/dashboard')
    return
  }

  next()
})

// 全局后置守卫
router.afterEach((to, from) => {
  // 结束进度条
  NProgress.done()
  
  // 添加页面访问记录
  if (import.meta.env.DEV) {
    console.log(`路由跳转: ${from.path} -> ${to.path}`)
  }
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
