from easyquant import StrategyTemplate


class Strategy(StrategyTemplate):
    name = '测试策略2'

    def strategy(self, event):
        self.log.info('\n\n策略2触发')
        # 安全地获取行情数据，避免KeyError
        if '162411' in event.data:
            self.log.info('行情数据: 华宝油气 %s' % event.data['162411'])
        else:
            # 显示可用的股票代码
            available_stocks = list(event.data.keys())
            self.log.info('当前可用股票代码: %s' % available_stocks)
            if available_stocks:
                first_stock = available_stocks[0]
                self.log.info('第一只股票行情数据: %s' % event.data[first_stock])
        self.log.info('检查持仓')
        self.log.info(self.user.balance)
        self.log.info('\n')

