// 策略服务 - 处理策略上架、收益跟踪、购买记录等

export interface StrategyPerformance {
  date: string
  daily_return: number // 日收益率 %
  cumulative_return: number // 累计收益率 %
  net_value: number // 净值
  drawdown: number // 回撤 %
  trade_count: number // 当日交易次数
  win_rate: number // 胜率 %
}

export interface StrategyTrade {
  id: string
  strategy_id: string
  symbol: string
  symbol_name: string
  direction: 'long' | 'short' // 多空方向
  entry_price: number
  exit_price?: number
  quantity: number
  entry_time: string
  exit_time?: string
  profit?: number
  profit_rate?: number
  status: 'open' | 'closed'
  trade_type: 'simulation' | 'real' // 模拟盘/实盘
}

export interface StrategyProduct {
  id: string
  creator_id: number
  creator_name: string
  name: string
  description: string
  price: number
  original_price?: number
  category: string
  tags: string[]
  
  // 策略状态
  status: 'draft' | 'pending' | 'active' | 'inactive' | 'rejected'
  trade_type: 'simulation' | 'real' | 'both' // 支持的交易类型
  
  // 上架相关
  submit_time?: string // 提交审核时间
  approve_time?: string // 审核通过时间
  online_time?: string // 上架时间
  
  // 销售数据
  sales_count: number
  rating: number
  total_revenue: number
  
  // 策略表现（用于上架审核）
  backtest_start: string // 回测开始时间
  backtest_end: string // 回测结束时间
  total_return: number // 总收益率 %
  annual_return: number // 年化收益率 %
  max_drawdown: number // 最大回撤 %
  sharpe_ratio: number // 夏普比率
  win_rate: number // 胜率 %
  trade_count: number // 总交易次数
  
  // 上架后表现
  online_return?: number // 上架后收益率 %
  online_max_drawdown?: number // 上架后最大回撤 %
  
  create_time: string
  update_time: string
}

export interface StrategyPurchase {
  id: string
  strategy_id: string
  user_id: number
  user_name: string
  purchase_price: number
  purchase_time: string
  status: 'active' | 'expired' | 'refunded'
  
  // 购买后收益跟踪
  purchase_return?: number // 购买后收益率 %
  purchase_max_drawdown?: number // 购买后最大回撤 %
}

// 模拟策略数据库
class StrategyDatabase {
  private strategies: StrategyProduct[] = []
  private performances: Map<string, StrategyPerformance[]> = new Map()
  private trades: Map<string, StrategyTrade[]> = new Map()
  private purchases: StrategyPurchase[] = []

  constructor() {
    this.initializeData()
  }

  private initializeData() {
    // 初始化策略数据
    this.strategies = [
      {
        id: 'strategy_001',
        creator_id: 1,
        creator_name: 'admin',
        name: 'AI量化趋势策略',
        description: '基于机器学习的趋势跟踪策略，通过深度学习模型识别市场趋势，适合中长线投资。该策略在过去6个月中表现稳定，最大回撤控制在15%以内。',
        price: 299,
        original_price: 399,
        category: '趋势策略',
        tags: ['AI', '机器学习', '趋势跟踪', '中长线'],
        status: 'active',
        trade_type: 'both',
        
        submit_time: '2024-07-01T10:00:00Z',
        approve_time: '2024-07-15T14:30:00Z',
        online_time: '2024-07-20T09:00:00Z',
        
        sales_count: 156,
        rating: 4.8,
        total_revenue: 46644, // 156 * 299
        
        backtest_start: '2024-01-01',
        backtest_end: '2024-06-30',
        total_return: 28.5,
        annual_return: 57.0,
        max_drawdown: 12.3,
        sharpe_ratio: 2.1,
        win_rate: 68.5,
        trade_count: 145,
        
        online_return: 15.2, // 上架后收益
        online_max_drawdown: 8.7,
        
        create_time: '2024-06-15T00:00:00Z',
        update_time: '2024-08-01T00:00:00Z'
      },
      {
        id: 'strategy_002',
        creator_id: 2,
        creator_name: 'quant_master',
        name: '均值回归套利策略',
        description: '经典的均值回归策略，通过统计套利捕捉价格偏离，风险可控，收益稳健。适合追求稳定收益的投资者。',
        price: 199,
        category: '套利策略',
        tags: ['均值回归', '套利', '稳健', '统计套利'],
        status: 'active',
        trade_type: 'simulation',
        
        submit_time: '2024-06-15T10:00:00Z',
        approve_time: '2024-07-01T14:30:00Z',
        online_time: '2024-07-05T09:00:00Z',
        
        sales_count: 89,
        rating: 4.5,
        total_revenue: 17711, // 89 * 199
        
        backtest_start: '2024-02-01',
        backtest_end: '2024-06-15',
        total_return: 18.3,
        annual_return: 42.1,
        max_drawdown: 8.9,
        sharpe_ratio: 1.8,
        win_rate: 72.1,
        trade_count: 203,
        
        online_return: 12.8,
        online_max_drawdown: 6.2,
        
        create_time: '2024-05-01T00:00:00Z',
        update_time: '2024-07-15T00:00:00Z'
      },
      {
        id: 'strategy_003',
        creator_id: 3,
        creator_name: 'trader_pro',
        name: '高频量化策略',
        description: '基于高频数据的量化策略，捕捉短期价格波动，需要较高的技术要求和资金门槛。',
        price: 599,
        category: '高频策略',
        tags: ['高频', '短线', '技术分析'],
        status: 'pending', // 待审核
        trade_type: 'real',
        
        submit_time: '2024-08-15T10:00:00Z',
        
        sales_count: 0,
        rating: 0,
        total_revenue: 0,
        
        backtest_start: '2024-06-01',
        backtest_end: '2024-08-15',
        total_return: 35.2,
        annual_return: 68.5,
        max_drawdown: 18.7, // 回撤较大，可能不符合上架要求
        sharpe_ratio: 1.9,
        win_rate: 58.3,
        trade_count: 89,
        
        create_time: '2024-08-10T00:00:00Z',
        update_time: '2024-08-15T00:00:00Z'
      }
    ]

    // 生成策略表现数据
    this.generatePerformanceData()
    
    // 生成交易记录
    this.generateTradeData()
    
    // 生成购买记录
    this.generatePurchaseData()
  }

  private generatePerformanceData() {
    this.strategies.forEach(strategy => {
      const performances: StrategyPerformance[] = []
      const startDate = new Date(strategy.backtest_start)
      const endDate = new Date()
      
      let cumulativeReturn = 0
      let netValue = 1
      let maxNetValue = 1
      
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        // 模拟日收益率（基于策略年化收益率）
        const dailyReturn = (Math.random() - 0.4) * 0.02 // -0.8% 到 1.2%
        cumulativeReturn += dailyReturn
        netValue = 1 + cumulativeReturn / 100
        maxNetValue = Math.max(maxNetValue, netValue)
        
        const drawdown = (maxNetValue - netValue) / maxNetValue * 100
        
        performances.push({
          date: d.toISOString().split('T')[0],
          daily_return: Math.round(dailyReturn * 100) / 100,
          cumulative_return: Math.round(cumulativeReturn * 100) / 100,
          net_value: Math.round(netValue * 10000) / 10000,
          drawdown: Math.round(drawdown * 100) / 100,
          trade_count: Math.floor(Math.random() * 5),
          win_rate: 60 + Math.random() * 20
        })
      }
      
      this.performances.set(strategy.id, performances)
    })
  }

  private generateTradeData() {
    this.strategies.forEach(strategy => {
      const trades: StrategyTrade[] = []
      const symbols = ['000001', '000002', '600036', '600519', '000858']
      const symbolNames = ['平安银行', '万科A', '招商银行', '贵州茅台', '五粮液']
      
      for (let i = 0; i < strategy.trade_count; i++) {
        const symbolIndex = Math.floor(Math.random() * symbols.length)
        const entryTime = new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000)
        const exitTime = new Date(entryTime.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000)
        
        const entryPrice = 10 + Math.random() * 90
        const exitPrice = entryPrice * (1 + (Math.random() - 0.4) * 0.1)
        const quantity = Math.floor(Math.random() * 1000) + 100
        
        const profit = (exitPrice - entryPrice) * quantity
        const profitRate = (exitPrice - entryPrice) / entryPrice * 100
        
        trades.push({
          id: `trade_${strategy.id}_${i}`,
          strategy_id: strategy.id,
          symbol: symbols[symbolIndex],
          symbol_name: symbolNames[symbolIndex],
          direction: Math.random() > 0.5 ? 'long' : 'short',
          entry_price: Math.round(entryPrice * 100) / 100,
          exit_price: Math.round(exitPrice * 100) / 100,
          quantity,
          entry_time: entryTime.toISOString(),
          exit_time: exitTime.toISOString(),
          profit: Math.round(profit * 100) / 100,
          profit_rate: Math.round(profitRate * 100) / 100,
          status: 'closed',
          trade_type: strategy.trade_type === 'both' ? 
            (Math.random() > 0.5 ? 'simulation' : 'real') : 
            strategy.trade_type as 'simulation' | 'real'
        })
      }
      
      this.trades.set(strategy.id, trades)
    })
  }

  private generatePurchaseData() {
    // 为已上架的策略生成购买记录
    this.strategies.filter(s => s.status === 'active').forEach(strategy => {
      for (let i = 0; i < strategy.sales_count; i++) {
        const purchaseTime = new Date(
          new Date(strategy.online_time!).getTime() + 
          Math.random() * (Date.now() - new Date(strategy.online_time!).getTime())
        )
        
        // 计算购买后收益（基于购买时间到现在的策略表现）
        const purchaseReturn = Math.random() * 20 - 5 // -5% 到 15%
        const purchaseMaxDrawdown = Math.random() * 10 // 0% 到 10%
        
        this.purchases.push({
          id: `purchase_${strategy.id}_${i}`,
          strategy_id: strategy.id,
          user_id: Math.floor(Math.random() * 100) + 1,
          user_name: `user${Math.floor(Math.random() * 100) + 1}`,
          purchase_price: strategy.price,
          purchase_time: purchaseTime.toISOString(),
          status: 'active',
          purchase_return: Math.round(purchaseReturn * 100) / 100,
          purchase_max_drawdown: Math.round(purchaseMaxDrawdown * 100) / 100
        })
      }
    })
  }

  // 获取策略列表
  getStrategies(status?: string): StrategyProduct[] {
    if (status) {
      return this.strategies.filter(s => s.status === status)
    }
    return this.strategies
  }

  // 获取策略详情
  getStrategyById(id: string): StrategyProduct | null {
    return this.strategies.find(s => s.id === id) || null
  }

  // 获取策略表现数据
  getStrategyPerformance(strategyId: string, days?: number): StrategyPerformance[] {
    const performances = this.performances.get(strategyId) || []
    if (days) {
      return performances.slice(-days)
    }
    return performances
  }

  // 获取策略交易记录
  getStrategyTrades(strategyId: string): StrategyTrade[] {
    return this.trades.get(strategyId) || []
  }

  // 获取策略购买记录
  getStrategyPurchases(strategyId: string): StrategyPurchase[] {
    return this.purchases.filter(p => p.strategy_id === strategyId)
  }

  // 检查策略是否符合上架条件
  checkListingEligibility(strategyId: string): {
    eligible: boolean
    reasons: string[]
  } {
    const strategy = this.getStrategyById(strategyId)
    if (!strategy) {
      return { eligible: false, reasons: ['策略不存在'] }
    }

    const reasons: string[] = []
    
    // 检查盈利时长（至少1个月）
    const backtestDays = Math.floor(
      (new Date(strategy.backtest_end).getTime() - new Date(strategy.backtest_start).getTime()) 
      / (1000 * 60 * 60 * 24)
    )
    if (backtestDays < 30) {
      reasons.push('回测时长不足1个月')
    }
    
    // 检查总收益率
    if (strategy.total_return <= 0) {
      reasons.push('总收益率必须为正')
    }
    
    // 检查最大回撤
    if (strategy.max_drawdown > 20) {
      reasons.push('最大回撤超过20%')
    }
    
    // 检查交易次数
    if (strategy.trade_count < 10) {
      reasons.push('交易次数不足10笔')
    }
    
    // 检查夏普比率
    if (strategy.sharpe_ratio < 1.0) {
      reasons.push('夏普比率低于1.0')
    }

    return {
      eligible: reasons.length === 0,
      reasons
    }
  }

  // 审核策略上架
  approveStrategy(strategyId: string, approved: boolean, reason?: string): boolean {
    const strategy = this.strategies.find(s => s.id === strategyId)
    if (!strategy) return false

    if (approved) {
      strategy.status = 'active'
      strategy.approve_time = new Date().toISOString()
      strategy.online_time = new Date().toISOString()
    } else {
      strategy.status = 'rejected'
    }

    return true
  }

  // 获取策略统计
  getStrategyStats() {
    const total = this.strategies.length
    const active = this.strategies.filter(s => s.status === 'active').length
    const pending = this.strategies.filter(s => s.status === 'pending').length
    const totalRevenue = this.strategies.reduce((sum, s) => sum + s.total_revenue, 0)
    const totalSales = this.strategies.reduce((sum, s) => sum + s.sales_count, 0)

    return {
      total,
      active,
      pending,
      rejected: this.strategies.filter(s => s.status === 'rejected').length,
      totalRevenue,
      totalSales,
      averagePrice: totalSales > 0 ? totalRevenue / totalSales : 0
    }
  }
}

// 创建全局策略数据库实例
const strategyDB = new StrategyDatabase()

// 导出策略服务
export const strategyService = {
  // 获取策略列表
  getStrategies: async (status?: string) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return strategyDB.getStrategies(status)
  },

  // 获取策略详情
  getStrategyDetail: async (strategyId: string) => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return strategyDB.getStrategyById(strategyId)
  },

  // 获取策略表现
  getStrategyPerformance: async (strategyId: string, days?: number) => {
    await new Promise(resolve => setTimeout(resolve, 400))
    return strategyDB.getStrategyPerformance(strategyId, days)
  },

  // 获取策略交易记录
  getStrategyTrades: async (strategyId: string) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return strategyDB.getStrategyTrades(strategyId)
  },

  // 获取策略购买记录
  getStrategyPurchases: async (strategyId: string) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return strategyDB.getStrategyPurchases(strategyId)
  },

  // 检查上架资格
  checkListingEligibility: async (strategyId: string) => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return strategyDB.checkListingEligibility(strategyId)
  },

  // 审核策略
  approveStrategy: async (strategyId: string, approved: boolean, reason?: string) => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return strategyDB.approveStrategy(strategyId, approved, reason)
  },

  // 获取统计数据
  getStrategyStats: async () => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return strategyDB.getStrategyStats()
  }
}

export default strategyService
