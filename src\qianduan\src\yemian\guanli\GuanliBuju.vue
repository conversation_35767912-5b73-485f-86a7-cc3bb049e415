<template>
  <div class="admin-layout-container">
    <!-- 管理员侧边栏 -->
    <el-aside 
      :width="adminSidebarWidth" 
      class="admin-sidebar"
      :class="{ 'is-collapsed': appStore.sidebarCollapsed }"
    >
      <div class="admin-sidebar-header">
        <div class="admin-logo">
          <el-icon class="logo-icon"><Setting /></el-icon>
          <span v-show="!appStore.sidebarCollapsed" class="logo-text">管理后台</span>
        </div>
      </div>
      
      <el-scrollbar class="admin-menu-wrapper">
        <el-menu
          :default-active="activeMenu"
          :collapse="appStore.sidebarCollapsed"
          :unique-opened="true"
          router
          class="admin-menu"
          background-color="#1f2937"
          text-color="#d1d5db"
          active-text-color="#f59e0b"
        >
          <el-menu-item index="/numendavid中国/dashboard">
            <el-icon><DataBoard /></el-icon>
            <span>管理仪表板</span>
          </el-menu-item>

          <el-menu-item
            index="/numendavid中国/users"
            v-if="adminStore.hasAdminPermission('user_management')"
          >
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>

          <el-menu-item
            index="/numendavid中国/system"
            v-if="adminStore.hasAdminPermission('system_config')"
          >
            <el-icon><Setting /></el-icon>
            <span>系统配置</span>
          </el-menu-item>

          <el-menu-item
            index="/numendavid中国/data"
            v-if="adminStore.hasAdminPermission('data_management')"
          >
            <el-icon><DataAnalysis /></el-icon>
            <span>数据管理</span>
          </el-menu-item>

          <el-menu-item
            index="/numendavid中国/security"
            v-if="adminStore.hasAdminPermission('security_audit')"
          >
            <el-icon><Lock /></el-icon>
            <span>安全审计</span>
          </el-menu-item>

          <el-menu-item
            index="/numendavid中国/orders"
            v-if="adminStore.hasAdminPermission('order_management')"
          >
            <el-icon><ShoppingCart /></el-icon>
            <span>订单管理</span>
          </el-menu-item>

          <el-menu-item
            index="/numendavid中国/strategies"
            v-if="adminStore.hasAdminPermission('strategy_management')"
          >
            <el-icon><TrendCharts /></el-icon>
            <span>策略商品</span>
          </el-menu-item>

          <el-menu-item
            index="/numendavid中国/subscriptions"
            v-if="adminStore.hasAdminPermission('subscription_management')"
          >
            <el-icon><CreditCard /></el-icon>
            <span>订阅类型管理</span>
          </el-menu-item>

          <el-menu-item
            index="/numendavid中国/finance"
            v-if="adminStore.hasAdminPermission('finance_management')"
          >
            <el-icon><Money /></el-icon>
            <span>财务管理</span>
          </el-menu-item>
        </el-menu>
      </el-scrollbar>
    </el-aside>
    
    <!-- 管理员主内容区 -->
    <el-container class="admin-main">
      <!-- 管理员顶部导航栏 -->
      <el-header class="admin-header">
        <div class="admin-header-left">
          <el-button
            type="text"
            @click="appStore.toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon><Expand v-if="appStore.sidebarCollapsed" /><Fold v-else /></el-icon>
          </el-button>
          
          <!-- 管理员面包屑导航 -->
          <el-breadcrumb separator="/" class="admin-breadcrumb">
            <el-breadcrumb-item to="/numendavid中国/dashboard">管理后台</el-breadcrumb-item>
            <el-breadcrumb-item
              v-for="item in adminBreadcrumbList"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="admin-header-right">
          <!-- 系统状态指示器 -->
          <div class="system-status">
            <el-icon class="status-icon" :class="systemStatusClass">
              <CircleCheckFilled v-if="systemStatus === 'good'" />
              <WarningFilled v-else-if="systemStatus === 'warning'" />
              <CircleCloseFilled v-else />
            </el-icon>
            <span class="status-text">{{ systemStatusText }}</span>
          </div>
          
          <!-- 管理员用户菜单 -->
          <el-dropdown @command="handleAdminCommand" class="admin-user-dropdown">
            <div class="admin-user-info">
              <el-avatar :size="32" class="admin-avatar">
                <el-icon><UserFilled /></el-icon>
              </el-avatar>
              <span class="admin-name">{{ adminStore.adminInfo?.guanli_yonghuming || '管理员' }}</span>
              <el-tag 
                :type="adminStore.isSuperAdmin ? 'danger' : 'warning'" 
                size="small"
                class="admin-role-tag"
              >
                {{ adminStore.isSuperAdmin ? '超级管理员' : '管理员' }}
              </el-tag>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>管理员资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>系统设置
                </el-dropdown-item>
                <el-dropdown-item command="user-portal">
                  <el-icon><Switch /></el-icon>切换到用户端
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <!-- 管理员主要内容区域 -->
      <el-main class="admin-content">
        <router-view v-slot="{ Component, route }">
          <transition name="admin-fade-slide" mode="out-in">
            <keep-alive :include="adminKeepAliveComponents">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/shangdian/app'
import { useAdminStore } from '@/shangdian/admin'
import { ElMessage, ElMessageBox } from 'element-plus'

// 使用状态管理
const appStore = useAppStore()
const adminStore = useAdminStore()
const route = useRoute()
const router = useRouter()

// 计算属性
const adminSidebarWidth = computed(() => appStore.sidebarCollapsed ? '64px' : '240px')

const activeMenu = computed(() => {
  return route.path
})

// 管理员面包屑导航
const adminBreadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title && item.path !== '/numendavid中国')
  return matched.map(item => ({
    title: item.meta?.title as string,
    path: item.path
  }))
})

// 管理员缓存组件
const adminKeepAliveComponents = computed(() => {
  return appStore.keepAliveComponents.filter(name => name.startsWith('Admin'))
})

// 系统状态
const systemStatus = computed(() => {
  // 这里可以根据实际系统状态返回
  return 'good' // 'good' | 'warning' | 'error'
})

const systemStatusClass = computed(() => ({
  'status-good': systemStatus.value === 'good',
  'status-warning': systemStatus.value === 'warning',
  'status-error': systemStatus.value === 'error'
}))

const systemStatusText = computed(() => {
  const statusMap = {
    good: '系统正常',
    warning: '系统警告',
    error: '系统异常'
  }
  return statusMap[systemStatus.value]
})

// 方法
const handleAdminCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('管理员资料功能开发中')
      break
    case 'settings':
      router.push('/numendavid中国/system')
      break
    case 'user-portal':
      // 切换到用户端
      router.push('/dashboard')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出管理员登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await adminStore.adminLogout()
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}

// 监听路由变化，更新keep-alive组件
watch(
  () => route.name,
  (newName) => {
    if (newName && route.meta?.keepAlive && typeof newName === 'string' && newName.startsWith('Admin')) {
      appStore.addKeepAliveComponent(newName)
    }
  },
  { immediate: true }
)

// 组件挂载时初始化
onMounted(() => {
  // 初始化管理员状态
  adminStore.initAdmin()
})
</script>

<style lang="scss" scoped>
.admin-layout-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background: #f3f4f6;
}

.admin-sidebar {
  background: #1f2937;
  border-right: 1px solid #374151;
  transition: width 0.3s ease;
  
  .admin-sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #374151;
    
    .admin-logo {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: #f59e0b;
      
      .logo-icon {
        font-size: 24px;
        margin-right: 8px;
      }
      
      .logo-text {
        transition: opacity 0.3s ease;
      }
    }
  }
  
  .admin-menu-wrapper {
    height: calc(100vh - 60px);
  }
  
  .admin-menu {
    border-right: none;
    
    :deep(.el-menu-item) {
      height: 48px;
      line-height: 48px;
      margin: 2px 8px;
      border-radius: 6px;
      
      &:hover {
        background-color: #374151 !important;
      }
      
      &.is-active {
        background-color: #f59e0b !important;
        color: #1f2937 !important;
        font-weight: 600;
      }
    }
  }
}

.admin-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.admin-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 60px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  .admin-header-left {
    display: flex;
    align-items: center;
    
    .sidebar-toggle {
      margin-right: 16px;
      font-size: 18px;
      color: #6b7280;
      
      &:hover {
        color: #f59e0b;
      }
    }
    
    .admin-breadcrumb {
      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          color: #6b7280;
          
          &:hover {
            color: #f59e0b;
          }
        }
        
        &:last-child .el-breadcrumb__inner {
          color: #1f2937;
          font-weight: 500;
        }
      }
    }
  }
  
  .admin-header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .system-status {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      
      .status-icon {
        &.status-good {
          color: #10b981;
        }
        
        &.status-warning {
          color: #f59e0b;
        }
        
        &.status-error {
          color: #ef4444;
        }
      }
      
      .status-text {
        color: #6b7280;
      }
    }
    
    .admin-user-dropdown {
      cursor: pointer;
    }
    
    .admin-user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.3s ease;
      
      &:hover {
        background-color: #f9fafb;
      }
      
      .admin-name {
        font-size: 14px;
        font-weight: 500;
        color: #1f2937;
      }
      
      .admin-role-tag {
        font-size: 10px;
      }
      
      .dropdown-icon {
        font-size: 12px;
        color: #6b7280;
        transition: transform 0.3s ease;
      }
    }
  }
}

.admin-content {
  background: #f9fafb;
  padding: 16px;
  overflow: auto;
}

// 管理员页面切换动画
.admin-fade-slide-enter-active,
.admin-fade-slide-leave-active {
  transition: all 0.3s ease;
}

.admin-fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.admin-fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 响应式设计
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    height: 100vh;
    
    &:not(.is-collapsed) {
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    }
  }
  
  .admin-main {
    margin-left: 0;
  }
  
  .admin-header {
    padding: 0 12px;
  }
  
  .admin-content {
    padding: 12px;
  }
}
</style>
