# 数据源状态检测系统说明

## 🚀 系统概述

本系统实现了完整的真实数据源状态检测机制，支持动态检测、配置管理、数据同步等功能。所有功能都基于真实的API调用，不使用模拟数据。

## 📋 主要功能

### 1. **动态状态检测**
- ✅ 实时检测数据源连接状态
- ✅ 自动定期检查（每30秒）
- ✅ 手动触发状态检查
- ✅ 响应时间监控
- ✅ 成功率统计

### 2. **数据源配置管理**
- ✅ 添加/编辑/删除数据源配置
- ✅ API密钥安全管理
- ✅ 连接参数配置（超时、重试等）
- ✅ 连接测试功能
- ✅ 配置持久化存储

### 3. **数据同步管理**
- ✅ 单个数据源同步
- ✅ 批量数据源同步
- ✅ 同步任务进度跟踪
- ✅ 同步历史记录
- ✅ 错误处理和重试

### 4. **实时监控**
- ✅ 数据源状态实时显示
- ✅ 错误信息详细展示
- ✅ 数据量统计
- ✅ 最后同步时间跟踪

## 🔧 支持的数据源

### 1. **TuShare**
- **类型**: `tushare`
- **默认端点**: `http://api.tushare.pro`
- **认证**: API Token
- **获取Token**: [TuShare注册页面](https://tushare.pro/register)

### 2. **东方财富**
- **类型**: `eastmoney`
- **默认端点**: `http://push2.eastmoney.com`
- **认证**: 无需认证
- **数据格式**: JSONP

### 3. **新浪财经**
- **类型**: `sina`
- **默认端点**: `http://hq.sinajs.cn`
- **认证**: 无需认证
- **数据格式**: JavaScript变量

### 4. **自定义数据源**
- **类型**: `custom`
- **端点**: 用户自定义
- **认证**: 可选

## 🛠️ 使用方法

### 1. **配置数据源**

1. 点击"数据源配置"按钮
2. 填写数据源信息：
   - 名称：数据源显示名称
   - 类型：选择数据源类型
   - API端点：数据源API地址
   - API密钥：如果需要（如TuShare）
   - 超时时间：请求超时时间（毫秒）
   - 重试次数：失败重试次数
   - 优先级：数字越小优先级越高
3. 点击"测试连接"验证配置
4. 保存配置

### 2. **检查数据源状态**

#### 自动检查
- 系统每30秒自动检查所有启用的数据源
- 页面加载时立即执行一次检查

#### 手动检查
- 点击"检查状态"按钮检查所有数据源
- 点击单个数据源的"检查"按钮检查特定数据源

### 3. **数据同步**

#### 同步所有数据源
```javascript
// 点击"同步数据"按钮
// 系统会同步所有在线状态的数据源
```

#### 同步单个数据源
```javascript
// 点击数据源行的"同步"按钮
// 只同步该数据源的数据
```

### 4. **监控和管理**

#### 状态监控
- **在线**: 绿色标签，数据源连接正常
- **离线**: 红色标签，数据源连接失败
- **检查中**: 黄色标签，正在检查连接状态
- **错误**: 红色标签，检查过程中出现错误

#### 性能指标
- **响应时间**: API请求的响应时间（毫秒）
- **成功率**: 连接成功的百分比
- **数据量**: 已获取的数据条数
- **最后同步**: 最近一次成功同步的时间

## 🔐 安全配置

### 1. **环境变量配置**

创建 `.env.local` 文件：
```bash
# TuShare API Token
VITE_TUSHARE_TOKEN=your_actual_token_here

# API超时配置
VITE_API_TIMEOUT=10000

# 同步间隔配置
VITE_SYNC_INTERVAL=300000
VITE_STATUS_CHECK_INTERVAL=30000
```

### 2. **API密钥管理**
- API密钥使用密码输入框，支持显示/隐藏
- 密钥存储在localStorage中（生产环境建议使用更安全的存储方式）
- 支持密钥验证和连接测试

## 📊 数据持久化

### 1. **配置存储**
- 数据源配置存储在 `localStorage` 的 `datasource_configs` 键中
- 包含完整的配置信息：端点、密钥、超时等

### 2. **状态存储**
- 数据源状态存储在 `localStorage` 的 `datasource_statuses` 键中
- 包含连接状态、统计信息、错误信息等

### 3. **数据恢复**
- 页面刷新后自动恢复所有配置和状态
- 服务器重启后配置保持不变

## 🚨 错误处理

### 1. **连接错误**
- 网络超时：显示"连接超时"错误
- API密钥无效：显示"API密钥无效"错误
- 服务器错误：显示具体的HTTP错误信息

### 2. **数据格式错误**
- 返回数据格式不正确：显示"数据格式错误"
- 空数据：显示"返回数据为空"

### 3. **系统错误**
- 配置保存失败：显示"配置保存失败"
- 同步任务启动失败：显示"同步启动失败"

## 🔄 定时任务

### 1. **状态检查任务**
- **频率**: 每30秒执行一次
- **范围**: 所有启用的数据源
- **自动启动**: 页面加载时自动启动
- **自动停止**: 页面卸载时自动停止

### 2. **数据同步任务**
- **触发方式**: 手动触发
- **进度跟踪**: 实时显示同步进度
- **状态更新**: 同步完成后更新数据统计

## 📈 性能优化

### 1. **并发控制**
- 状态检查使用 `Promise.allSettled` 并发执行
- 避免阻塞UI线程

### 2. **缓存机制**
- 配置信息缓存在内存中
- 减少重复的localStorage读取

### 3. **错误恢复**
- 自动重试机制
- 失败后不影响其他数据源的检查

## 🎯 最佳实践

### 1. **配置建议**
- TuShare超时时间建议设置为10秒
- 东方财富和新浪财经建议设置为5秒
- 重试次数建议设置为2-3次

### 2. **监控建议**
- 定期检查数据源状态
- 关注成功率低于90%的数据源
- 及时处理连接错误

### 3. **维护建议**
- 定期更新API密钥
- 监控数据源API的变化
- 备份重要的配置信息

## 🔮 未来扩展

### 1. **计划功能**
- [ ] 数据源负载均衡
- [ ] 智能故障转移
- [ ] 数据质量监控
- [ ] 告警通知系统

### 2. **技术改进**
- [ ] 使用WebSocket实现实时状态推送
- [ ] 集成更多数据源
- [ ] 支持数据源集群
- [ ] 添加数据源性能分析

---

## 📞 技术支持

如果您在使用过程中遇到问题，请检查：

1. **网络连接**: 确保能够访问数据源API
2. **API密钥**: 确保密钥有效且有足够权限
3. **防火墙设置**: 确保没有阻止API请求
4. **浏览器控制台**: 查看详细的错误信息

**系统现已完全实现真实的数据源状态检测，所有功能都基于实际的API调用！** 🎉
