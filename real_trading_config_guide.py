#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa真实交易配置指南
展示如何配置各种真实交易接口
"""

# 真实交易接口配置示例

# ==================== A股真实交易配置 ====================

# 1. 中泰XTP配置示例
XTP_SETTING = {
    "用户名": "your_username",
    "密码": "your_password", 
    "客户号": 123456789,
    "交易服务器": "120.27.164.138:6001",  # 生产环境
    "行情服务器": "120.27.164.138:6002",  # 生产环境
    "产品信息": "vnpy_2.0.0",
    "授权码": "your_auth_code"
}

# 2. 华鑫奇点TORA配置示例  
TORA_SETTING = {
    "用户名": "your_username",
    "密码": "your_password",
    "交易服务器": "tcp://210.14.72.17:4400",  # 生产环境
    "行情服务器": "tcp://210.14.72.17:4402",  # 生产环境
    "产品信息": "vnpy",
    "授权码": "your_auth_code"
}

# 3. 东方证券OST配置示例
OST_SETTING = {
    "用户名": "your_username", 
    "密码": "your_password",
    "交易服务器": "tcp://122.112.139.0:7708",  # 生产环境
    "行情服务器": "tcp://122.112.139.0:7709",  # 生产环境
    "产品信息": "vnpy"
}

# ==================== 期货真实交易配置 ====================

# CTP期货配置示例
CTP_SETTING = {
    "用户名": "your_username",
    "密码": "your_password", 
    "经纪商代码": "9999",  # 期货公司代码
    "交易服务器": "tcp://180.168.146.187:10130",  # 生产环境
    "行情服务器": "tcp://180.168.146.187:10131",  # 生产环境
    "产品信息": "vnpy_2.0.0",
    "授权编码": "0000000000000000"
}

# ==================== 海外市场配置 ====================

# 盈透证券IB配置示例
IB_SETTING = {
    "TWS地址": "127.0.0.1",
    "TWS端口": 7497,  # 真实交易端口 (模拟: 7497, 真实: 7496)
    "客户号": 123456789
}

def print_real_trading_guide():
    """打印真实交易配置指南"""
    
    print("🏦 VeighNa真实交易配置指南")
    print("=" * 80)
    
    print("\n📊 A股真实交易步骤:")
    print("1. 选择券商并开户 (中泰/华鑫/东方证券/东方财富)")
    print("2. 申请API接口权限")
    print("3. 获取交易账号和授权码")
    print("4. 在VeighNa中配置接口参数")
    print("5. 连接并开始真实交易")
    
    print("\n📈 期货真实交易步骤:")
    print("1. 选择期货公司开户")
    print("2. 申请CTP接口权限")
    print("3. 获取交易账号和经纪商代码")
    print("4. 配置CTP接口参数")
    print("5. 连接并开始交易")
    
    print("\n🌍 海外市场交易步骤:")
    print("1. 在盈透证券开户")
    print("2. 下载并安装TWS交易软件")
    print("3. 启用API接口")
    print("4. 配置IB接口参数")
    print("5. 连接并交易全球市场")
    
    print("\n⚠️ 重要提醒:")
    print("• 真实交易前务必充分测试")
    print("• 设置合理的风险控制参数")
    print("• 妥善保管账户信息和密钥")
    print("• 遵守相关法律法规")
    
    print("\n🔧 在VeighNa中配置:")
    print("1. 启动VeighNa")
    print("2. 点击 '系统' -> '连接管理'")
    print("3. 选择对应的交易接口")
    print("4. 填入配置参数")
    print("5. 点击连接")

def show_broker_comparison():
    """显示券商接口对比"""
    
    print("\n🏦 主要券商接口对比")
    print("=" * 80)
    
    brokers = [
        {
            "券商": "中泰证券",
            "接口": "XTP", 
            "支持品种": "A股、ETF、期权",
            "特点": "功能全面、稳定性好",
            "适合": "专业量化交易"
        },
        {
            "券商": "华鑫证券", 
            "接口": "TORA奇点",
            "支持品种": "A股、ETF、期权",
            "特点": "低延时、高频交易",
            "适合": "高频策略"
        },
        {
            "券商": "东方证券",
            "接口": "OST",
            "支持品种": "A股、ETF", 
            "特点": "接入简单",
            "适合": "中小投资者"
        },
        {
            "券商": "东方财富",
            "接口": "EMT",
            "支持品种": "A股、ETF",
            "特点": "用户基数大",
            "适合": "个人投资者"
        }
    ]
    
    for broker in brokers:
        print(f"\n📋 {broker['券商']} ({broker['接口']})")
        print(f"   支持品种: {broker['支持品种']}")
        print(f"   特点: {broker['特点']}")
        print(f"   适合: {broker['适合']}")

def show_risk_warnings():
    """显示风险提醒"""
    
    print("\n⚠️ 真实交易风险提醒")
    print("=" * 80)
    
    warnings = [
        "💰 资金风险: 真实交易涉及真实资金，可能造成损失",
        "🔧 技术风险: 策略bug、网络中断等可能导致异常交易", 
        "📊 市场风险: 市场波动可能超出策略预期",
        "⚖️ 合规风险: 需遵守证监会等监管机构规定",
        "🔐 安全风险: 账户信息泄露可能导致资金损失"
    ]
    
    for warning in warnings:
        print(f"   {warning}")
    
    print("\n🛡️ 风险控制建议:")
    print("   1. 从小资金开始测试")
    print("   2. 设置止损止盈")
    print("   3. 限制单笔交易金额")
    print("   4. 定期检查策略表现")
    print("   5. 保持策略多样化")

if __name__ == "__main__":
    print_real_trading_guide()
    show_broker_comparison()
    show_risk_warnings()
