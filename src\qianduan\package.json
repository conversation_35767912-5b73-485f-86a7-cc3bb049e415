{"name": "quantitative-trading-frontend", "version": "0.1.0", "description": "A股量化交易平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0"}, "devDependencies": {"@types/node": "^20.9.0", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "sass": "^1.69.5", "sass-embedded": "^1.69.5", "typescript": "~5.2.2", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.0", "vue-tsc": "^1.8.22"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}