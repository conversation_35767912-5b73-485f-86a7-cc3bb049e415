<template>
  <el-dialog
    v-model="visible"
    title="订单详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="order-detail-container" v-loading="loading">
      <div v-if="orderDetail" class="order-content">
        <!-- 订单基本信息 -->
        <div class="order-basic-info">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>订单号：</label>
                <span class="order-id">{{ orderDetail.id }}</span>
                <el-button type="primary" size="small" @click="copyOrderId">复制</el-button>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>订单状态：</label>
                <el-tag :type="getStatusTagType(orderDetail.status)">
                  {{ getStatusText(orderDetail.status) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>用户：</label>
                <span>{{ orderDetail.user_name }} (ID: {{ orderDetail.user_id }})</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>订单类型：</label>
                <el-tag :type="getOrderTypeTagType(orderDetail.order_type)" size="small">
                  {{ getOrderTypeText(orderDetail.order_type) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="info-item">
                <label>商品/服务：</label>
                <span class="product-title">{{ orderDetail.title }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="info-item">
                <label>描述：</label>
                <span>{{ orderDetail.description }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 金额信息 -->
        <div class="amount-info">
          <h3>金额信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="amount-item">
                <label>订单金额：</label>
                <span class="amount-value">¥{{ orderDetail.amount.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="orderDetail.original_amount">
              <div class="amount-item">
                <label>原价：</label>
                <span class="original-amount">¥{{ orderDetail.original_amount.toFixed(2) }}</span>
              </div>
            </el-col>
            <el-col :span="8" v-if="orderDetail.discount_amount">
              <div class="amount-item">
                <label>优惠金额：</label>
                <span class="discount-amount">-¥{{ orderDetail.discount_amount.toFixed(2) }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="amount-item">
                <label>支付方式：</label>
                <span>{{ getPaymentMethodText(orderDetail.payment_method) }}</span>
              </div>
            </el-col>
            <el-col :span="12" v-if="orderDetail.transaction_id">
              <div class="amount-item">
                <label>交易号：</label>
                <span class="transaction-id">{{ orderDetail.transaction_id }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 时间信息 -->
        <div class="time-info">
          <h3>时间信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="time-item">
                <label>创建时间：</label>
                <span>{{ formatDateTime(orderDetail.create_time) }}</span>
              </div>
            </el-col>
            <el-col :span="12" v-if="orderDetail.pay_time">
              <div class="time-item">
                <label>支付时间：</label>
                <span>{{ formatDateTime(orderDetail.pay_time) }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" v-if="orderDetail.cancel_time || orderDetail.refund_time">
            <el-col :span="12" v-if="orderDetail.cancel_time">
              <div class="time-item">
                <label>取消时间：</label>
                <span>{{ formatDateTime(orderDetail.cancel_time) }}</span>
              </div>
            </el-col>
            <el-col :span="12" v-if="orderDetail.refund_time">
              <div class="time-item">
                <label>退款时间：</label>
                <span>{{ formatDateTime(orderDetail.refund_time) }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" v-if="orderDetail.expire_time">
            <el-col :span="12">
              <div class="time-item">
                <label>过期时间：</label>
                <span>{{ formatDateTime(orderDetail.expire_time) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 额外信息 -->
        <div v-if="orderDetail.extra_data" class="extra-info">
          <h3>额外信息</h3>
          <div class="extra-content">
            <template v-if="orderDetail.order_type === 'strategy'">
              <div class="extra-item">
                <label>策略ID：</label>
                <span>{{ orderDetail.extra_data.strategy_id }}</span>
              </div>
              <div class="extra-item">
                <label>策略作者：</label>
                <span>{{ orderDetail.extra_data.creator_id }}</span>
              </div>
            </template>
            
            <template v-if="orderDetail.order_type === 'subscription'">
              <div class="extra-item">
                <label>订阅类型：</label>
                <span>{{ getSubscriptionText(orderDetail.extra_data.subscription_type) }}</span>
              </div>
              <div class="extra-item">
                <label>订阅时长：</label>
                <span>{{ orderDetail.extra_data.duration }}天</span>
              </div>
            </template>
          </div>
        </div>
        
        <!-- 备注信息 -->
        <div v-if="orderDetail.remark" class="remark-info">
          <h3>备注信息</h3>
          <div class="remark-content">
            {{ orderDetail.remark }}
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="orderDetail?.status === 'pending'"
          type="success" 
          @click="handleConfirmPayment"
        >
          确认支付
        </el-button>
        <el-button 
          v-if="orderDetail?.status === 'paid' && ['subscription', 'strategy'].includes(orderDetail?.order_type)"
          type="warning" 
          @click="handleRefund"
        >
          处理退款
        </el-button>
        <el-button 
          v-if="orderDetail?.status === 'pending'"
          type="danger" 
          @click="handleCancel"
        >
          取消订单
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { orderService } from '@/fuwu/orderService'

// Props
interface Props {
  modelValue: boolean
  orderId?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  orderId: ''
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const orderDetail = ref<any>(null)

// 工具函数
const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    cancelled: '已取消',
    refunded: '已退款',
    failed: '支付失败'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    paid: 'success',
    cancelled: 'info',
    refunded: 'danger',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

const getOrderTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    recharge: '充值订单',
    subscription: '订阅订单',
    strategy: '策略购买',
    withdraw: '提现订单',
    refund: '退款订单'
  }
  return typeMap[type] || '未知'
}

const getOrderTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    recharge: 'success',
    subscription: 'primary',
    strategy: 'warning',
    withdraw: 'info',
    refund: 'danger'
  }
  return typeMap[type] || 'info'
}

const getPaymentMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    alipay: '支付宝',
    wechat: '微信支付',
    bank: '银行卡',
    balance: '余额支付',
    combo: '组合支付'
  }
  return methodMap[method] || '未知'
}

const getSubscriptionText = (type: string) => {
  const typeMap: Record<string, string> = {
    zhuce: '注册用户版',
    jiben: '基础版',
    gaoji: '高级版'
  }
  return typeMap[type] || '未知'
}

// 方法
const loadOrderDetail = async () => {
  if (!props.orderId) return
  
  try {
    loading.value = true
    const detail = await orderService.getOrderDetail(props.orderId)
    orderDetail.value = detail
  } catch (error) {
    console.error('加载订单详情失败:', error)
    ElMessage.error('加载订单详情失败')
  } finally {
    loading.value = false
  }
}

const copyOrderId = () => {
  if (orderDetail.value?.id) {
    navigator.clipboard.writeText(orderDetail.value.id)
    ElMessage.success('订单号已复制到剪贴板')
  }
}

const handleConfirmPayment = async () => {
  if (!orderDetail.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要将订单标记为已支付吗？`,
      '确认支付',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    const success = await orderService.updateOrderStatus(
      orderDetail.value.id, 
      'paid', 
      '管理员手动确认支付'
    )
    
    if (success) {
      ElMessage.success('订单状态更新成功')
      emit('refresh')
      loadOrderDetail()
    } else {
      ElMessage.error('订单状态更新失败')
    }
  } catch {
    // 用户取消操作
  }
}

const handleRefund = async () => {
  if (!orderDetail.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要退款此订单吗？退款金额：¥${orderDetail.value.amount}`,
      '确认退款',
      {
        confirmButtonText: '确定退款',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await orderService.updateOrderStatus(
      orderDetail.value.id, 
      'refunded', 
      '管理员手动退款'
    )
    
    if (success) {
      ElMessage.success('退款处理成功')
      emit('refresh')
      loadOrderDetail()
    } else {
      ElMessage.error('退款处理失败')
    }
  } catch {
    // 用户取消操作
  }
}

const handleCancel = async () => {
  if (!orderDetail.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要取消此订单吗？`,
      '取消订单',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '返回',
        type: 'warning'
      }
    )
    
    const success = await orderService.updateOrderStatus(
      orderDetail.value.id, 
      'cancelled', 
      '管理员手动取消'
    )
    
    if (success) {
      ElMessage.success('订单已取消')
      emit('refresh')
      loadOrderDetail()
    } else {
      ElMessage.error('订单取消失败')
    }
  } catch {
    // 用户取消操作
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(() => props.orderId, (newOrderId) => {
  if (newOrderId && visible.value) {
    loadOrderDetail()
  }
})

watch(visible, (newVisible) => {
  if (newVisible && props.orderId) {
    loadOrderDetail()
  }
})
</script>

<style lang="scss" scoped>
.order-detail-container {
  min-height: 400px;

  .order-content {
    > div {
      margin-bottom: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-lighter);
        padding-bottom: 8px;
      }
    }
  }

  .info-item,
  .amount-item,
  .time-item,
  .extra-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      font-weight: 500;
      color: var(--el-text-color-regular);
      min-width: 100px;
      margin-right: 8px;
    }

    span {
      color: var(--el-text-color-primary);
    }
  }

  .order-id {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    margin-right: 8px;
  }

  .product-title {
    font-weight: 500;
  }

  .amount-value {
    font-size: 18px;
    font-weight: 600;
    color: #67c23a;
  }

  .original-amount {
    text-decoration: line-through;
    color: var(--el-text-color-placeholder);
  }

  .discount-amount {
    color: #f56c6c;
    font-weight: 500;
  }

  .transaction-id {
    font-family: 'Courier New', monospace;
    font-size: 12px;
  }

  .remark-content {
    padding: 12px;
    background: white;
    border-radius: 4px;
    border-left: 4px solid var(--el-color-primary);
    color: var(--el-text-color-primary);
    line-height: 1.6;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
