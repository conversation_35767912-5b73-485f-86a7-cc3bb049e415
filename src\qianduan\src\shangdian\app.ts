import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface AppState {
  // 应用设置
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  sidebarCollapsed: boolean
  
  // 加载状态
  loading: boolean
  loadingText: string
  
  // 缓存组件
  keepAliveComponents: string[]
  
  // 设备信息
  device: 'desktop' | 'tablet' | 'mobile'
  
  // 网络状态
  online: boolean
}

export const useAppStore = defineStore('app', () => {
  // 状态
  const theme = ref<AppState['theme']>('auto')
  const language = ref<AppState['language']>('zh-CN')
  const sidebarCollapsed = ref(false)
  const loading = ref(false)
  const loadingText = ref('加载中...')
  const keepAliveComponents = ref<string[]>([])
  const device = ref<AppState['device']>('desktop')
  const online = ref(navigator.onLine)
  
  // 计算属性
  const isDark = computed(() => {
    if (theme.value === 'dark') return true
    if (theme.value === 'light') return false
    return window.matchMedia('(prefers-color-scheme: dark)').matches
  })
  
  const isMobile = computed(() => device.value === 'mobile')
  const isTablet = computed(() => device.value === 'tablet')
  const isDesktop = computed(() => device.value === 'desktop')
  
  // 方法
  const setTheme = (newTheme: AppState['theme']) => {
    theme.value = newTheme
    localStorage.setItem('app-theme', newTheme)
    
    // 应用主题
    const html = document.documentElement
    if (newTheme === 'dark') {
      html.classList.add('dark')
    } else if (newTheme === 'light') {
      html.classList.remove('dark')
    } else {
      // auto模式
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      html.classList.toggle('dark', prefersDark)
    }
  }
  
  const setLanguage = (newLanguage: AppState['language']) => {
    language.value = newLanguage
    localStorage.setItem('app-language', newLanguage)
    
    // 这里可以添加国际化逻辑
    document.documentElement.lang = newLanguage === 'zh-CN' ? 'zh' : 'en'
  }
  
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebar-collapsed', String(sidebarCollapsed.value))
  }
  
  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebar-collapsed', String(collapsed))
  }
  
  const setLoading = (isLoading: boolean, text = '加载中...') => {
    loading.value = isLoading
    loadingText.value = text
  }
  
  const addKeepAliveComponent = (componentName: string) => {
    if (!keepAliveComponents.value.includes(componentName)) {
      keepAliveComponents.value.push(componentName)
    }
  }
  
  const removeKeepAliveComponent = (componentName: string) => {
    const index = keepAliveComponents.value.indexOf(componentName)
    if (index > -1) {
      keepAliveComponents.value.splice(index, 1)
    }
  }
  
  const clearKeepAliveComponents = () => {
    keepAliveComponents.value = []
  }
  
  const detectDevice = () => {
    const width = window.innerWidth
    if (width < 768) {
      device.value = 'mobile'
    } else if (width < 1024) {
      device.value = 'tablet'
    } else {
      device.value = 'desktop'
    }
    
    // 移动端默认收起侧边栏
    if (device.value === 'mobile') {
      sidebarCollapsed.value = true
    }
  }
  
  const setOnlineStatus = (status: boolean) => {
    online.value = status
  }
  
  // 初始化应用设置
  const initApp = () => {
    // 从localStorage恢复设置
    const savedTheme = localStorage.getItem('app-theme') as AppState['theme']
    if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
      setTheme(savedTheme)
    }
    
    const savedLanguage = localStorage.getItem('app-language') as AppState['language']
    if (savedLanguage && ['zh-CN', 'en-US'].includes(savedLanguage)) {
      setLanguage(savedLanguage)
    }
    
    const savedSidebarState = localStorage.getItem('sidebar-collapsed')
    if (savedSidebarState !== null) {
      sidebarCollapsed.value = savedSidebarState === 'true'
    }
    
    // 检测设备类型
    detectDevice()
    
    // 监听窗口大小变化
    window.addEventListener('resize', detectDevice)
    
    // 监听网络状态变化
    window.addEventListener('online', () => setOnlineStatus(true))
    window.addEventListener('offline', () => setOnlineStatus(false))
    
    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', () => {
      if (theme.value === 'auto') {
        setTheme('auto') // 重新应用auto主题
      }
    })
  }
  
  // 重置应用状态
  const resetApp = () => {
    theme.value = 'auto'
    language.value = 'zh-CN'
    sidebarCollapsed.value = false
    loading.value = false
    loadingText.value = '加载中...'
    keepAliveComponents.value = []
    
    // 清除localStorage
    localStorage.removeItem('app-theme')
    localStorage.removeItem('app-language')
    localStorage.removeItem('sidebar-collapsed')
  }
  
  return {
    // 状态
    theme,
    language,
    sidebarCollapsed,
    loading,
    loadingText,
    keepAliveComponents,
    device,
    online,
    
    // 计算属性
    isDark,
    isMobile,
    isTablet,
    isDesktop,
    
    // 方法
    setTheme,
    setLanguage,
    toggleSidebar,
    setSidebarCollapsed,
    setLoading,
    addKeepAliveComponent,
    removeKeepAliveComponent,
    clearKeepAliveComponents,
    detectDevice,
    setOnlineStatus,
    initApp,
    resetApp
  }
})

// 类型导出
export type AppStore = ReturnType<typeof useAppStore>
