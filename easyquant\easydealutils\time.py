import datetime
import doctest
from functools import lru_cache

import requests


@lru_cache()
def _is_holiday(day):
    # 原API已失效，使用简单的节假日判断逻辑
    # 这里可以根据需要添加更复杂的节假日判断逻辑
    try:
        # 尝试使用原API
        api = 'http://www.easybots.cn/api/holiday.php'
        params = {'d': day}
        rep = requests.get(api, params, timeout=3)
        if rep.status_code == 200:
            try:
                res = rep.json()[day if isinstance(day, str) else day[0]]
                return True if res == "1" else False
            except:
                pass
    except:
        pass

    # API失效时的备用逻辑：简单的节假日判断
    # 这里只是一个基础实现，实际使用时可能需要更完整的节假日数据
    year = int(day[:4])
    month_day = day[4:]

    # 一些固定的节假日（简化版本）
    fixed_holidays = [
        '0101',  # 元旦
        '0501', '0502', '0503',  # 劳动节
        '1001', '1002', '1003',  # 国庆节
    ]

    if month_day in fixed_holidays:
        return True

    # 春节期间（简化判断，实际需要根据农历计算）
    if year >= 2024:
        spring_festival_days = ['0210', '0211', '0212', '0213', '0214', '0215', '0216']  # 2024年春节示例
        if month_day in spring_festival_days:
            return True

    return False


def is_holiday(now_time):
    today = now_time.strftime('%Y%m%d')
    return _is_holiday(today)


def is_weekend(now_time):
    return now_time.weekday() >= 5


def is_trade_date(now_time):
    return not (is_holiday(now_time) or is_weekend(now_time))


def get_next_trade_date(now_time):
    """
    :param now_time: datetime.datetime
    :return:
    >>> import datetime
    >>> get_next_trade_date(datetime.date(2016, 5, 5))
    datetime.date(2016, 5, 6)
    """
    now = now_time
    max_days = 365
    days = 0
    while 1:
        days += 1
        now += datetime.timedelta(days=1)
        if is_trade_date(now):
            if isinstance(now, datetime.date):
                return now
            else:
                return now.date()
        if days > max_days:
            raise ValueError('无法确定 %s 下一个交易日' % now_time)


OPEN_TIME = (
    (datetime.time(9, 15, 0), datetime.time(11, 30, 0)),
    (datetime.time(13, 0, 0), datetime.time(15, 0, 0)),
)


def is_tradetime(now_time):
    """
    :param now_time: datetime.time()
    :return:
    """
    now = now_time.time()
    for begin, end in OPEN_TIME:
        if begin <= now < end:
            return True
    else:
        return False


PAUSE_TIME = (
    (datetime.time(11, 30, 0), datetime.time(12, 59, 30)),
)


def is_pause(now_time):
    """
    :param now_time:
    :return:
    """
    now = now_time.time()
    for b, e in PAUSE_TIME:
        if b <= now < e:
            return True


CONTINUE_TIME = (
    (datetime.time(12, 59, 30), datetime.time(13, 0, 0)),
)


def is_continue(now_time):
    now = now_time.time()
    for b, e in CONTINUE_TIME:
        if b <= now < e:
            return True
    return False


CLOSE_TIME = (
    datetime.time(15, 0, 0),
)


def is_closing(now_time, start=datetime.time(14, 54, 30)):
    now = now_time.time()
    for close in CLOSE_TIME:
        if start <= now < close:
            return True
    return False

if __name__ == "__main__":
    doctest.testmod()