<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑用户' : '用户详情'"
    width="800px"
    :before-close="handleClose"
  >
    <div class="user-detail-container" v-loading="loading">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-form
            ref="userFormRef"
            :model="userForm"
            :rules="userRules"
            label-width="120px"
            :disabled="!isEdit"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户ID">
                  <el-input v-model="userForm.id" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户名" prop="yonghuming">
                  <el-input v-model="userForm.yonghuming" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="邮箱" prop="youxiang">
                  <el-input v-model="userForm.youxiang" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="订阅类型" prop="dingyue_leixing">
                  <el-select v-model="userForm.dingyue_leixing" style="width: 100%">
                    <el-option label="注册用户版" value="zhuce" />
                    <el-option label="基础版" value="jiben" />
                    <el-option label="高级版" value="gaoji" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="账户状态">
                  <el-switch
                    v-model="userForm.shi_huoyue"
                    active-text="启用"
                    inactive-text="禁用"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册时间">
                  <el-input v-model="formattedCreateTime" disabled />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="订阅开始">
                  <el-date-picker
                    v-model="userForm.dingyue_kaishi"
                    type="date"
                    placeholder="选择开始日期"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="订阅结束">
                  <el-date-picker
                    v-model="userForm.dingyue_jieshu"
                    type="date"
                    placeholder="选择结束日期"
                    style="width: 100%"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 订阅历史 -->
        <el-tab-pane label="订阅历史" name="subscription">
          <div class="subscription-history">
            <el-timeline>
              <el-timeline-item
                v-for="record in subscriptionHistory"
                :key="record.id"
                :timestamp="record.date"
                placement="top"
              >
                <el-card>
                  <div class="subscription-record">
                    <div class="record-header">
                      <span class="record-type">{{ record.type }}</span>
                      <el-tag :type="record.status === 'active' ? 'success' : 'info'" size="small">
                        {{ record.status === 'active' ? '生效' : '已过期' }}
                      </el-tag>
                    </div>
                    <div class="record-details">
                      <p><strong>订阅类型:</strong> {{ getSubscriptionText(record.subscription_type) }}</p>
                      <p><strong>有效期:</strong> {{ record.start_date }} 至 {{ record.end_date }}</p>
                      <p v-if="record.amount"><strong>金额:</strong> ¥{{ record.amount }}</p>
                    </div>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>
        
        <!-- 操作日志 -->
        <el-tab-pane label="操作日志" name="logs">
          <div class="operation-logs">
            <el-table :data="operationLogs" style="width: 100%">
              <el-table-column prop="operation" label="操作" width="150" />
              <el-table-column prop="operator" label="操作人" width="120" />
              <el-table-column prop="timestamp" label="操作时间" width="180">
                <template #default="{ row }">
                  {{ formatDateTime(row.timestamp) }}
                </template>
              </el-table-column>
              <el-table-column prop="details" label="详情" />
              <el-table-column prop="ip" label="IP地址" width="140" />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button v-if="!isEdit" type="primary" @click="isEdit = true">编辑</el-button>
        <el-button v-if="isEdit" type="primary" @click="handleSave" :loading="saving">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import dayjs from 'dayjs'
import { userService } from '@/fuwu/userService'

// Props
interface Props {
  modelValue: boolean
  userId?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  userId: 0
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const saving = ref(false)
const isEdit = ref(false)
const activeTab = ref('basic')
const userFormRef = ref<FormInstance>()

// 用户表单数据
const userForm = reactive({
  id: 0,
  yonghuming: '',
  youxiang: '',
  dingyue_leixing: 'zhuce' as 'zhuce' | 'jiben' | 'gaoji',
  shi_huoyue: true,
  chuangjian_shijian: '',
  dingyue_kaishi: '',
  dingyue_jieshu: ''
})

// 订阅历史
const subscriptionHistory = ref([
  {
    id: 1,
    type: '用户注册',
    subscription_type: 'zhuce',
    start_date: '2024-08-01',
    end_date: '2024-08-31',
    amount: 0,
    status: 'expired',
    date: '2024-08-01 10:30:00'
  },
  {
    id: 2,
    type: '升级订阅',
    subscription_type: 'jiben',
    start_date: '2024-08-15',
    end_date: '2024-09-15',
    amount: 99,
    status: 'active',
    date: '2024-08-15 14:20:00'
  }
])

// 操作日志
const operationLogs = ref([
  {
    operation: '修改用户信息',
    operator: 'superadmin',
    timestamp: '2024-08-20 15:30:00',
    details: '修改了用户邮箱地址',
    ip: '*************'
  },
  {
    operation: '禁用用户',
    operator: 'admin',
    timestamp: '2024-08-19 09:15:00',
    details: '因违规操作禁用用户账户',
    ip: '*************'
  }
])

// 表单验证规则
const userRules: FormRules = {
  yonghuming: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  youxiang: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  dingyue_leixing: [
    { required: true, message: '请选择订阅类型', trigger: 'change' }
  ]
}

// 计算属性
const formattedCreateTime = computed(() => {
  return userForm.chuangjian_shijian ? dayjs(userForm.chuangjian_shijian).format('YYYY-MM-DD HH:mm:ss') : ''
})

// 工具函数
const getSubscriptionText = (type: string) => {
  const typeMap: Record<string, string> = {
    zhuce: '注册用户版',
    jiben: '基础版',
    gaoji: '高级版'
  }
  return typeMap[type] || '未知'
}

const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 方法
const loadUserDetail = async () => {
  if (!props.userId) return
  
  try {
    loading.value = true
    const user = await userService.getUserInfo(props.userId.toString())
    if (user) {
      Object.assign(userForm, user)
    }
  } catch (error) {
    console.error('加载用户详情失败:', error)
    ElMessage.error('加载用户详情失败')
  } finally {
    loading.value = false
  }
}

const handleSave = async () => {
  if (!userFormRef.value) return
  
  try {
    const valid = await userFormRef.value.validate()
    if (!valid) return
    
    saving.value = true
    
    const result = await userService.updateUser(userForm.id, {
      yonghuming: userForm.yonghuming,
      youxiang: userForm.youxiang,
      dingyue_leixing: userForm.dingyue_leixing,
      shi_huoyue: userForm.shi_huoyue,
      dingyue_kaishi: userForm.dingyue_kaishi,
      dingyue_jieshu: userForm.dingyue_jieshu
    })
    
    if (result.success) {
      ElMessage.success('用户信息更新成功')
      isEdit.value = false
      emit('refresh')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('保存用户信息失败:', error)
    ElMessage.error('保存用户信息失败')
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  isEdit.value = false
  activeTab.value = 'basic'
  visible.value = false
}

// 监听器
watch(() => props.userId, (newUserId) => {
  if (newUserId && visible.value) {
    loadUserDetail()
  }
})

watch(visible, (newVisible) => {
  if (newVisible && props.userId) {
    loadUserDetail()
  }
})
</script>

<style lang="scss" scoped>
.user-detail-container {
  min-height: 400px;
  
  :deep(.el-tabs__content) {
    padding: 20px;
  }
}

.subscription-history {
  max-height: 400px;
  overflow-y: auto;
  
  .subscription-record {
    .record-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      
      .record-type {
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .record-details {
      p {
        margin: 5px 0;
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
}

.operation-logs {
  max-height: 400px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
