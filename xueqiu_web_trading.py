#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
雪球真实模拟交易Web界面
基于雪球网真实API的Web交易系统
"""

from flask import Flask, request, jsonify
import json
import logging
from xueqiu_real_trading import XueqiuRealTrader

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

# 全局交易器实例
trader = None

def init_trader():
    """初始化交易器"""
    global trader
    try:
        trader = XueqiuRealTrader()
        return trader.is_logged_in
    except Exception as e:
        logging.error(f"❌ 初始化交易器失败: {e}")
        return False

@app.route('/')
def index():
    """主页"""
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>雪球真实模拟交易系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); overflow: hidden; }
        .header { background: linear-gradient(45deg, #FF6B6B, #4ECDC4); color: white; padding: 20px; text-align: center; }
        .main { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; padding: 20px; }
        .panel { background: #f8f9fa; border-radius: 10px; padding: 20px; border: 1px solid #e0e0e0; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; transition: all 0.3s; margin: 5px; }
        .btn-primary { background: #FF6B6B; color: white; }
        .btn-success { background: #4ECDC4; color: white; }
        .btn:hover { opacity: 0.8; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .alert { padding: 15px; margin: 15px 0; border-radius: 5px; display: none; }
        .alert.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { text-align: center; padding: 20px; }
        .cube-item { padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 10px; cursor: pointer; }
        .cube-item:hover { background: #f0f0f0; }
        .cube-item.active { background: #e3f2fd; border-color: #2196f3; }
        .holding-item { display: flex; justify-content: space-between; padding: 8px; border-bottom: 1px solid #eee; }
        .trade-form { background: white; padding: 15px; border-radius: 8px; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐍 雪球真实模拟交易系统</h1>
            <p>基于雪球网真实API，实现组合调仓功能</p>
            <div id="loginStatus" class="loading">检查登录状态...</div>
        </div>
        
        <div class="main">
            <!-- 左侧：组合管理 -->
            <div class="panel">
                <h3>📊 投资组合</h3>
                <button class="btn btn-primary" onclick="loadCubes()">刷新组合</button>
                <button class="btn btn-success" onclick="showCreateCube()">创建组合</button>
                
                <div id="createCubeForm" class="trade-form" style="display: none;">
                    <h4>创建新组合</h4>
                    <div class="form-group">
                        <label>组合名称:</label>
                        <input type="text" id="cubeName" placeholder="输入组合名称">
                    </div>
                    <div class="form-group">
                        <label>组合描述:</label>
                        <input type="text" id="cubeDesc" placeholder="输入组合描述">
                    </div>
                    <button class="btn btn-success" onclick="createCube()">创建</button>
                    <button class="btn" onclick="hideCreateCube()">取消</button>
                </div>
                
                <div id="cubesList" class="loading">加载中...</div>
                
                <div id="currentCube" style="margin-top: 20px;">
                    <h4>当前组合</h4>
                    <div id="currentCubeInfo">未选择组合</div>
                </div>
            </div>
            
            <!-- 中间：持仓和交易 -->
            <div class="panel">
                <h3>💼 持仓管理</h3>
                <button class="btn btn-primary" onclick="loadHoldings()">刷新持仓</button>
                
                <div id="holdingsList" class="loading">请先选择组合</div>
                
                <div class="trade-form">
                    <h4>🔄 股票交易</h4>
                    <div class="form-group">
                        <label>搜索股票:</label>
                        <input type="text" id="stockSearch" placeholder="输入股票名称或代码">
                        <button class="btn btn-primary" onclick="searchStock()">搜索</button>
                    </div>
                    
                    <div id="searchResults"></div>
                    
                    <div class="form-group">
                        <label>股票代码:</label>
                        <input type="text" id="stockSymbol" placeholder="如: SH600519">
                    </div>
                    <div class="form-group">
                        <label>目标权重 (%):</label>
                        <input type="number" id="stockWeight" placeholder="0-100" min="0" max="100" step="0.1">
                    </div>
                    <div class="form-group">
                        <label>操作说明:</label>
                        <input type="text" id="tradeComment" placeholder="可选">
                    </div>
                    
                    <button class="btn btn-success" onclick="buyStock()">买入/调整</button>
                    <button class="btn btn-primary" onclick="sellStock()">清仓</button>
                </div>
            </div>
            
            <!-- 右侧：组合表现 -->
            <div class="panel">
                <h3>📈 组合表现</h3>
                <button class="btn btn-primary" onclick="loadPerformance()">刷新数据</button>
                
                <div id="performanceData" class="loading">请先选择组合</div>
                
                <div style="margin-top: 20px;">
                    <h4>📝 操作日志</h4>
                    <div id="operationLog" style="max-height: 300px; overflow-y: auto; font-size: 12px;">
                        <div>系统启动...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="alertBox" class="alert"></div>
    </div>

    <script>
        let currentCubeSymbol = null;
        
        function showAlert(message, type = 'info') {
            const alertBox = document.getElementById('alertBox');
            alertBox.className = `alert ${type}`;
            alertBox.textContent = message;
            alertBox.style.display = 'block';
            
            setTimeout(() => {
                alertBox.style.display = 'none';
            }, 5000);
        }
        
        function addLog(message) {
            const logDiv = document.getElementById('operationLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function checkLoginStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('loginStatus');
                    if (data.logged_in) {
                        statusDiv.innerHTML = '✅ 已登录雪球';
                        statusDiv.className = '';
                        loadCubes();
                    } else {
                        statusDiv.innerHTML = '❌ 未登录 - 请检查配置文件';
                        statusDiv.className = 'alert error';
                    }
                })
                .catch(error => {
                    document.getElementById('loginStatus').innerHTML = '❌ 连接失败';
                    addLog(`状态检查失败: ${error}`);
                });
        }
        
        function loadCubes() {
            fetch('/api/cubes')
                .then(response => response.json())
                .then(data => {
                    const cubesDiv = document.getElementById('cubesList');
                    if (data.success) {
                        let html = '';
                        data.cubes.forEach(cube => {
                            const cubeSymbol = `ZH${cube.id.toString().padStart(6, '0')}`;
                            html += `
                                <div class="cube-item" onclick="selectCube('${cubeSymbol}', '${cube.name}')">
                                    <strong>${cube.name}</strong><br>
                                    <small>ID: ${cubeSymbol} | 股票数: ${cube.symbol_count}</small>
                                </div>
                            `;
                        });
                        cubesDiv.innerHTML = html || '<div>没有找到组合</div>';
                        addLog(`加载了 ${data.cubes.length} 个组合`);
                    } else {
                        cubesDiv.innerHTML = `<div class="alert error">${data.error}</div>`;
                        addLog(`加载组合失败: ${data.error}`);
                    }
                })
                .catch(error => {
                    document.getElementById('cubesList').innerHTML = '<div class="alert error">加载失败</div>';
                    addLog(`加载组合异常: ${error}`);
                });
        }
        
        function selectCube(cubeSymbol, cubeName) {
            currentCubeSymbol = cubeSymbol;
            
            // 更新UI
            document.querySelectorAll('.cube-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.cube-item').classList.add('active');
            
            document.getElementById('currentCubeInfo').innerHTML = `
                <strong>${cubeName}</strong><br>
                <small>${cubeSymbol}</small>
            `;
            
            // 设置当前组合
            fetch('/api/set_cube', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ cube_symbol: cubeSymbol })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog(`切换到组合: ${cubeName}`);
                    loadHoldings();
                    loadPerformance();
                } else {
                    showAlert(`切换组合失败: ${data.error}`, 'error');
                }
            });
        }
        
        function loadHoldings() {
            if (!currentCubeSymbol) {
                document.getElementById('holdingsList').innerHTML = '请先选择组合';
                return;
            }
            
            fetch('/api/holdings')
                .then(response => response.json())
                .then(data => {
                    const holdingsDiv = document.getElementById('holdingsList');
                    if (data.success) {
                        let html = '<h4>当前持仓</h4>';
                        if (data.holdings.length > 0) {
                            data.holdings.forEach(holding => {
                                html += `
                                    <div class="holding-item">
                                        <span>${holding.stock_name} (${holding.stock_symbol})</span>
                                        <span>${holding.weight.toFixed(1)}%</span>
                                    </div>
                                `;
                            });
                        } else {
                            html += '<div>暂无持仓</div>';
                        }
                        holdingsDiv.innerHTML = html;
                        addLog(`持仓加载完成: ${data.holdings.length} 只股票`);
                    } else {
                        holdingsDiv.innerHTML = `<div class="alert error">${data.error}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('holdingsList').innerHTML = '<div class="alert error">加载失败</div>';
                    addLog(`持仓加载异常: ${error}`);
                });
        }
        
        function loadPerformance() {
            if (!currentCubeSymbol) {
                document.getElementById('performanceData').innerHTML = '请先选择组合';
                return;
            }
            
            fetch('/api/performance')
                .then(response => response.json())
                .then(data => {
                    const perfDiv = document.getElementById('performanceData');
                    if (data.success) {
                        const perf = data.performance;
                        perfDiv.innerHTML = `
                            <h4>${perf.name || '组合表现'}</h4>
                            <div><strong>当前净值:</strong> ${perf.current_nav || 'N/A'}</div>
                            <div><strong>日收益:</strong> ${perf.daily_gain || 'N/A'}%</div>
                            <div><strong>总收益:</strong> ${perf.total_gain || 'N/A'}%</div>
                            <div><strong>年化收益:</strong> ${perf.annualized_gain || 'N/A'}%</div>
                        `;
                        addLog('组合表现数据已更新');
                    } else {
                        perfDiv.innerHTML = `<div class="alert error">${data.error}</div>`;
                    }
                })
                .catch(error => {
                    document.getElementById('performanceData').innerHTML = '<div class="alert error">加载失败</div>';
                    addLog(`表现数据异常: ${error}`);
                });
        }
        
        function searchStock() {
            const query = document.getElementById('stockSearch').value;
            if (!query) {
                showAlert('请输入搜索关键词', 'error');
                return;
            }
            
            fetch(`/api/search_stock?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    const resultsDiv = document.getElementById('searchResults');
                    if (data.success) {
                        let html = '<h5>搜索结果:</h5>';
                        data.stocks.slice(0, 5).forEach(stock => {
                            html += `
                                <div class="cube-item" onclick="selectStock('${stock.code}', '${stock.query}')">
                                    ${stock.query} (${stock.code})
                                </div>
                            `;
                        });
                        resultsDiv.innerHTML = html;
                    } else {
                        resultsDiv.innerHTML = `<div class="alert error">${data.error}</div>`;
                    }
                })
                .catch(error => {
                    showAlert(`搜索失败: ${error}`, 'error');
                });
        }
        
        function selectStock(code, name) {
            document.getElementById('stockSymbol').value = code;
            addLog(`选择股票: ${name} (${code})`);
        }
        
        function buyStock() {
            const symbol = document.getElementById('stockSymbol').value;
            const weight = document.getElementById('stockWeight').value;
            const comment = document.getElementById('tradeComment').value;
            
            if (!symbol || !weight) {
                showAlert('请填写股票代码和权重', 'error');
                return;
            }
            
            fetch('/api/buy', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    stock_symbol: symbol,
                    weight: parseFloat(weight),
                    comment: comment
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('买入/调整成功', 'success');
                    addLog(`买入成功: ${symbol} 权重${weight}%`);
                    loadHoldings();
                    loadPerformance();
                } else {
                    showAlert(`买入失败: ${data.error}`, 'error');
                    addLog(`买入失败: ${data.error}`);
                }
            })
            .catch(error => {
                showAlert(`买入异常: ${error}`, 'error');
            });
        }
        
        function sellStock() {
            const symbol = document.getElementById('stockSymbol').value;
            const comment = document.getElementById('tradeComment').value;
            
            if (!symbol) {
                showAlert('请填写股票代码', 'error');
                return;
            }
            
            fetch('/api/sell', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    stock_symbol: symbol,
                    weight: 0,
                    comment: comment
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('清仓成功', 'success');
                    addLog(`清仓成功: ${symbol}`);
                    loadHoldings();
                    loadPerformance();
                } else {
                    showAlert(`清仓失败: ${data.error}`, 'error');
                    addLog(`清仓失败: ${data.error}`);
                }
            })
            .catch(error => {
                showAlert(`清仓异常: ${error}`, 'error');
            });
        }
        
        function showCreateCube() {
            document.getElementById('createCubeForm').style.display = 'block';
        }
        
        function hideCreateCube() {
            document.getElementById('createCubeForm').style.display = 'none';
        }
        
        function createCube() {
            const name = document.getElementById('cubeName').value;
            const desc = document.getElementById('cubeDesc').value;
            
            if (!name) {
                showAlert('请输入组合名称', 'error');
                return;
            }
            
            fetch('/api/create_cube', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name: name,
                    description: desc
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('组合创建成功', 'success');
                    addLog(`创建组合: ${name}`);
                    hideCreateCube();
                    loadCubes();
                } else {
                    showAlert(`创建失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showAlert(`创建异常: ${error}`, 'error');
            });
        }
        
        // 页面加载时初始化
        window.onload = function() {
            checkLoginStatus();
        };
    </script>
</body>
</html>
    '''

# API路由
@app.route('/api/status')
def get_status():
    """获取系统状态"""
    try:
        if not trader:
            init_trader()
        
        return jsonify({
            'success': True,
            'logged_in': trader.is_logged_in if trader else False
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/cubes')
def get_cubes():
    """获取组合列表"""
    try:
        if not trader or not trader.is_logged_in:
            return jsonify({
                'success': False,
                'error': '未登录'
            })
        
        cubes = trader.get_user_cubes()
        return jsonify({
            'success': True,
            'cubes': cubes
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/set_cube', methods=['POST'])
def set_cube():
    """设置当前组合"""
    try:
        data = request.get_json()
        cube_symbol = data.get('cube_symbol')
        
        if not trader:
            return jsonify({
                'success': False,
                'error': '交易器未初始化'
            })
        
        trader.set_current_cube(cube_symbol)
        return jsonify({
            'success': True,
            'message': f'已切换到组合 {cube_symbol}'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/holdings')
def get_holdings():
    """获取持仓"""
    try:
        if not trader:
            return jsonify({
                'success': False,
                'error': '交易器未初始化'
            })
        
        holdings = trader.get_current_holdings()
        return jsonify({
            'success': True,
            'holdings': holdings
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/performance')
def get_performance():
    """获取组合表现"""
    try:
        if not trader:
            return jsonify({
                'success': False,
                'error': '交易器未初始化'
            })
        
        performance = trader.get_cube_performance()
        return jsonify({
            'success': True,
            'performance': performance or {}
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/search_stock')
def search_stock():
    """搜索股票"""
    try:
        query = request.args.get('q', '')
        if not query:
            return jsonify({
                'success': False,
                'error': '搜索关键词不能为空'
            })
        
        if not trader:
            return jsonify({
                'success': False,
                'error': '交易器未初始化'
            })
        
        stocks = trader.search_stock(query)
        return jsonify({
            'success': True,
            'stocks': stocks
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/buy', methods=['POST'])
def buy_stock():
    """买入股票"""
    try:
        data = request.get_json()
        stock_symbol = data.get('stock_symbol')
        weight = data.get('weight')
        comment = data.get('comment', '')
        
        if not trader:
            return jsonify({
                'success': False,
                'error': '交易器未初始化'
            })
        
        success = trader.buy_stock(stock_symbol, weight, comment)
        return jsonify({
            'success': success,
            'message': '买入成功' if success else '买入失败'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/sell', methods=['POST'])
def sell_stock():
    """卖出股票"""
    try:
        data = request.get_json()
        stock_symbol = data.get('stock_symbol')
        weight = data.get('weight', 0)
        comment = data.get('comment', '')
        
        if not trader:
            return jsonify({
                'success': False,
                'error': '交易器未初始化'
            })
        
        success = trader.sell_stock(stock_symbol, weight, comment)
        return jsonify({
            'success': success,
            'message': '卖出成功' if success else '卖出失败'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/create_cube', methods=['POST'])
def create_cube():
    """创建组合"""
    try:
        data = request.get_json()
        name = data.get('name')
        description = data.get('description', '')
        
        if not trader:
            return jsonify({
                'success': False,
                'error': '交易器未初始化'
            })
        
        cube_symbol = trader.create_cube(name, description)
        return jsonify({
            'success': cube_symbol is not None,
            'cube_symbol': cube_symbol,
            'message': '创建成功' if cube_symbol else '创建失败'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def main():
    """主函数"""
    print("🚀 雪球真实模拟交易Web系统")
    print("基于雪球网真实API的Web交易界面")
    print("=" * 60)
    
    # 初始化交易器
    if init_trader():
        print("✅ 交易器初始化成功")
    else:
        print("⚠️ 交易器初始化失败，部分功能可能不可用")
    
    print("\n🌐 启动Web服务器...")
    print("访问地址: http://localhost:5005")
    print("功能特点:")
    print("  📊 真实雪球组合管理")
    print("  🔄 组合调仓交易")
    print("  📈 实时组合表现")
    print("  🔍 股票搜索功能")
    print("  💼 持仓管理")
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5005, debug=True)

if __name__ == '__main__':
    main()
