#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复版股票信息获取模块
解决股票名称缺失和代码格式问题
"""

import json
import logging
import re
from typing import Dict, List, Optional, Tuple
import pysnowball as ball

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FixedStockInfoProvider:
    """修复版股票信息提供器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.stock_name_cache = {}  # 股票名称缓存
        self.load_token()
    
    def load_token(self):
        """加载token"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info("✅ Token设置成功")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 加载token失败: {e}")
            return False
    
    def normalize_stock_symbol(self, symbol: str) -> str:
        """标准化股票代码格式"""
        if not symbol:
            return symbol
        
        symbol = symbol.upper().strip()
        
        # 如果已经有前缀，直接返回
        if symbol.startswith(('SH', 'SZ', 'BJ')):
            return symbol
        
        # 如果是6位数字，判断市场
        if re.match(r'^\d{6}$', symbol):
            code = symbol
            # 上海市场：60开头、68开头、90开头
            if code.startswith(('60', '68', '90')):
                return f'SH{code}'
            # 深圳市场：00开头、30开头、20开头
            elif code.startswith(('00', '30', '20')):
                return f'SZ{code}'
            # 北京市场：43开头、83开头、87开头
            elif code.startswith(('43', '83', '87')):
                return f'BJ{code}'
            else:
                # 默认上海市场
                return f'SH{code}'
        
        # 港股等其他格式直接返回
        return symbol
    
    def get_stock_name_from_search(self, symbol: str) -> Optional[str]:
        """通过搜索API获取股票名称"""
        try:
            # 先检查缓存
            if symbol in self.stock_name_cache:
                return self.stock_name_cache[symbol]
            
            # 搜索股票
            result = ball.suggest_stock(symbol)
            if result and 'data' in result:
                stocks = result['data']
                
                # 查找完全匹配的股票
                for stock in stocks:
                    if stock.get('code') == symbol:
                        name = stock.get('query', '')
                        if name:
                            self.stock_name_cache[symbol] = name
                            return name
                
                # 如果没有完全匹配，返回第一个结果
                if stocks:
                    name = stocks[0].get('query', '')
                    if name:
                        self.stock_name_cache[symbol] = name
                        return name
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 搜索股票名称失败: {e}")
            return None
    
    def get_stock_quote(self, symbol: str) -> Optional[Dict]:
        """获取完整的股票行情信息（包含名称）"""
        try:
            # 标准化股票代码
            normalized_symbol = self.normalize_stock_symbol(symbol)
            
            # 获取行情数据
            result = ball.quotec(normalized_symbol)
            if not result or 'data' not in result or not result['data']:
                logger.warning(f"⚠️ 无法获取 {normalized_symbol} 的行情数据")
                return None
            
            quote_data = result['data'][0]
            
            # 获取股票名称
            stock_name = self.get_stock_name_from_search(normalized_symbol)
            if stock_name:
                quote_data['name'] = stock_name
            else:
                # 如果搜索失败，使用股票代码作为名称
                quote_data['name'] = normalized_symbol
            
            # 确保symbol字段是标准化的
            quote_data['symbol'] = normalized_symbol
            
            logger.info(f"✅ 获取股票信息成功: {quote_data['name']} ({normalized_symbol}) 价格: {quote_data.get('current', 'N/A')}")
            
            return quote_data
            
        except Exception as e:
            logger.error(f"❌ 获取股票行情失败: {e}")
            return None
    
    def get_stock_pankou(self, symbol: str) -> Optional[Dict]:
        """获取五档行情"""
        try:
            normalized_symbol = self.normalize_stock_symbol(symbol)
            result = ball.pankou(normalized_symbol)
            
            if result:
                # 添加股票名称
                stock_name = self.get_stock_name_from_search(normalized_symbol)
                if stock_name:
                    result['name'] = stock_name
                else:
                    result['name'] = normalized_symbol
                
                result['symbol'] = normalized_symbol
                logger.info(f"✅ 获取五档行情成功: {result['name']} ({normalized_symbol})")
                
            return result
            
        except Exception as e:
            logger.error(f"❌ 获取五档行情失败: {e}")
            return None
    
    def search_stocks(self, query: str, limit: int = 10) -> List[Dict]:
        """搜索股票"""
        try:
            result = ball.suggest_stock(query)
            if result and 'data' in result:
                stocks = result['data'][:limit]
                
                # 标准化搜索结果
                standardized_stocks = []
                for stock in stocks:
                    standardized_stock = {
                        'symbol': stock.get('code', ''),
                        'name': stock.get('query', ''),
                        'code': stock.get('code', ''),
                        'query': stock.get('query', '')
                    }
                    standardized_stocks.append(standardized_stock)
                
                logger.info(f"✅ 搜索到 {len(standardized_stocks)} 个结果")
                return standardized_stocks
            
            return []
            
        except Exception as e:
            logger.error(f"❌ 搜索股票失败: {e}")
            return []
    
    def get_multiple_quotes(self, symbols: List[str]) -> Dict[str, Dict]:
        """批量获取股票行情"""
        quotes = {}
        
        for symbol in symbols:
            quote = self.get_stock_quote(symbol)
            if quote:
                quotes[symbol] = quote
        
        return quotes
    
    def validate_stock_symbol(self, symbol: str) -> Tuple[bool, str]:
        """验证股票代码是否有效"""
        try:
            normalized_symbol = self.normalize_stock_symbol(symbol)
            quote = self.get_stock_quote(normalized_symbol)
            
            if quote:
                return True, f"有效股票: {quote['name']} ({normalized_symbol})"
            else:
                return False, f"无效股票代码: {symbol}"
                
        except Exception as e:
            return False, f"验证失败: {str(e)}"
    
    def get_stock_basic_info(self, symbol: str) -> Optional[Dict]:
        """获取股票基本信息（轻量级）"""
        try:
            normalized_symbol = self.normalize_stock_symbol(symbol)
            stock_name = self.get_stock_name_from_search(normalized_symbol)
            
            if stock_name:
                return {
                    'symbol': normalized_symbol,
                    'name': stock_name,
                    'original_symbol': symbol
                }
            else:
                return {
                    'symbol': normalized_symbol,
                    'name': normalized_symbol,
                    'original_symbol': symbol
                }
                
        except Exception as e:
            logger.error(f"❌ 获取股票基本信息失败: {e}")
            return None

def test_fixed_stock_info():
    """测试修复版股票信息获取"""
    print("🧪 测试修复版股票信息获取")
    print("=" * 50)
    
    provider = FixedStockInfoProvider()
    
    # 测试各种格式的股票代码
    test_symbols = [
        '600519',    # 贵州茅台（不带前缀）
        'SH600519',  # 贵州茅台（带前缀）
        '000001',    # 平安银行（不带前缀）
        'SZ000001',  # 平安银行（带前缀）
        '000858',    # 五粮液（不带前缀）
        '300015',    # 爱尔眼科（创业板）
    ]
    
    for symbol in test_symbols:
        print(f"\n📊 测试股票: {symbol}")
        print("-" * 30)
        
        # 测试股票行情
        quote = provider.get_stock_quote(symbol)
        if quote:
            print(f"✅ 行情获取成功:")
            print(f"   名称: {quote.get('name', 'N/A')}")
            print(f"   代码: {quote.get('symbol', 'N/A')}")
            print(f"   价格: {quote.get('current', 'N/A')}")
            print(f"   涨跌: {quote.get('chg', 'N/A')} ({quote.get('percent', 'N/A')}%)")
        else:
            print(f"❌ 行情获取失败")
        
        # 测试五档行情
        pankou = provider.get_stock_pankou(symbol)
        if pankou:
            print(f"✅ 五档行情获取成功:")
            print(f"   买一: {pankou.get('bp1', 'N/A')} x {pankou.get('bc1', 'N/A')}")
            print(f"   卖一: {pankou.get('sp1', 'N/A')} x {pankou.get('sc1', 'N/A')}")
        else:
            print(f"❌ 五档行情获取失败")
    
    # 测试搜索功能
    print(f"\n🔍 测试搜索功能")
    print("-" * 30)
    
    search_terms = ['茅台', '平安', '腾讯']
    for term in search_terms:
        print(f"\n搜索: {term}")
        stocks = provider.search_stocks(term, 3)
        for i, stock in enumerate(stocks):
            print(f"  {i+1}. {stock['name']} ({stock['symbol']})")

if __name__ == '__main__':
    test_fixed_stock_info()
