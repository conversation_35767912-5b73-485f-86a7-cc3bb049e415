<template>
  <el-dialog
    v-model="visible"
    title="财务报表"
    width="1200px"
    :before-close="handleClose"
  >
    <div class="finance-report-container" v-loading="loading">
      <div class="report-content">
        <!-- 报表控制 -->
        <div class="report-controls">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-select v-model="reportType" placeholder="报表类型" style="width: 100%">
                <el-option label="收入报表" value="revenue" />
                <el-option label="利润报表" value="profit" />
                <el-option label="用户报表" value="user" />
                <el-option label="综合报表" value="comprehensive" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="timePeriod" placeholder="时间周期" style="width: 100%">
                <el-option label="最近7天" value="7" />
                <el-option label="最近30天" value="30" />
                <el-option label="最近90天" value="90" />
                <el-option label="本年度" value="365" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-date-picker
                v-model="customDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="generateReport">
                <el-icon><Document /></el-icon>
                生成报表
              </el-button>
            </el-col>
          </el-row>
        </div>
        
        <!-- 报表图表 -->
        <div class="report-charts">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>收入趋势</span>
                </template>
                <div class="chart-container">
                  <v-chart 
                    :option="revenueChartOption" 
                    :loading="chartLoading"
                    class="chart"
                    autoresize
                  />
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>收入构成</span>
                </template>
                <div class="chart-container">
                  <v-chart 
                    :option="pieChartOption" 
                    :loading="chartLoading"
                    class="chart"
                    autoresize
                  />
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <!-- 报表数据表格 -->
        <div class="report-table">
          <el-card>
            <template #header>
              <div class="table-header">
                <span>详细数据</span>
                <div class="table-actions">
                  <el-button type="success" size="small" @click="exportExcel">
                    <el-icon><Download /></el-icon>
                    导出Excel
                  </el-button>
                  <el-button type="primary" size="small" @click="exportPDF">
                    <el-icon><Document /></el-icon>
                    导出PDF
                  </el-button>
                </div>
              </div>
            </template>
            
            <el-table :data="reportData" style="width: 100%" show-summary>
              <el-table-column prop="date" label="日期" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.date) }}
                </template>
              </el-table-column>
              
              <el-table-column prop="revenue" label="收入" width="120">
                <template #default="{ row }">
                  <span class="revenue-text">¥{{ row.revenue.toLocaleString() }}</span>
                </template>
              </el-table-column>
              
              <el-table-column prop="orders" label="订单数" width="100" />
              
              <el-table-column prop="refunds" label="退款" width="120">
                <template #default="{ row }">
                  <span class="refund-text">¥{{ row.refunds.toLocaleString() }}</span>
                </template>
              </el-table-column>
              
              <el-table-column prop="net_revenue" label="净收入" width="120">
                <template #default="{ row }">
                  <span class="net-revenue-text">¥{{ row.net_revenue.toLocaleString() }}</span>
                </template>
              </el-table-column>
              
              <el-table-column prop="new_users" label="新用户" width="100" />
              
              <el-table-column prop="active_users" label="活跃用户" width="100" />
              
              <el-table-column label="转化率" width="100">
                <template #default="{ row }">
                  {{ ((row.orders / row.active_users) * 100).toFixed(1) }}%
                </template>
              </el-table-column>
              
              <el-table-column label="客单价" width="120">
                <template #default="{ row }">
                  ¥{{ (row.revenue / row.orders).toFixed(2) }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
        
        <!-- 关键指标汇总 -->
        <div class="report-summary">
          <el-card>
            <template #header>
              <span>关键指标汇总</span>
            </template>
            
            <el-row :gutter="16">
              <el-col :span="6">
                <div class="summary-item">
                  <div class="summary-label">总收入</div>
                  <div class="summary-value revenue">¥{{ totalRevenue.toLocaleString() }}</div>
                </div>
              </el-col>
              
              <el-col :span="6">
                <div class="summary-item">
                  <div class="summary-label">总订单</div>
                  <div class="summary-value orders">{{ totalOrders.toLocaleString() }}</div>
                </div>
              </el-col>
              
              <el-col :span="6">
                <div class="summary-item">
                  <div class="summary-label">平均客单价</div>
                  <div class="summary-value avg">¥{{ avgOrderValue.toFixed(2) }}</div>
                </div>
              </el-col>
              
              <el-col :span="6">
                <div class="summary-item">
                  <div class="summary-label">退款率</div>
                  <div class="summary-value refund">{{ refundRate.toFixed(1) }}%</div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handlePrint">
          <el-icon><Printer /></el-icon>
          打印报表
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { use } from 'echarts/core'
import { LineChart, PieChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import { financeService } from '@/fuwu/financeService'

// 注册ECharts组件
use([LineChart, PieChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

// Props
interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const chartLoading = ref(false)
const reportType = ref('revenue')
const timePeriod = ref('30')
const customDateRange = ref<[string, string] | null>(null)

// 数据
const reportData = ref<any[]>([])

// 计算属性
const totalRevenue = computed(() => {
  return reportData.value.reduce((sum, item) => sum + item.revenue, 0)
})

const totalOrders = computed(() => {
  return reportData.value.reduce((sum, item) => sum + item.orders, 0)
})

const avgOrderValue = computed(() => {
  return totalOrders.value > 0 ? totalRevenue.value / totalOrders.value : 0
})

const refundRate = computed(() => {
  const totalRefunds = reportData.value.reduce((sum, item) => sum + item.refunds, 0)
  return totalRevenue.value > 0 ? (totalRefunds / totalRevenue.value) * 100 : 0
})

// 图表配置
const revenueChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const data = params[0]
      return `${data.name}<br/>收入: ¥${data.value.toLocaleString()}`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: reportData.value.map(item => dayjs(item.date).format('MM-DD'))
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => `¥${(value / 1000).toFixed(0)}k`
    }
  },
  series: [
    {
      name: '收入',
      type: 'line',
      data: reportData.value.map(item => item.revenue),
      smooth: true,
      lineStyle: {
        color: '#67c23a',
        width: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.05)' }
          ]
        }
      }
    }
  ]
}))

const pieChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '收入构成',
      type: 'pie',
      radius: '50%',
      data: [
        { value: totalRevenue.value * 0.6, name: '订阅收入' },
        { value: totalRevenue.value * 0.3, name: '策略销售' },
        { value: totalRevenue.value * 0.1, name: '其他收入' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 工具函数
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}

// 方法
const generateReport = async () => {
  try {
    loading.value = true
    chartLoading.value = true
    
    const days = parseInt(timePeriod.value)
    const reports = await financeService.getFinanceReports(days)
    reportData.value = reports
    
  } catch (error) {
    console.error('生成报表失败:', error)
    ElMessage.error('生成报表失败')
  } finally {
    loading.value = false
    chartLoading.value = false
  }
}

const exportExcel = async () => {
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('Excel报表导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const exportPDF = async () => {
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('PDF报表导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handlePrint = () => {
  window.print()
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(visible, (newVisible) => {
  if (newVisible) {
    generateReport()
  }
})

watch([reportType, timePeriod], () => {
  if (visible.value) {
    generateReport()
  }
})
</script>

<style lang="scss" scoped>
.finance-report-container {
  min-height: 700px;

  .report-content {
    .report-controls {
      margin-bottom: 20px;
    }

    .report-charts {
      margin-bottom: 20px;

      .chart-container {
        height: 300px;

        .chart {
          width: 100%;
          height: 100%;
        }
      }
    }

    .report-table {
      margin-bottom: 20px;

      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .table-actions {
          display: flex;
          gap: 8px;
        }
      }

      .revenue-text {
        color: #67c23a;
        font-weight: 500;
      }

      .refund-text {
        color: #f56c6c;
        font-weight: 500;
      }

      .net-revenue-text {
        color: #409eff;
        font-weight: 600;
      }
    }

    .report-summary {
      .summary-item {
        text-align: center;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;

        .summary-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
          margin-bottom: 8px;
        }

        .summary-value {
          font-size: 20px;
          font-weight: 600;

          &.revenue {
            color: #67c23a;
          }

          &.orders {
            color: #409eff;
          }

          &.avg {
            color: #e6a23c;
          }

          &.refund {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

// 打印样式
@media print {
  .dialog-footer {
    display: none;
  }

  .report-controls {
    display: none;
  }

  .table-actions {
    display: none;
  }
}
</style>
