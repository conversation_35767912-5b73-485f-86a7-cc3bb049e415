<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>策略列表</span>
          <el-button type="primary">新建策略</el-button>
        </div>
      </template>
      <el-empty description="策略列表页面开发中..." />
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 策略列表页面
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
