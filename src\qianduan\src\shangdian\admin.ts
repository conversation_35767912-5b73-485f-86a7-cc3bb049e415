import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 管理员信息接口
export interface AdminInfo {
  id: number
  guanli_yonghuming: string
  role: 'super_admin' | 'admin'
  youxiang: string
  chuangjian_shijian: string
  zuihou_denglu: string
  shi_huoyue: boolean
}

// 管理员权限类型
export type AdminPermission = 
  | 'user_management'      // 用户管理
  | 'system_config'        // 系统配置
  | 'data_management'      // 数据管理
  | 'security_audit'       // 安全审计
  | 'financial_management' // 财务管理
  | 'content_management'   // 内容管理

export const useAdminStore = defineStore('admin', () => {
  // 状态
  const adminToken = ref<string>('')
  const adminInfo = ref<AdminInfo | null>(null)
  const adminPermissions = ref<AdminPermission[]>([])
  
  // 计算属性
  const isAdminLoggedIn = computed(() => !!adminToken.value)
  const isSuperAdmin = computed(() => adminInfo.value?.role === 'super_admin')
  
  // 方法
  const setAdminToken = (token: string) => {
    adminToken.value = token
    localStorage.setItem('admin-token', token)
  }
  
  const setAdminInfo = (info: AdminInfo) => {
    adminInfo.value = info
    localStorage.setItem('admin-info', JSON.stringify(info))
  }
  
  const setAdminPermissions = (permissions: AdminPermission[]) => {
    adminPermissions.value = permissions
    localStorage.setItem('admin-permissions', JSON.stringify(permissions))
  }
  
  // 检查管理员权限
  const hasAdminPermission = (permission: AdminPermission) => {
    return adminPermissions.value.includes(permission)
  }
  
  // 检查多个权限（需要全部拥有）
  const hasAllAdminPermissions = (permissions: AdminPermission[]) => {
    return permissions.every(perm => adminPermissions.value.includes(perm))
  }
  
  // 检查多个权限（拥有其中一个即可）
  const hasAnyAdminPermission = (permissions: AdminPermission[]) => {
    return permissions.some(perm => adminPermissions.value.includes(perm))
  }
  
  // 管理员登出
  const adminLogout = async () => {
    try {
      // 这里可以调用登出API
      console.log('管理员登出')
    } catch (error) {
      console.error('管理员登出API调用失败:', error)
    } finally {
      // 清除本地状态
      adminToken.value = ''
      adminInfo.value = null
      adminPermissions.value = []
      
      // 清除localStorage
      localStorage.removeItem('admin-token')
      localStorage.removeItem('admin-info')
      localStorage.removeItem('admin-permissions')
      
      // 重定向到管理员登录页
      window.location.href = '/numendavid中国/login'
    }
  }
  
  // 初始化管理员状态
  const initAdmin = () => {
    // 从localStorage恢复状态
    const savedToken = localStorage.getItem('admin-token')
    const savedAdminInfo = localStorage.getItem('admin-info')
    const savedPermissions = localStorage.getItem('admin-permissions')
    
    if (savedToken) {
      adminToken.value = savedToken
    }
    
    if (savedAdminInfo) {
      try {
        adminInfo.value = JSON.parse(savedAdminInfo)
      } catch (error) {
        console.error('解析管理员信息失败:', error)
        localStorage.removeItem('admin-info')
      }
    }
    
    if (savedPermissions) {
      try {
        adminPermissions.value = JSON.parse(savedPermissions)
      } catch (error) {
        console.error('解析管理员权限失败:', error)
        localStorage.removeItem('admin-permissions')
      }
    }
  }
  
  // 重置管理员状态
  const resetAdmin = () => {
    adminToken.value = ''
    adminInfo.value = null
    adminPermissions.value = []
    
    localStorage.removeItem('admin-token')
    localStorage.removeItem('admin-info')
    localStorage.removeItem('admin-permissions')
  }
  
  // 获取管理员统计信息
  const getAdminStats = () => {
    return {
      totalUsers: 0,
      activeUsers: 0,
      totalStrategies: 0,
      totalBacktests: 0,
      systemHealth: 'good' as 'good' | 'warning' | 'error'
    }
  }
  
  return {
    // 状态
    adminToken,
    adminInfo,
    adminPermissions,
    
    // 计算属性
    isAdminLoggedIn,
    isSuperAdmin,
    
    // 方法
    setAdminToken,
    setAdminInfo,
    setAdminPermissions,
    hasAdminPermission,
    hasAllAdminPermissions,
    hasAnyAdminPermission,
    adminLogout,
    initAdmin,
    resetAdmin,
    getAdminStats
  }
})

// 类型导出
export type AdminStore = ReturnType<typeof useAdminStore>
