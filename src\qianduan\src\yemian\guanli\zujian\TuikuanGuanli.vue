<template>
  <el-dialog
    v-model="visible"
    title="退款管理"
    width="1200px"
    :before-close="handleClose"
  >
    <div class="refund-management-container" v-loading="loading">
      <div class="refund-content">
        <!-- 退款统计 -->
        <div class="refund-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value pending">{{ pendingRefunds.length }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value approved">{{ approvedRefunds.length }}</div>
                <div class="stat-label">已批准</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value completed">{{ completedRefunds.length }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value total">¥{{ formatMoney(totalRefundAmount) }}</div>
                <div class="stat-label">退款总额</div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 筛选条件 -->
        <div class="refund-filters">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-select v-model="statusFilter" placeholder="退款状态" style="width: 100%">
                <el-option label="全部状态" value="" />
                <el-option label="待处理" value="pending" />
                <el-option label="已批准" value="approved" />
                <el-option label="已拒绝" value="rejected" />
                <el-option label="已完成" value="completed" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索订单号或用户名"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="8">
              <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="handleSearch">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
            </el-col>
          </el-row>
        </div>
        
        <!-- 退款列表 -->
        <div class="refund-list">
          <el-table :data="filteredRefunds" style="width: 100%">
            <el-table-column prop="id" label="退款单号" width="180">
              <template #default="{ row }">
                <el-link type="primary" @click="handleViewRefund(row)">
                  {{ row.id }}
                </el-link>
              </template>
            </el-table-column>
            
            <el-table-column prop="order_id" label="订单号" width="180" />
            
            <el-table-column prop="user_name" label="用户" width="120" />
            
            <el-table-column prop="order_amount" label="订单金额" width="100">
              <template #default="{ row }">
                ¥{{ row.order_amount.toFixed(2) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="refund_amount" label="退款金额" width="100">
              <template #default="{ row }">
                <span class="refund-amount">¥{{ row.refund_amount.toFixed(2) }}</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="refund_reason" label="退款原因" width="150" show-overflow-tooltip />
            
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="apply_time" label="申请时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.apply_time) }}
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button type="info" size="small" @click="handleViewRefund(row)">
                  详情
                </el-button>
                <el-button 
                  v-if="row.status === 'pending'"
                  type="success" 
                  size="small" 
                  @click="handleApproveRefund(row)"
                >
                  批准
                </el-button>
                <el-button 
                  v-if="row.status === 'pending'"
                  type="danger" 
                  size="small" 
                  @click="handleRejectRefund(row)"
                >
                  拒绝
                </el-button>
                <el-button 
                  v-if="row.status === 'approved'"
                  type="primary" 
                  size="small" 
                  @click="handleCompleteRefund(row)"
                >
                  完成退款
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50]"
              :total="totalRefunds"
              layout="total, sizes, prev, pager, next, jumper"
            />
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleBatchProcess" :disabled="selectedRefunds.length === 0">
          批量处理 ({{ selectedRefunds.length }})
        </el-button>
      </div>
    </template>
    
    <!-- 退款详情弹窗 -->
    <RefundDetail
      v-model="showRefundDetail"
      :refund="selectedRefund"
      @refresh="loadRefundRequests"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { financeService } from '@/fuwu/financeService'
import RefundDetail from './RefundDetail.vue'

// Props
interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const statusFilter = ref('')
const searchKeyword = ref('')
const dateRange = ref<[string, string] | null>(null)
const currentPage = ref(1)
const pageSize = ref(20)
const totalRefunds = ref(0)

const showRefundDetail = ref(false)
const selectedRefund = ref<any>(null)
const selectedRefunds = ref<any[]>([])

// 数据
const refundRequests = ref<any[]>([])

// 计算属性
const pendingRefunds = computed(() => {
  return refundRequests.value.filter(r => r.status === 'pending')
})

const approvedRefunds = computed(() => {
  return refundRequests.value.filter(r => r.status === 'approved')
})

const completedRefunds = computed(() => {
  return refundRequests.value.filter(r => r.status === 'completed')
})

const totalRefundAmount = computed(() => {
  return refundRequests.value
    .filter(r => ['approved', 'completed'].includes(r.status))
    .reduce((sum, r) => sum + r.refund_amount, 0)
})

const filteredRefunds = computed(() => {
  let filtered = refundRequests.value
  
  if (statusFilter.value) {
    filtered = filtered.filter(r => r.status === statusFilter.value)
  }
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(r => 
      r.order_id.toLowerCase().includes(keyword) ||
      r.user_name.toLowerCase().includes(keyword)
    )
  }
  
  if (dateRange.value) {
    filtered = filtered.filter(r => 
      r.apply_time >= dateRange.value![0] && r.apply_time <= dateRange.value![1]
    )
  }
  
  return filtered
})

// 工具函数
const formatMoney = (amount: number) => {
  return amount.toLocaleString()
}

const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    completed: '已完成'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    completed: 'info'
  }
  return statusMap[status] || 'info'
}

// 方法
const loadRefundRequests = async () => {
  try {
    loading.value = true
    const requests = await financeService.getRefundRequests()
    refundRequests.value = requests
    totalRefunds.value = requests.length
  } catch (error) {
    console.error('加载退款申请失败:', error)
    ElMessage.error('加载退款申请失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  // 筛选逻辑在计算属性中处理
}

const handleViewRefund = (refund: any) => {
  selectedRefund.value = refund
  showRefundDetail.value = true
}

const handleApproveRefund = async (refund: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要批准退款申请吗？\n退款金额：¥${refund.refund_amount}`,
      '批准退款',
      {
        confirmButtonText: '确定批准',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    const success = await financeService.processRefund(refund.id, 'approve', '管理员批准退款')
    
    if (success) {
      ElMessage.success('退款申请已批准')
      loadRefundRequests()
      emit('refresh')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleRejectRefund = async (refund: any) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      '请输入拒绝理由：',
      '拒绝退款',
      {
        confirmButtonText: '确定拒绝',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入拒绝理由...'
      }
    )
    
    const success = await financeService.processRefund(refund.id, 'reject', reason)
    
    if (success) {
      ElMessage.success('退款申请已拒绝')
      loadRefundRequests()
      emit('refresh')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleCompleteRefund = async (refund: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要完成退款吗？\n退款金额：¥${refund.refund_amount}\n\n请确保已通过第三方支付平台完成实际退款操作。`,
      '完成退款',
      {
        confirmButtonText: '确定完成',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里应该调用实际的退款完成接口
    refund.status = 'completed'
    refund.process_time = new Date().toISOString()
    
    ElMessage.success('退款已完成')
    emit('refresh')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleBatchProcess = () => {
  ElMessage.info('批量处理功能开发中')
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(visible, (newVisible) => {
  if (newVisible) {
    loadRefundRequests()
  }
})
</script>

<style lang="scss" scoped>
.refund-management-container {
  min-height: 600px;

  .refund-content {
    .refund-stats {
      margin-bottom: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      .stat-item {
        text-align: center;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 4px;

          &.pending {
            color: #e6a23c;
          }

          &.approved {
            color: #67c23a;
          }

          &.completed {
            color: #409eff;
          }

          &.total {
            color: #f56c6c;
          }
        }

        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
      }
    }

    .refund-filters {
      margin-bottom: 20px;
    }

    .refund-list {
      .refund-amount {
        font-weight: 600;
        color: #f56c6c;
      }

      .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
