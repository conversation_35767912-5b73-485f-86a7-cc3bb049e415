<template>
  <div class="subscription-management-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">订阅类型管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="showAddDialog">
              <el-icon><Plus /></el-icon>
              添加订阅类型
            </el-button>
            <el-button @click="refreshData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="subscription-content">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 订阅类型管理 -->
          <el-tab-pane label="订阅类型" name="types">
            <div class="types-content">
              <!-- 统计信息 -->
              <div class="stats-overview">
                <el-row :gutter="16">
                  <el-col :span="6">
                    <div class="stat-item">
                      <div class="stat-value">{{ subscriptionStats.totalSubscriptions }}</div>
                      <div class="stat-label">总订阅数</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-item">
                      <div class="stat-value active">{{ subscriptionStats.activeSubscriptions }}</div>
                      <div class="stat-label">活跃订阅</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-item">
                      <div class="stat-value revenue">¥{{ formatMoney(subscriptionStats.totalRevenue) }}</div>
                      <div class="stat-label">总收入</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-item">
                      <div class="stat-value monthly">¥{{ formatMoney(subscriptionStats.monthlyRevenue) }}</div>
                      <div class="stat-label">本月收入</div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 订阅类型列表 -->
              <div class="types-table">
                <el-table :data="subscriptionTypes" style="width: 100%" v-loading="loading">
                  <el-table-column prop="displayName" label="订阅类型" width="120">
                    <template #default="{ row }">
                      <div class="type-name">
                        <span>{{ row.displayName }}</span>
                        <el-tag v-if="row.isRecommended" type="warning" size="small">推荐</el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="price" label="价格" width="100">
                    <template #default="{ row }">
                      <div class="price-info">
                        <span class="current-price">¥{{ row.price }}</span>
                        <span v-if="row.originalPrice" class="original-price">¥{{ row.originalPrice }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="duration" label="时长" width="80">
                    <template #default="{ row }">
                      {{ row.duration }}天
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="limitations" label="限制" width="200">
                    <template #default="{ row }">
                      <div class="limitations">
                        <div>策略: {{ row.limitations.maxStrategies === -1 ? '无限' : row.limitations.maxStrategies }}</div>
                        <div>账户: {{ row.limitations.maxRealTradingAccounts === -1 ? '无限' : row.limitations.maxRealTradingAccounts }}</div>
                      </div>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="features" label="功能特性" min-width="200">
                    <template #default="{ row }">
                      <div class="features">
                        <el-tag 
                          v-for="feature in row.features.slice(0, 3)" 
                          :key="feature" 
                          size="small" 
                          class="feature-tag"
                        >
                          {{ feature }}
                        </el-tag>
                        <span v-if="row.features.length > 3" class="more-features">
                          +{{ row.features.length - 3 }}项
                        </span>
                      </div>
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="isActive" label="状态" width="80">
                    <template #default="{ row }">
                      <el-switch 
                        v-model="row.isActive" 
                        @change="handleStatusChange(row)"
                      />
                    </template>
                  </el-table-column>
                  
                  <el-table-column prop="sortOrder" label="排序" width="80" />
                  
                  <el-table-column label="操作" width="200">
                    <template #default="{ row }">
                      <el-button type="primary" size="small" @click="editSubscriptionType(row)">
                        编辑
                      </el-button>
                      <el-button type="warning" size="small" @click="copySubscriptionType(row)">
                        复制
                      </el-button>
                      <el-button 
                        type="danger" 
                        size="small" 
                        @click="deleteSubscriptionType(row)"
                        :disabled="!canDelete(row)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 用户订阅管理 -->
          <el-tab-pane label="用户订阅" name="subscriptions">
            <div class="subscriptions-content">
              <el-table :data="userSubscriptions" style="width: 100%" v-loading="loading">
                <el-table-column prop="userName" label="用户" width="120" />
                <el-table-column prop="subscriptionTypeName" label="订阅类型" width="120" />
                <el-table-column prop="paymentAmount" label="支付金额" width="100">
                  <template #default="{ row }">
                    ¥{{ row.paymentAmount }}
                  </template>
                </el-table-column>
                <el-table-column prop="startDate" label="开始时间" width="180">
                  <template #default="{ row }">
                    {{ formatDateTime(row.startDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="endDate" label="结束时间" width="180">
                  <template #default="{ row }">
                    {{ formatDateTime(row.endDate) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="paymentStatus" label="支付状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getPaymentStatusTagType(row.paymentStatus)">
                      {{ getPaymentStatusText(row.paymentStatus) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="autoRenew" label="自动续费" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.autoRenew ? 'success' : 'info'" size="small">
                      {{ row.autoRenew ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-dropdown @command="handleSubscriptionAction">
                      <el-button type="primary" size="small">
                        操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="{ action: 'activate', row }">激活</el-dropdown-item>
                          <el-dropdown-item :command="{ action: 'cancel', row }">取消</el-dropdown-item>
                          <el-dropdown-item :command="{ action: 'extend', row }">延期</el-dropdown-item>
                          <el-dropdown-item :command="{ action: 'refund', row }">退款</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
    
    <!-- 订阅类型编辑对话框 -->
    <SubscriptionTypeDialog 
      v-model="showDialog"
      :subscription-type="currentSubscriptionType"
      @refresh="refreshData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { subscriptionService, type SubscriptionType, type UserSubscription, type SubscriptionStats } from '@/fuwu/subscriptionService'
import SubscriptionTypeDialog from './zujian/SubscriptionTypeDialog.vue'

// 响应式数据
const activeTab = ref('types')
const loading = ref(false)
const showDialog = ref(false)
const currentSubscriptionType = ref<SubscriptionType | null>(null)

const subscriptionTypes = ref<SubscriptionType[]>([])
const userSubscriptions = ref<UserSubscription[]>([])
const subscriptionStats = ref<SubscriptionStats>({
  totalSubscriptions: 0,
  activeSubscriptions: 0,
  expiredSubscriptions: 0,
  totalRevenue: 0,
  monthlyRevenue: 0,
  conversionRate: 0,
  churnRate: 0,
  averageLifetime: 0
})

// 工具函数
const formatMoney = (amount: number) => {
  return amount.toLocaleString()
}

const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '活跃',
    expired: '已过期',
    cancelled: '已取消',
    pending: '待激活'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    expired: 'danger',
    cancelled: 'info',
    pending: 'warning'
  }
  return statusMap[status] || 'info'
}

const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    paid: '已支付',
    pending: '待支付',
    failed: '支付失败',
    refunded: '已退款'
  }
  return statusMap[status] || '未知'
}

const getPaymentStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    paid: 'success',
    pending: 'warning',
    failed: 'danger',
    refunded: 'info'
  }
  return statusMap[status] || 'info'
}

const canDelete = (subscriptionType: SubscriptionType) => {
  // 检查是否有活跃用户使用此订阅类型
  return !userSubscriptions.value.some(
    sub => sub.subscriptionTypeId === subscriptionType.id && sub.status === 'active'
  )
}

// 数据加载
const loadSubscriptionTypes = async () => {
  try {
    const types = await subscriptionService.getSubscriptionTypes()
    subscriptionTypes.value = types
  } catch (error) {
    console.error('加载订阅类型失败:', error)
    ElMessage.error('加载订阅类型失败')
  }
}

const loadUserSubscriptions = async () => {
  try {
    const subscriptions = await subscriptionService.getUserSubscriptions()
    userSubscriptions.value = subscriptions
  } catch (error) {
    console.error('加载用户订阅失败:', error)
    ElMessage.error('加载用户订阅失败')
  }
}

const loadSubscriptionStats = async () => {
  try {
    const stats = await subscriptionService.getSubscriptionStats()
    subscriptionStats.value = stats
  } catch (error) {
    console.error('加载订阅统计失败:', error)
    ElMessage.error('加载订阅统计失败')
  }
}

const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadSubscriptionTypes(),
      loadUserSubscriptions(),
      loadSubscriptionStats()
    ])
  } finally {
    loading.value = false
  }
}

// 订阅类型管理
const showAddDialog = () => {
  currentSubscriptionType.value = null
  showDialog.value = true
}

const editSubscriptionType = (subscriptionType: SubscriptionType) => {
  currentSubscriptionType.value = subscriptionType
  showDialog.value = true
}

const copySubscriptionType = (subscriptionType: SubscriptionType) => {
  const copy = {
    ...subscriptionType,
    name: `${subscriptionType.name}_copy`,
    displayName: `${subscriptionType.displayName} (副本)`,
    isRecommended: false,
    sortOrder: subscriptionType.sortOrder + 1
  }
  delete (copy as any).id
  delete (copy as any).createTime
  delete (copy as any).updateTime
  
  currentSubscriptionType.value = copy as SubscriptionType
  showDialog.value = true
}

const deleteSubscriptionType = async (subscriptionType: SubscriptionType) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除订阅类型 "${subscriptionType.displayName}" 吗？此操作不可恢复。`,
      '删除订阅类型',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await subscriptionService.deleteSubscriptionType(subscriptionType.id)
    if (success) {
      ElMessage.success('订阅类型删除成功')
      await refreshData()
    } else {
      ElMessage.error('删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除订阅类型失败:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const handleStatusChange = async (subscriptionType: SubscriptionType) => {
  try {
    const success = await subscriptionService.updateSubscriptionType(subscriptionType.id, {
      isActive: subscriptionType.isActive
    })
    
    if (success) {
      ElMessage.success(`订阅类型已${subscriptionType.isActive ? '启用' : '禁用'}`)
    } else {
      // 回滚状态
      subscriptionType.isActive = !subscriptionType.isActive
      ElMessage.error('状态更新失败')
    }
  } catch (error) {
    // 回滚状态
    subscriptionType.isActive = !subscriptionType.isActive
    console.error('更新订阅类型状态失败:', error)
    ElMessage.error('状态更新失败')
  }
}

// 用户订阅管理
const handleSubscriptionAction = async ({ action, row }: { action: string, row: UserSubscription }) => {
  try {
    let newStatus: UserSubscription['status']
    let message = ''
    
    switch (action) {
      case 'activate':
        newStatus = 'active'
        message = '订阅已激活'
        break
      case 'cancel':
        newStatus = 'cancelled'
        message = '订阅已取消'
        break
      case 'extend':
        // 这里可以打开延期对话框
        ElMessage.info('延期功能开发中...')
        return
      case 'refund':
        // 这里可以处理退款逻辑
        ElMessage.info('退款功能开发中...')
        return
      default:
        return
    }
    
    await ElMessageBox.confirm(
      `确定要${message.replace('已', '')}用户 "${row.userName}" 的订阅吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await subscriptionService.updateUserSubscriptionStatus(row.id, newStatus)
    if (success) {
      ElMessage.success(message)
      await refreshData()
    } else {
      ElMessage.error('操作失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('订阅操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.subscription-management-container {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.subscription-content {
  .stats-overview {
    margin-bottom: 20px;
    
    .stat-item {
      text-align: center;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
        
        &.active {
          color: #67c23a;
        }
        
        &.revenue {
          color: #e6a23c;
        }
        
        &.monthly {
          color: #409eff;
        }
      }
      
      .stat-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
  
  .types-table {
    .type-name {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .price-info {
      .current-price {
        font-weight: 600;
        color: var(--el-color-primary);
      }
      
      .original-price {
        font-size: 12px;
        color: var(--el-text-color-placeholder);
        text-decoration: line-through;
        margin-left: 4px;
      }
    }
    
    .limitations {
      font-size: 12px;
      color: var(--el-text-color-regular);
      
      div {
        margin-bottom: 2px;
      }
    }
    
    .features {
      .feature-tag {
        margin-right: 4px;
        margin-bottom: 4px;
      }
      
      .more-features {
        font-size: 12px;
        color: var(--el-text-color-placeholder);
      }
    }
  }
}
</style>
