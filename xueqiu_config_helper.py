#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
雪球配置助手
帮助用户正确配置雪球模拟交易
"""

import json
import requests
import re

def get_xueqiu_portfolio_info():
    """获取雪球组合信息的指导"""
    print("=" * 60)
    print("🔧 雪球配置助手")
    print("=" * 60)
    
    print("\n📋 配置雪球模拟交易需要以下信息：")
    print("1. 雪球账号（手机号）")
    print("2. 雪球密码")
    print("3. 组合代码（ZH开头的代码）")
    print("4. 登录Cookies")
    
    print("\n🎯 获取组合代码的步骤：")
    print("1. 登录雪球网站：https://xueqiu.com")
    print("2. 进入「我的组合」页面")
    print("3. 创建或选择一个模拟组合")
    print("4. 从组合URL中获取代码，格式如：ZH123456")
    
    print("\n🍪 获取Cookies的步骤：")
    print("1. 在雪球网站按F12打开开发者工具")
    print("2. 点击Application标签 → Storage → Cookies")
    print("3. 点击https://xueqiu.com")
    print("4. 复制以下cookie的值：")
    print("   - xq_a_token")
    print("   - xq_r_token") 
    print("   - u")
    print("   - s")
    
    return input_config()

def input_config():
    """交互式输入配置"""
    print("\n" + "=" * 40)
    print("📝 请输入配置信息：")
    print("=" * 40)
    
    config = {}
    
    # 输入账号信息
    config['account'] = input("雪球账号（手机号）: ").strip()
    config['password'] = input("雪球密码: ").strip()
    
    # 输入组合代码
    while True:
        portfolio_code = input("组合代码（如ZH123456）: ").strip()
        if portfolio_code.startswith('ZH') and len(portfolio_code) >= 8:
            config['portfolio_code'] = portfolio_code
            break
        else:
            print("❌ 组合代码格式错误，应该以ZH开头，如：ZH123456")
    
    # 输入Cookies
    print("\n🍪 请输入Cookies信息：")
    print("格式：xq_a_token=xxx; xq_r_token=xxx; u=xxx; s=xxx")
    
    while True:
        cookies_input = input("Cookies: ").strip()
        if validate_cookies(cookies_input):
            config['cookies'] = cookies_input
            break
        else:
            print("❌ Cookies格式错误，请确保包含xq_a_token, xq_r_token, u, s")
    
    return config

def validate_cookies(cookies_str):
    """验证cookies格式"""
    required_cookies = ['xq_a_token', 'xq_r_token', 'u', 's']
    
    for cookie_name in required_cookies:
        if cookie_name not in cookies_str:
            return False
    
    return True

def save_config(config):
    """保存配置到文件"""
    try:
        with open('xq.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("\n✅ 配置已保存到 xq.json")
        print("📄 配置内容：")
        print(json.dumps(config, indent=2, ensure_ascii=False))
        
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {str(e)}")
        return False

def test_config():
    """测试配置是否有效"""
    print("\n🧪 测试配置...")
    
    try:
        import easytrader
        
        # 创建雪球交易用户
        user = easytrader.use('xq')
        user.prepare('xq.json')
        
        # 测试获取账户信息
        balance = user.balance
        print("✅ 账户连接成功！")
        print(f"账户信息: {balance}")
        
        # 测试获取持仓
        try:
            position = user.position
            print("✅ 持仓信息获取成功！")
            print(f"持仓数量: {len(position) if position else 0}")
        except Exception as e:
            print(f"⚠️ 持仓信息获取失败: {str(e)}")
            print("这可能是因为组合代码不正确或组合为空")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")
        print("\n可能的原因：")
        print("1. 组合代码不正确")
        print("2. Cookies已过期")
        print("3. 网络连接问题")
        print("4. 雪球账户问题")
        return False

def show_current_config():
    """显示当前配置"""
    try:
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("\n📄 当前配置：")
        print("=" * 40)
        print(f"账号: {config.get('account', '未设置')}")
        print(f"密码: {'*' * len(config.get('password', '')) if config.get('password') else '未设置'}")
        print(f"组合代码: {config.get('portfolio_code', '未设置')}")
        print(f"Cookies: {'已设置' if config.get('cookies') else '未设置'}")
        
        return config
    except FileNotFoundError:
        print("❌ 配置文件 xq.json 不存在")
        return None
    except Exception as e:
        print(f"❌ 读取配置失败: {str(e)}")
        return None

def main():
    """主函数"""
    print("🚀 雪球配置助手")
    print("帮助你正确配置雪球模拟交易")
    
    while True:
        print("\n" + "=" * 50)
        print("请选择操作：")
        print("1. 查看当前配置")
        print("2. 重新配置")
        print("3. 测试配置")
        print("4. 获取配置帮助")
        print("5. 退出")
        print("=" * 50)
        
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == '1':
            show_current_config()
            
        elif choice == '2':
            config = get_xueqiu_portfolio_info()
            if save_config(config):
                print("\n是否测试新配置？(y/n)")
                if input().lower() == 'y':
                    test_config()
                    
        elif choice == '3':
            test_config()
            
        elif choice == '4':
            get_xueqiu_portfolio_info()
            
        elif choice == '5':
            print("👋 再见！")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == '__main__':
    main()
