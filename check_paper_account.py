#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查模拟账户模块状态
"""

import sys
import importlib

def check_paper_account_module():
    """检查模拟账户模块"""
    print("🔍 检查模拟账户模块状态")
    print("=" * 50)
    
    # 检查模块是否已安装
    try:
        import vnpy_paperaccount
        print("✅ vnpy_paperaccount 模块已安装")
        print(f"   版本: {getattr(vnpy_paperaccount, '__version__', '未知')}")
        print(f"   路径: {vnpy_paperaccount.__file__}")
    except ImportError as e:
        print(f"❌ vnpy_paperaccount 模块未安装: {e}")
        return False
    
    # 检查应用类
    try:
        from vnpy_paperaccount import PaperAccountApp
        print("✅ PaperAccountApp 类可用")
        print(f"   应用名称: {PaperAccountApp.app_name}")
        print(f"   应用模块: {PaperAccountApp.app_module}")
    except ImportError as e:
        print(f"❌ PaperAccountApp 导入失败: {e}")
        return False
    
    # 检查引擎类
    try:
        from vnpy_paperaccount.engine import PaperEngine
        print("✅ PaperEngine 引擎可用")
    except ImportError as e:
        print(f"❌ PaperEngine 导入失败: {e}")
        return False
    
    # 检查UI组件
    try:
        from vnpy_paperaccount.ui import PaperAccountWidget
        print("✅ PaperAccountWidget UI组件可用")
    except ImportError as e:
        print(f"❌ PaperAccountWidget 导入失败: {e}")
        return False
    
    return True

def check_vnpy_apps():
    """检查VeighNa中已注册的应用"""
    print("\n📱 检查VeighNa已注册的应用")
    print("=" * 50)
    
    try:
        from vnpy.trader.engine import MainEngine
        from vnpy.event import EventEngine
        
        # 创建临时引擎来检查应用
        event_engine = EventEngine()
        main_engine = MainEngine(event_engine)
        
        # 尝试添加模拟账户应用
        try:
            from vnpy_paperaccount import PaperAccountApp
            main_engine.add_app(PaperAccountApp)
            print("✅ 模拟账户应用成功添加到主引擎")
            
            # 检查应用是否在引擎中
            if "PaperAccount" in main_engine.engines:
                print("✅ 模拟账户引擎已在主引擎中注册")
            else:
                print("⚠️ 模拟账户引擎未在主引擎中找到")
                
        except Exception as e:
            print(f"❌ 添加模拟账户应用失败: {e}")
            return False
        
        # 清理
        event_engine.stop()
        
    except Exception as e:
        print(f"❌ 检查VeighNa应用失败: {e}")
        return False
    
    return True

def check_dependencies():
    """检查依赖项"""
    print("\n🔧 检查依赖项")
    print("=" * 50)
    
    dependencies = [
        "vnpy",
        "pandas", 
        "numpy",
        "importlib_metadata"
    ]
    
    all_ok = True
    
    for dep in dependencies:
        try:
            module = importlib.import_module(dep)
            version = getattr(module, '__version__', '未知')
            print(f"✅ {dep}: {version}")
        except ImportError:
            print(f"❌ {dep}: 未安装")
            all_ok = False
    
    return all_ok

def provide_solutions():
    """提供解决方案"""
    print("\n💡 解决方案")
    print("=" * 50)
    
    print("如果模拟账户模块有问题，请尝试：")
    print()
    print("1. 重新安装模拟账户模块:")
    print("   pip uninstall vnpy-paperaccount")
    print("   pip install vnpy-paperaccount")
    print()
    print("2. 检查VeighNa版本兼容性:")
    print("   pip install --upgrade vnpy")
    print()
    print("3. 清理Python缓存:")
    print("   python -m pip cache purge")
    print()
    print("4. 使用替代方案:")
    print("   - 使用CTA回测模块进行策略测试")
    print("   - 申请CTP SimNow模拟账户")
    print("   - 使用券商模拟环境")

def main():
    """主函数"""
    print("🎯 VeighNa模拟账户模块检查工具")
    print("=" * 80)
    
    # 检查模拟账户模块
    paper_ok = check_paper_account_module()
    
    # 检查VeighNa应用注册
    apps_ok = check_vnpy_apps()
    
    # 检查依赖项
    deps_ok = check_dependencies()
    
    # 总结
    print("\n📊 检查结果总结")
    print("=" * 50)
    
    if paper_ok and apps_ok and deps_ok:
        print("✅ 模拟账户模块完全正常")
        print("💡 如果在VeighNa界面中找不到模拟账户:")
        print("   1. 检查 功能 → 模拟账户")
        print("   2. 检查 系统 → 应用管理")
        print("   3. 查看左侧应用面板")
        print("   4. 重启VeighNa系统")
    else:
        print("❌ 模拟账户模块存在问题")
        provide_solutions()
    
    print(f"\n🔍 详细查找指南请查看: 找到模拟账户设置.md")

if __name__ == "__main__":
    main()
