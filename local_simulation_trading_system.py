#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
本地模拟交易系统
基于真实雪球行情数据，创建完全本地的模拟交易功能
支持多个模拟账户，包括"测试"和"test"
"""

from flask import Flask, request, jsonify
import json
import logging
import pysnowball as ball
from datetime import datetime
import sqlite3
import os

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

class LocalSimulationTradingSystem:
    """本地模拟交易系统"""
    
    def __init__(self, db_file='simulation_trading.db'):
        self.db_file = db_file
        self.load_config()
        self.init_database()
        self.create_default_accounts()
    
    def load_config(self):
        """加载雪球配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logging.info(f"✅ 雪球Token设置成功")
            
        except Exception as e:
            logging.error(f"❌ 加载配置失败: {e}")
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 创建模拟账户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS simulation_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_name TEXT UNIQUE NOT NULL,
                initial_cash REAL DEFAULT 1000000.0,
                current_cash REAL DEFAULT 1000000.0,
                total_asset REAL DEFAULT 1000000.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建持仓表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_name TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                stock_name TEXT,
                shares INTEGER DEFAULT 0,
                avg_cost REAL DEFAULT 0.0,
                total_cost REAL DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(account_name, stock_code)
            )
        ''')
        
        # 创建交易记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_name TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                stock_name TEXT,
                action TEXT NOT NULL,  -- 'buy' or 'sell'
                shares INTEGER NOT NULL,
                price REAL NOT NULL,
                amount REAL NOT NULL,
                commission REAL DEFAULT 0.0,
                trade_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        logging.info("✅ 数据库初始化完成")
    
    def create_default_accounts(self):
        """创建默认的模拟账户"""
        default_accounts = [
            {"name": "测试", "cash": 1000000.0},
            {"name": "test", "cash": 1000000.0},
            {"name": "模拟账户1", "cash": 500000.0},
            {"name": "模拟账户2", "cash": 2000000.0},
        ]
        
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        for account in default_accounts:
            try:
                cursor.execute('''
                    INSERT OR IGNORE INTO simulation_accounts 
                    (account_name, initial_cash, current_cash, total_asset) 
                    VALUES (?, ?, ?, ?)
                ''', (account["name"], account["cash"], account["cash"], account["cash"]))
            except Exception as e:
                logging.warning(f"创建账户 {account['name']} 失败: {e}")
        
        conn.commit()
        conn.close()
        logging.info("✅ 默认模拟账户创建完成")
    
    def get_all_accounts(self):
        """获取所有模拟账户"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT account_name, current_cash, total_asset, 
                   (SELECT COUNT(*) FROM positions WHERE account_name = sa.account_name AND shares > 0) as position_count
            FROM simulation_accounts sa
        ''')
        
        accounts = []
        for row in cursor.fetchall():
            accounts.append({
                'account_name': row[0],
                'current_cash': row[1],
                'total_asset': row[2],
                'position_count': row[3]
            })
        
        conn.close()
        return accounts
    
    def get_account_detail(self, account_name):
        """获取账户详情"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # 获取账户基本信息
        cursor.execute('''
            SELECT account_name, current_cash, total_asset, initial_cash
            FROM simulation_accounts 
            WHERE account_name = ?
        ''', (account_name,))
        
        account_info = cursor.fetchone()
        if not account_info:
            conn.close()
            return None
        
        # 获取持仓信息
        cursor.execute('''
            SELECT stock_code, stock_name, shares, avg_cost, total_cost
            FROM positions 
            WHERE account_name = ? AND shares > 0
        ''', (account_name,))
        
        positions = []
        total_market_value = 0
        
        for row in cursor.fetchall():
            stock_code, stock_name, shares, avg_cost, total_cost = row
            
            # 获取实时行情
            try:
                quote = ball.quotec(stock_code)
                if quote and 'data' in quote and quote['data']:
                    current_price = quote['data'][0].get('current', avg_cost)
                    chg = quote['data'][0].get('chg', 0)
                    percent = quote['data'][0].get('percent', 0)
                else:
                    current_price = avg_cost
                    chg = 0
                    percent = 0
            except:
                current_price = avg_cost
                chg = 0
                percent = 0
            
            current_value = current_price * shares
            pnl = current_value - total_cost
            pnl_percent = (pnl / total_cost * 100) if total_cost > 0 else 0
            
            total_market_value += current_value
            
            positions.append({
                'stock_code': stock_code,
                'stock_name': stock_name,
                'shares': shares,
                'avg_cost': avg_cost,
                'current_price': current_price,
                'total_cost': total_cost,
                'current_value': current_value,
                'pnl': pnl,
                'pnl_percent': pnl_percent,
                'chg': chg,
                'percent': percent
            })
        
        # 更新总资产
        new_total_asset = account_info[1] + total_market_value  # 现金 + 市值
        cursor.execute('''
            UPDATE simulation_accounts 
            SET total_asset = ?, updated_at = CURRENT_TIMESTAMP
            WHERE account_name = ?
        ''', (new_total_asset, account_name))
        
        conn.commit()
        conn.close()
        
        return {
            'account_name': account_info[0],
            'current_cash': account_info[1],
            'total_asset': new_total_asset,
            'initial_cash': account_info[3],
            'market_value': total_market_value,
            'total_pnl': new_total_asset - account_info[3],
            'total_pnl_percent': ((new_total_asset - account_info[3]) / account_info[3] * 100) if account_info[3] > 0 else 0,
            'positions': positions
        }
    
    def execute_trade(self, account_name, stock_code, action, shares, price):
        """执行交易"""
        try:
            # 获取股票名称
            try:
                quote = ball.quotec(stock_code)
                if quote and 'data' in quote and quote['data']:
                    stock_name = quote['data'][0].get('name', stock_code)
                else:
                    stock_name = stock_code
            except:
                stock_name = stock_code
            
            amount = shares * price
            commission = amount * 0.0003  # 0.03% 手续费
            
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            if action == 'buy':
                # 检查资金是否足够
                cursor.execute('SELECT current_cash FROM simulation_accounts WHERE account_name = ?', (account_name,))
                cash_result = cursor.fetchone()
                if not cash_result or cash_result[0] < (amount + commission):
                    conn.close()
                    return {'success': False, 'error': '资金不足'}
                
                # 更新现金
                new_cash = cash_result[0] - amount - commission
                cursor.execute('''
                    UPDATE simulation_accounts 
                    SET current_cash = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE account_name = ?
                ''', (new_cash, account_name))
                
                # 更新持仓
                cursor.execute('''
                    SELECT shares, total_cost FROM positions 
                    WHERE account_name = ? AND stock_code = ?
                ''', (account_name, stock_code))
                
                position_result = cursor.fetchone()
                if position_result:
                    # 更新现有持仓
                    old_shares, old_cost = position_result
                    new_shares = old_shares + shares
                    new_total_cost = old_cost + amount
                    new_avg_cost = new_total_cost / new_shares
                    
                    cursor.execute('''
                        UPDATE positions 
                        SET shares = ?, avg_cost = ?, total_cost = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE account_name = ? AND stock_code = ?
                    ''', (new_shares, new_avg_cost, new_total_cost, account_name, stock_code))
                else:
                    # 新建持仓
                    cursor.execute('''
                        INSERT INTO positions 
                        (account_name, stock_code, stock_name, shares, avg_cost, total_cost)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (account_name, stock_code, stock_name, shares, price, amount))
            
            elif action == 'sell':
                # 检查持仓是否足够
                cursor.execute('''
                    SELECT shares, avg_cost, total_cost FROM positions 
                    WHERE account_name = ? AND stock_code = ?
                ''', (account_name, stock_code))
                
                position_result = cursor.fetchone()
                if not position_result or position_result[0] < shares:
                    conn.close()
                    return {'success': False, 'error': '持仓不足'}
                
                old_shares, avg_cost, old_total_cost = position_result
                
                # 更新现金
                cursor.execute('SELECT current_cash FROM simulation_accounts WHERE account_name = ?', (account_name,))
                cash_result = cursor.fetchone()
                new_cash = cash_result[0] + amount - commission
                cursor.execute('''
                    UPDATE simulation_accounts 
                    SET current_cash = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE account_name = ?
                ''', (new_cash, account_name))
                
                # 更新持仓
                new_shares = old_shares - shares
                if new_shares > 0:
                    new_total_cost = old_total_cost * (new_shares / old_shares)
                    cursor.execute('''
                        UPDATE positions 
                        SET shares = ?, total_cost = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE account_name = ? AND stock_code = ?
                    ''', (new_shares, new_total_cost, account_name, stock_code))
                else:
                    # 清空持仓
                    cursor.execute('''
                        UPDATE positions 
                        SET shares = 0, total_cost = 0, updated_at = CURRENT_TIMESTAMP
                        WHERE account_name = ? AND stock_code = ?
                    ''', (account_name, stock_code))
            
            # 记录交易
            cursor.execute('''
                INSERT INTO trades 
                (account_name, stock_code, stock_name, action, shares, price, amount, commission)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (account_name, stock_code, stock_name, action, shares, price, amount, commission))
            
            conn.commit()
            conn.close()
            
            return {'success': True, 'message': f'{action}成功', 'commission': commission}
            
        except Exception as e:
            logging.error(f"交易执行失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_trade_history(self, account_name, limit=50):
        """获取交易历史"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT stock_code, stock_name, action, shares, price, amount, commission, trade_time
            FROM trades 
            WHERE account_name = ?
            ORDER BY trade_time DESC
            LIMIT ?
        ''', (account_name, limit))
        
        trades = []
        for row in cursor.fetchall():
            trades.append({
                'stock_code': row[0],
                'stock_name': row[1],
                'action': row[2],
                'shares': row[3],
                'price': row[4],
                'amount': row[5],
                'commission': row[6],
                'trade_time': row[7]
            })
        
        conn.close()
        return trades

# 全局系统实例
trading_system = LocalSimulationTradingSystem()

@app.route('/')
def index():
    """主页"""
    return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地模拟交易系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            color: #333;
        }
        .container { 
            max-width: 1600px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header { 
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4); 
            color: white; 
            padding: 20px; 
            border-radius: 15px; 
            text-align: center; 
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .account-selector {
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .account-selector select {
            padding: 10px 20px;
            border-radius: 5px;
            border: 2px solid #ddd;
            font-size: 16px;
            margin: 0 10px;
        }
        .trading-grid { 
            display: grid; 
            grid-template-columns: 2fr 1fr 1fr; 
            gap: 20px; 
            margin-bottom: 20px;
        }
        .panel { 
            background: rgba(255,255,255,0.95); 
            border-radius: 15px; 
            padding: 20px; 
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .panel h3 { 
            color: #2c3e50; 
            margin-bottom: 15px; 
            padding-bottom: 10px; 
            border-bottom: 3px solid #3498db;
            font-size: 18px;
        }
        .btn { 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 14px; 
            font-weight: bold;
            transition: all 0.3s;
            margin: 5px;
        }
        .btn-primary { background: linear-gradient(45deg, #3498db, #2980b9); color: white; }
        .btn-success { background: linear-gradient(45deg, #2ecc71, #27ae60); color: white; }
        .btn-danger { background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0,0,0,0.2); }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            margin-top: 5px;
        }
        
        .holdings-table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 15px; 
            font-size: 12px;
        }
        .holdings-table th, .holdings-table td { 
            padding: 8px 4px; 
            text-align: center; 
            border-bottom: 1px solid #eee; 
        }
        .holdings-table th { 
            background: linear-gradient(45deg, #34495e, #2c3e50); 
            color: white; 
            font-weight: bold;
            font-size: 11px;
        }
        .holdings-table tr:hover { background: #f8f9fa; }
        
        .price-up { color: #e74c3c; font-weight: bold; }
        .price-down { color: #27ae60; font-weight: bold; }
        .price-flat { color: #7f8c8d; }
        
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; }
        .form-group input, .form-group select { 
            width: 100%; 
            padding: 12px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            font-size: 14px;
        }
        
        .alert { 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 8px; 
            display: none;
        }
        .alert.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 本地模拟交易系统</h1>
            <p>基于真实雪球行情数据的完全本地模拟交易</p>
            <div>
                <span style="color: #2ecc71;">●</span>
                <span>支持多账户 | 真实行情 | 完整交易记录</span>
            </div>
        </div>
        
        <div class="account-selector">
            <label for="accountSelect"><strong>选择模拟账户:</strong></label>
            <select id="accountSelect" onchange="switchAccount()">
                <option value="测试">测试账户</option>
                <option value="test">test账户</option>
                <option value="模拟账户1">模拟账户1</option>
                <option value="模拟账户2">模拟账户2</option>
            </select>
            <button class="btn btn-primary" onclick="refreshData()">刷新数据</button>
            <button class="btn btn-success" onclick="showAllAccounts()">查看所有账户</button>
        </div>
        
        <div class="trading-grid">
            <!-- 左侧：账户详情和持仓 -->
            <div class="panel">
                <h3>💼 账户详情和持仓</h3>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div>总资产</div>
                        <div class="stat-value" id="totalAsset">--</div>
                    </div>
                    <div class="stat-item">
                        <div>可用资金</div>
                        <div class="stat-value" id="availableCash">--</div>
                    </div>
                    <div class="stat-item">
                        <div>持仓市值</div>
                        <div class="stat-value" id="marketValue">--</div>
                    </div>
                    <div class="stat-item">
                        <div>总盈亏</div>
                        <div class="stat-value" id="totalPnL">--</div>
                    </div>
                </div>
                
                <div id="holdingsContainer">
                    <div style="text-align: center; padding: 20px; color: #7f8c8d;">加载中...</div>
                </div>
            </div>
            
            <!-- 中间：行情查看 -->
            <div class="panel">
                <h3>📊 实时行情</h3>
                
                <div class="form-group">
                    <label>股票代码:</label>
                    <input type="text" id="stockCode" placeholder="如: SH600519" value="SH600507">
                    <button class="btn btn-primary" onclick="loadQuote()">查看行情</button>
                </div>
                
                <div id="quoteDisplay">
                    <div style="text-align: center; padding: 20px; color: #7f8c8d;">请输入股票代码</div>
                </div>
            </div>
            
            <!-- 右侧：模拟交易 -->
            <div class="panel">
                <h3>💰 模拟交易</h3>
                
                <div style="background: #d1ecf1; padding: 10px; border-radius: 5px; margin-bottom: 15px; font-size: 12px;">
                    <strong>💡 说明:</strong> 这是本地模拟交易，使用真实行情数据，不影响真实账户
                </div>
                
                <div class="form-group">
                    <label>股票代码:</label>
                    <input type="text" id="tradeStock" placeholder="股票代码">
                </div>
                
                <div class="form-group">
                    <label>交易类型:</label>
                    <select id="tradeAction">
                        <option value="buy">买入</option>
                        <option value="sell">卖出</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>数量:</label>
                    <input type="number" id="tradeAmount" placeholder="股数" min="100" step="100">
                </div>
                
                <div class="form-group">
                    <label>价格:</label>
                    <input type="number" id="tradePrice" placeholder="价格" step="0.01">
                </div>
                
                <div class="form-group">
                    <button class="btn btn-success" onclick="submitTrade()">提交交易</button>
                    <button class="btn btn-danger" onclick="clearTradeForm()">清空</button>
                </div>
            </div>
        </div>
        
        <div id="alertBox" class="alert"></div>
    </div>

    <script>
        let currentAccount = '测试';
        
        function showAlert(message, type = 'info') {
            const alertBox = document.getElementById('alertBox');
            alertBox.className = `alert ${type}`;
            alertBox.textContent = message;
            alertBox.style.display = 'block';
            
            setTimeout(() => {
                alertBox.style.display = 'none';
            }, 5000);
        }
        
        function switchAccount() {
            const select = document.getElementById('accountSelect');
            currentAccount = select.value;
            showAlert(`已切换到账户: ${currentAccount}`, 'success');
            refreshData();
        }
        
        function refreshData() {
            fetch(`/api/account/${currentAccount}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateAccountDisplay(data.data);
                        showAlert('数据刷新成功', 'success');
                    } else {
                        showAlert(`刷新失败: ${data.error}`, 'error');
                    }
                });
        }
        
        function updateAccountDisplay(accountData) {
            // 更新统计信息
            document.getElementById('totalAsset').textContent = `¥${accountData.total_asset.toLocaleString()}`;
            document.getElementById('availableCash').textContent = `¥${accountData.current_cash.toLocaleString()}`;
            document.getElementById('marketValue').textContent = `¥${accountData.market_value.toLocaleString()}`;
            
            const pnlClass = accountData.total_pnl > 0 ? 'price-up' : accountData.total_pnl < 0 ? 'price-down' : 'price-flat';
            document.getElementById('totalPnL').innerHTML = `<span class="${pnlClass}">¥${accountData.total_pnl.toLocaleString()} (${accountData.total_pnl_percent.toFixed(2)}%)</span>`;
            
            // 更新持仓表格
            const container = document.getElementById('holdingsContainer');
            
            if (accountData.positions.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 20px; color: #7f8c8d;">暂无持仓</div>';
                return;
            }
            
            let html = '<table class="holdings-table">';
            html += '<tr><th>股票</th><th>代码</th><th>数量</th><th>成本价</th><th>现价</th><th>市值</th><th>盈亏</th><th>涨跌幅</th></tr>';
            
            accountData.positions.forEach(pos => {
                const pnlClass = pos.pnl > 0 ? 'price-up' : pos.pnl < 0 ? 'price-down' : 'price-flat';
                const percentClass = pos.percent > 0 ? 'price-up' : pos.percent < 0 ? 'price-down' : 'price-flat';
                
                html += '<tr>';
                html += `<td><strong>${pos.stock_name}</strong></td>`;
                html += `<td>${pos.stock_code}</td>`;
                html += `<td>${pos.shares}</td>`;
                html += `<td>¥${pos.avg_cost.toFixed(2)}</td>`;
                html += `<td class="${percentClass}">¥${pos.current_price.toFixed(2)}</td>`;
                html += `<td>¥${pos.current_value.toLocaleString()}</td>`;
                html += `<td class="${pnlClass}">¥${pos.pnl.toFixed(2)}<br><small>(${pos.pnl_percent.toFixed(2)}%)</small></td>`;
                html += `<td class="${percentClass}">${pos.percent.toFixed(2)}%</td>`;
                html += '</tr>';
            });
            
            html += '</table>';
            container.innerHTML = html;
        }
        
        function loadQuote() {
            const stockCode = document.getElementById('stockCode').value;
            if (!stockCode) {
                showAlert('请输入股票代码', 'error');
                return;
            }
            
            fetch(`/api/quote/${stockCode}`)
                .then(response => response.json())
                .then(data => {
                    const quoteDiv = document.getElementById('quoteDisplay');
                    
                    if (data.success) {
                        const quote = data.quote;
                        const priceClass = quote.percent > 0 ? 'price-up' : 
                                         quote.percent < 0 ? 'price-down' : 'price-flat';
                        
                        quoteDiv.innerHTML = `
                            <h4>${quote.name || stockCode}</h4>
                            <div style="font-size: 24px; margin: 10px 0;">
                                <span class="${priceClass}">¥${quote.current}</span>
                                <span class="${priceClass}" style="font-size: 16px;">
                                    ${quote.chg} (${quote.percent}%)
                                </span>
                            </div>
                            <div style="font-size: 12px;">
                                <div>开盘: ¥${quote.open} | 最高: ¥${quote.high}</div>
                                <div>最低: ¥${quote.low} | 昨收: ¥${quote.last_close}</div>
                                <div>成交量: ${(quote.volume / 10000).toFixed(0)}万</div>
                            </div>
                        `;
                        
                        // 自动填入交易价格
                        document.getElementById('tradeStock').value = stockCode;
                        document.getElementById('tradePrice').value = quote.current;
                        
                    } else {
                        quoteDiv.innerHTML = `<div style="color: red;">${data.error}</div>`;
                    }
                });
        }
        
        function submitTrade() {
            const stockCode = document.getElementById('tradeStock').value;
            const action = document.getElementById('tradeAction').value;
            const amount = document.getElementById('tradeAmount').value;
            const price = document.getElementById('tradePrice').value;
            
            if (!stockCode || !amount || !price) {
                showAlert('请填写完整的交易信息', 'error');
                return;
            }
            
            const tradeData = {
                account_name: currentAccount,
                stock_code: stockCode,
                action: action,
                shares: parseInt(amount),
                price: parseFloat(price)
            };
            
            fetch('/api/trade', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(tradeData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`${action === 'buy' ? '买入' : '卖出'}成功！手续费: ¥${data.commission.toFixed(2)}`, 'success');
                    clearTradeForm();
                    refreshData();
                } else {
                    showAlert(`交易失败: ${data.error}`, 'error');
                }
            });
        }
        
        function clearTradeForm() {
            document.getElementById('tradeStock').value = '';
            document.getElementById('tradeAmount').value = '';
            document.getElementById('tradePrice').value = '';
        }
        
        function showAllAccounts() {
            fetch('/api/accounts')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let summary = '所有模拟账户概览:\\n\\n';
                        data.accounts.forEach(acc => {
                            summary += `${acc.account_name}:\\n`;
                            summary += `  总资产: ¥${acc.total_asset.toLocaleString()}\\n`;
                            summary += `  现金: ¥${acc.current_cash.toLocaleString()}\\n`;
                            summary += `  持仓数: ${acc.position_count}只\\n\\n`;
                        });
                        alert(summary);
                    }
                });
        }
        
        // 页面加载时初始化
        window.onload = function() {
            refreshData();
            loadQuote();  // 默认加载方大特钢行情
        };
        
        // 定时刷新（每60秒）
        setInterval(refreshData, 60000);
    </script>
</body>
</html>
    '''

# API路由
@app.route('/api/accounts')
def get_all_accounts():
    """获取所有账户"""
    try:
        accounts = trading_system.get_all_accounts()
        return jsonify({'success': True, 'accounts': accounts})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/account/<account_name>')
def get_account_detail(account_name):
    """获取账户详情"""
    try:
        detail = trading_system.get_account_detail(account_name)
        if detail:
            return jsonify({'success': True, 'data': detail})
        else:
            return jsonify({'success': False, 'error': '账户不存在'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/quote/<symbol>')
def get_quote(symbol):
    """获取股票行情"""
    try:
        quote = ball.quotec(symbol)
        if quote and 'data' in quote and quote['data']:
            return jsonify({'success': True, 'quote': quote['data'][0]})
        else:
            return jsonify({'success': False, 'error': '无法获取行情数据'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/trade', methods=['POST'])
def execute_trade():
    """执行交易"""
    try:
        data = request.get_json()
        account_name = data.get('account_name')
        stock_code = data.get('stock_code')
        action = data.get('action')
        shares = data.get('shares')
        price = data.get('price')
        
        result = trading_system.execute_trade(account_name, stock_code, action, shares, price)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/trades/<account_name>')
def get_trade_history(account_name):
    """获取交易历史"""
    try:
        trades = trading_system.get_trade_history(account_name)
        return jsonify({'success': True, 'trades': trades})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def main():
    """主函数"""
    print("💰 本地模拟交易系统")
    print("基于真实雪球行情数据")
    print("=" * 60)
    
    print("\n🌐 启动Web服务器...")
    print("访问地址: http://localhost:5008")
    print("功能特点:")
    print("  💰 多个模拟账户 (测试、test等)")
    print("  📊 真实行情数据")
    print("  💹 完整交易功能")
    print("  📈 盈亏计算")
    print("  📋 交易记录")
    print("  💾 本地数据库存储")
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5008, debug=True)

if __name__ == '__main__':
    main()
