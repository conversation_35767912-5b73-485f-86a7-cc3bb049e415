#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多因子选股系统
基于VeighNa的量化选股工具
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class MultiFactorStockSelector:
    """多因子选股系统"""
    
    def __init__(self):
        self.factors = {
            'technical': ['rsi', 'macd', 'volume_ratio', 'price_momentum'],
            'fundamental': ['pe_ratio', 'pb_ratio', 'roe', 'revenue_growth'],
            'market': ['turnover_rate', 'market_cap', 'float_ratio'],
            'sentiment': ['institution_rating', 'fund_holding', 'news_sentiment']
        }
        
        self.hot_sectors = [
            '半导体', '新能源汽车', '人工智能', '生物医药', 
            '白酒食品', '券商', '新材料', '军工', '光伏', '储能'
        ]
    
    def get_sector_stocks(self, sector):
        """获取板块股票代码（示例数据）"""
        sector_mapping = {
            '半导体': {
                'description': '国产替代+AI芯片需求',
                'sample_codes': ['002049', '300661', '688981', '002371'],
                'keywords': ['芯片设计', '半导体设备', '封装测试', 'IC设计']
            },
            '新能源汽车': {
                'description': '产业链持续增长+技术升级',
                'sample_codes': ['300750', '002594', '300014', '002460'],
                'keywords': ['动力电池', '充电桩', '电机电控', '整车制造']
            },
            '人工智能': {
                'description': 'AI应用落地+算力需求',
                'sample_codes': ['002230', '300496', '688111', '300253'],
                'keywords': ['算力服务', 'AI芯片', '机器视觉', '语音识别']
            },
            '生物医药': {
                'description': '创新药+医疗器械+政策支持',
                'sample_codes': ['300122', '000661', '002821', '688185'],
                'keywords': ['创新药', '医疗器械', 'CRO', '疫苗']
            },
            '白酒食品': {
                'description': '消费复苏+品牌价值',
                'sample_codes': ['000858', '000568', '002304', '600519'],
                'keywords': ['白酒', '调味品', '乳制品', '休闲食品']
            }
        }
        return sector_mapping.get(sector, {})
    
    def calculate_technical_score(self, stock_data):
        """计算技术面评分"""
        score = 0
        
        # RSI评分 (30-70为正常区间)
        rsi = stock_data.get('rsi', 50)
        if 30 <= rsi <= 70:
            score += 20
        elif rsi > 70:
            score += 10  # 超买但可能继续上涨
        
        # 成交量评分
        volume_ratio = stock_data.get('volume_ratio', 1.0)
        if volume_ratio > 1.5:
            score += 20
        elif volume_ratio > 1.2:
            score += 15
        
        # 价格动量评分
        momentum = stock_data.get('momentum', 0)
        if momentum > 0.05:
            score += 20
        elif momentum > 0:
            score += 10
        
        # MACD评分
        macd = stock_data.get('macd', 0)
        if macd > 0:
            score += 20
        
        return min(score, 100)
    
    def calculate_fundamental_score(self, stock_data):
        """计算基本面评分"""
        score = 0
        
        # PE估值评分
        pe = stock_data.get('pe_ratio', 30)
        if 10 <= pe <= 25:
            score += 25
        elif 25 < pe <= 40:
            score += 15
        
        # ROE评分
        roe = stock_data.get('roe', 0.1)
        if roe > 0.15:
            score += 25
        elif roe > 0.1:
            score += 15
        
        # 营收增长评分
        revenue_growth = stock_data.get('revenue_growth', 0)
        if revenue_growth > 0.2:
            score += 25
        elif revenue_growth > 0.1:
            score += 15
        
        # PB评分
        pb = stock_data.get('pb_ratio', 3)
        if pb < 2:
            score += 25
        elif pb < 3:
            score += 15
        
        return min(score, 100)
    
    def calculate_market_score(self, stock_data):
        """计算市场面评分"""
        score = 0
        
        # 换手率评分
        turnover = stock_data.get('turnover_rate', 0.02)
        if 0.03 <= turnover <= 0.08:
            score += 30
        elif 0.08 < turnover <= 0.15:
            score += 20
        
        # 市值评分 (偏好中等市值)
        market_cap = stock_data.get('market_cap', 100)  # 亿元
        if 50 <= market_cap <= 500:
            score += 35
        elif 500 < market_cap <= 1000:
            score += 25
        
        # 流通比例评分
        float_ratio = stock_data.get('float_ratio', 0.5)
        if float_ratio > 0.7:
            score += 35
        elif float_ratio > 0.5:
            score += 25
        
        return min(score, 100)
    
    def calculate_sentiment_score(self, stock_data):
        """计算市场情绪评分"""
        score = 0
        
        # 机构评级评分
        rating = stock_data.get('institution_rating', 3)  # 1-5分
        score += rating * 20
        
        # 基金持仓评分
        fund_holding = stock_data.get('fund_holding', 0)
        if fund_holding > 0.05:
            score += 30
        elif fund_holding > 0.02:
            score += 20
        
        # 新闻情绪评分
        news_sentiment = stock_data.get('news_sentiment', 0)  # -1到1
        score += (news_sentiment + 1) * 25
        
        return min(score, 100)
    
    def comprehensive_scoring(self, stock_data):
        """综合评分"""
        weights = {
            'technical': 0.3,
            'fundamental': 0.35,
            'market': 0.2,
            'sentiment': 0.15
        }
        
        scores = {
            'technical': self.calculate_technical_score(stock_data),
            'fundamental': self.calculate_fundamental_score(stock_data),
            'market': self.calculate_market_score(stock_data),
            'sentiment': self.calculate_sentiment_score(stock_data)
        }
        
        total_score = sum(scores[factor] * weights[factor] for factor in scores)
        
        return {
            'total_score': round(total_score, 2),
            'detail_scores': scores,
            'risk_level': self.assess_risk_level(total_score)
        }
    
    def assess_risk_level(self, score):
        """评估风险等级"""
        if score >= 80:
            return "低风险"
        elif score >= 60:
            return "中等风险"
        else:
            return "高风险"
    
    def generate_stock_screening_guide(self):
        """生成选股指导"""
        print("🎯 多因子选股系统指导")
        print("=" * 80)
        
        print("\n📊 热点板块分析:")
        for sector in self.hot_sectors[:5]:
            sector_info = self.get_sector_stocks(sector)
            if sector_info:
                print(f"\n🔥 {sector}板块:")
                print(f"   投资逻辑: {sector_info['description']}")
                print(f"   关键词: {', '.join(sector_info['keywords'])}")
                print(f"   参考代码: {', '.join(sector_info['sample_codes'])}")
        
        print("\n💡 选股建议:")
        print("1. 技术面筛选:")
        print("   - RSI在30-70区间，避免极端超买超卖")
        print("   - 成交量放大1.5倍以上")
        print("   - MACD金叉向上")
        print("   - 突破重要技术位")
        
        print("\n2. 基本面筛选:")
        print("   - PE估值合理(10-25倍)")
        print("   - ROE > 15%")
        print("   - 营收增长 > 20%")
        print("   - PB < 3倍")
        
        print("\n3. 市场面筛选:")
        print("   - 换手率3%-8%(活跃但不过热)")
        print("   - 市值50-500亿(成长空间大)")
        print("   - 流通比例 > 70%")
        
        print("\n4. 情绪面筛选:")
        print("   - 机构评级较高")
        print("   - 基金持仓增加")
        print("   - 新闻情绪积极")
        
        print("\n⚠️ 风险提示:")
        print("- 以上仅为选股框架，不构成投资建议")
        print("- 需要结合实时数据进行分析")
        print("- 建议分散投资，控制风险")
        print("- 设置止损位，及时止盈")
    
    def create_screening_checklist(self):
        """创建选股检查清单"""
        checklist = {
            "技术面检查": [
                "□ RSI指标是否在合理区间",
                "□ 成交量是否放大",
                "□ 是否突破重要阻力位",
                "□ MACD是否金叉向上",
                "□ 均线系统是否多头排列"
            ],
            "基本面检查": [
                "□ PE估值是否合理",
                "□ ROE是否大于15%",
                "□ 营收增长是否超过20%",
                "□ 负债率是否合理",
                "□ 现金流是否健康"
            ],
            "行业面检查": [
                "□ 是否属于热点板块",
                "□ 行业景气度是否向上",
                "□ 政策是否支持",
                "□ 竞争格局是否良好",
                "□ 市场空间是否足够大"
            ],
            "风险控制": [
                "□ 是否设置止损位",
                "□ 仓位是否合理",
                "□ 是否分散投资",
                "□ 是否了解公司风险",
                "□ 是否制定退出策略"
            ]
        }
        
        return checklist

def main():
    """主函数"""
    selector = MultiFactorStockSelector()
    
    # 生成选股指导
    selector.generate_stock_screening_guide()
    
    # 创建检查清单
    checklist = selector.create_screening_checklist()
    
    print("\n📋 选股检查清单:")
    print("=" * 80)
    for category, items in checklist.items():
        print(f"\n{category}:")
        for item in items:
            print(f"  {item}")
    
    print("\n🔧 使用建议:")
    print("1. 使用TuShare等数据源获取实时数据")
    print("2. 结合VeighNa进行策略回测")
    print("3. 定期更新选股模型参数")
    print("4. 建立投资组合管理系统")
    
    print("\n⚠️ 最终提醒:")
    print("股市有风险，投资需谨慎！")
    print("建议咨询专业投资顾问！")

if __name__ == "__main__":
    main()
