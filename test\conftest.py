"""
测试配置文件
提供全局的pytest fixtures和配置
"""

import asyncio
import os
import tempfile
from pathlib import Path
from typing import AsyncGenerator, Generator
from unittest.mock import Mock

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# 设置测试环境变量
os.environ["TESTING"] = "1"
os.environ["DATABASE_URL"] = "sqlite:///./test.db"


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """创建事件循环用于异步测试."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def temp_dir() -> Generator[Path, None, None]:
    """创建临时目录用于测试."""
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield Path(tmp_dir)


@pytest.fixture(scope="session")
def test_db_path(temp_dir: Path) -> Path:
    """测试数据库文件路径."""
    return temp_dir / "test.db"


@pytest.fixture(scope="session")
def test_engine(test_db_path: Path):
    """创建测试数据库引擎."""
    database_url = f"sqlite:///{test_db_path}"
    engine = create_engine(
        database_url,
        connect_args={"check_same_thread": False},
        echo=False,  # 测试时不输出SQL
    )
    yield engine
    engine.dispose()


@pytest.fixture(scope="session")
def test_async_engine(test_db_path: Path):
    """创建异步测试数据库引擎."""
    database_url = f"sqlite+aiosqlite:///{test_db_path}"
    engine = create_async_engine(
        database_url,
        echo=False,  # 测试时不输出SQL
    )
    yield engine
    engine.sync_engine.dispose()


@pytest.fixture
def db_session(test_engine):
    """创建数据库会话."""
    from src.backend.database.base import Base
    
    # 创建所有表
    Base.metadata.create_all(bind=test_engine)
    
    # 创建会话
    TestingSessionLocal = sessionmaker(
        autocommit=False, autoflush=False, bind=test_engine
    )
    
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        # 清理所有表
        Base.metadata.drop_all(bind=test_engine)


@pytest_asyncio.fixture
async def async_db_session(test_async_engine) -> AsyncGenerator[AsyncSession, None]:
    """创建异步数据库会话."""
    from src.backend.database.base import Base
    
    # 创建所有表
    async with test_async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # 创建异步会话
    AsyncTestingSessionLocal = sessionmaker(
        test_async_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with AsyncTestingSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()
    
    # 清理所有表
    async with test_async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
def client(db_session) -> TestClient:
    """创建测试客户端."""
    from src.backend.main import app
    from src.backend.database.session import get_db
    
    # 覆盖数据库依赖
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # 清理依赖覆盖
    app.dependency_overrides.clear()


@pytest.fixture
def mock_tushare():
    """模拟TuShare API."""
    mock = Mock()
    
    # 模拟股票列表
    mock.stock_basic.return_value = Mock()
    mock.stock_basic.return_value.to_dict.return_value = {
        'ts_code': ['000001.SZ', '000002.SZ'],
        'symbol': ['000001', '000002'],
        'name': ['平安银行', '万科A'],
        'area': ['深圳', '深圳'],
        'industry': ['银行', '房地产'],
        'market': ['主板', '主板'],
        'list_date': ['19910403', '19910129']
    }
    
    # 模拟日线数据
    mock.daily.return_value = Mock()
    mock.daily.return_value.to_dict.return_value = {
        'ts_code': ['000001.SZ'] * 5,
        'trade_date': ['20240101', '20240102', '20240103', '20240104', '20240105'],
        'open': [10.0, 10.1, 10.2, 10.3, 10.4],
        'high': [10.2, 10.3, 10.4, 10.5, 10.6],
        'low': [9.8, 9.9, 10.0, 10.1, 10.2],
        'close': [10.1, 10.2, 10.3, 10.4, 10.5],
        'pre_close': [10.0, 10.1, 10.2, 10.3, 10.4],
        'change': [0.1, 0.1, 0.1, 0.1, 0.1],
        'pct_chg': [1.0, 0.99, 0.98, 0.97, 0.96],
        'vol': [1000000, 1100000, 1200000, 1300000, 1400000],
        'amount': [101000, 112200, 123600, 135300, 147000]
    }
    
    return mock


@pytest.fixture
def sample_user_data():
    """示例用户数据."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "subscription_type": "basic"
    }


@pytest.fixture
def sample_strategy_data():
    """示例策略数据."""
    return {
        "name": "测试策略",
        "description": "这是一个测试策略",
        "strategy_type": "custom",
        "strategy_code": "# 测试策略代码\nprint('Hello, World!')",
        "parameters": {
            "period": 20,
            "threshold": 0.02
        }
    }


@pytest.fixture
def sample_stock_data():
    """示例股票数据."""
    return {
        "ts_code": "000001.SZ",
        "symbol": "000001",
        "name": "平安银行",
        "area": "深圳",
        "industry": "银行",
        "market": "主板",
        "list_date": "1991-04-03"
    }


@pytest.fixture(autouse=True)
def cleanup_test_files():
    """自动清理测试文件."""
    # 测试前的设置
    yield
    
    # 测试后的清理
    test_files = [
        "test.db",
        "test.db-journal",
        "test.log",
    ]
    
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
            except OSError:
                pass  # 忽略删除失败的情况


@pytest.fixture
def mock_redis():
    """模拟Redis客户端."""
    mock = Mock()
    mock.get.return_value = None
    mock.set.return_value = True
    mock.delete.return_value = 1
    mock.exists.return_value = False
    return mock


# 测试标记
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.e2e = pytest.mark.e2e
pytest.mark.slow = pytest.mark.slow


def pytest_configure(config):
    """Pytest配置."""
    config.addinivalue_line(
        "markers", "unit: 单元测试标记"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试标记"
    )
    config.addinivalue_line(
        "markers", "e2e: 端到端测试标记"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试标记"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集项."""
    # 为没有标记的测试添加unit标记
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)


# 测试数据清理装饰器
def cleanup_after_test(func):
    """测试后清理装饰器."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        finally:
            # 清理逻辑
            pass
    return wrapper
