#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PySnowball API 测试脚本
基于pysnowball项目的雪球API测试和验证
"""

import json
import logging
import traceback
from datetime import datetime

# 尝试导入pysnowball
try:
    import pysnowball as ball
    PYSNOWBALL_AVAILABLE = True
except ImportError:
    PYSNOWBALL_AVAILABLE = False
    print("⚠️ pysnowball未安装，请运行: pip install pysnowball")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PySnowballTester:
    """PySnowball API测试器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.token_set = False
        self.test_results = {}
        
    def load_and_set_token(self):
        """从配置文件加载并设置token"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 解析cookies获取token
            cookies_str = config.get('cookies', '')
            xq_a_token = ''
            u = ''
            
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    if key == 'xq_a_token':
                        xq_a_token = value
                    elif key == 'u':
                        u = value
            
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                if PYSNOWBALL_AVAILABLE:
                    ball.set_token(token)
                    self.token_set = True
                    logger.info(f"✅ Token设置成功: u={u}")
                    return True
                else:
                    logger.error("❌ pysnowball未安装")
                    return False
            else:
                logger.error("❌ 无法从配置文件获取有效token")
                return False
                
        except Exception as e:
            logger.error(f"❌ 加载token失败: {e}")
            return False
    
    def test_realtime_quote(self, symbol='SH600519'):
        """测试实时行情API"""
        test_name = "实时行情"
        logger.info(f"🧪 测试{test_name}: {symbol}")
        
        try:
            if not PYSNOWBALL_AVAILABLE:
                raise Exception("pysnowball未安装")
                
            result = ball.quotec(symbol)
            
            if result and 'data' in result and result['data']:
                quote_data = result['data'][0]
                logger.info(f"✅ {test_name}成功:")
                logger.info(f"   股票: {quote_data.get('symbol', 'N/A')}")
                logger.info(f"   价格: {quote_data.get('current', 'N/A')}")
                logger.info(f"   涨跌: {quote_data.get('chg', 'N/A')}")
                logger.info(f"   涨跌幅: {quote_data.get('percent', 'N/A')}%")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': quote_data,
                    'message': '实时行情获取成功'
                }
                return True
            else:
                raise Exception("返回数据格式错误")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e),
                'message': f'{test_name}测试失败'
            }
            return False
    
    def test_pankou(self, symbol='SH600519'):
        """测试五档行情API"""
        test_name = "五档行情"
        logger.info(f"🧪 测试{test_name}: {symbol}")
        
        try:
            if not PYSNOWBALL_AVAILABLE:
                raise Exception("pysnowball未安装")
                
            result = ball.pankou(symbol)
            
            if result and 'symbol' in result:
                logger.info(f"✅ {test_name}成功:")
                logger.info(f"   股票: {result.get('symbol', 'N/A')}")
                logger.info(f"   当前价: {result.get('current', 'N/A')}")
                logger.info(f"   买一价: {result.get('bp1', 'N/A')} 量: {result.get('bc1', 'N/A')}")
                logger.info(f"   卖一价: {result.get('sp1', 'N/A')} 量: {result.get('sc1', 'N/A')}")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': result,
                    'message': '五档行情获取成功'
                }
                return True
            else:
                raise Exception("返回数据格式错误")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e),
                'message': f'{test_name}测试失败'
            }
            return False
    
    def test_watch_list(self):
        """测试自选股列表API"""
        test_name = "自选股列表"
        logger.info(f"🧪 测试{test_name}")
        
        try:
            if not PYSNOWBALL_AVAILABLE:
                raise Exception("pysnowball未安装")
                
            result = ball.watch_list()
            
            if result and 'data' in result:
                stocks = result['data'].get('stocks', [])
                logger.info(f"✅ {test_name}成功:")
                logger.info(f"   股票组合数量: {len(stocks)}")
                
                for stock_group in stocks[:3]:  # 显示前3个组合
                    logger.info(f"   组合: {stock_group.get('name', 'N/A')} "
                              f"(股票数: {stock_group.get('symbol_count', 0)})")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': result['data'],
                    'message': '自选股列表获取成功'
                }
                return True
            else:
                raise Exception("返回数据格式错误")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e),
                'message': f'{test_name}测试失败'
            }
            return False
    
    def test_watch_stock(self, portfolio_id=-1):
        """测试自选股详情API"""
        test_name = "自选股详情"
        logger.info(f"🧪 测试{test_name}: portfolio_id={portfolio_id}")
        
        try:
            if not PYSNOWBALL_AVAILABLE:
                raise Exception("pysnowball未安装")
                
            result = ball.watch_stock(portfolio_id)
            
            if result and 'data' in result:
                stocks = result['data'].get('stocks', [])
                logger.info(f"✅ {test_name}成功:")
                logger.info(f"   组合ID: {result['data'].get('pid', 'N/A')}")
                logger.info(f"   股票数量: {len(stocks)}")
                
                for stock in stocks[:5]:  # 显示前5只股票
                    logger.info(f"   股票: {stock.get('name', 'N/A')} "
                              f"({stock.get('symbol', 'N/A')})")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': result['data'],
                    'message': '自选股详情获取成功'
                }
                return True
            else:
                raise Exception("返回数据格式错误")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e),
                'message': f'{test_name}测试失败'
            }
            return False
    
    def test_kline(self, symbol='SH600519', period='day', count=5):
        """测试K线数据API"""
        test_name = "K线数据"
        logger.info(f"🧪 测试{test_name}: {symbol}")
        
        try:
            if not PYSNOWBALL_AVAILABLE:
                raise Exception("pysnowball未安装")
                
            result = ball.kline(symbol, period, count)
            
            if result and 'data' in result:
                kline_data = result['data']
                logger.info(f"✅ {test_name}成功:")
                logger.info(f"   股票: {kline_data.get('symbol', 'N/A')}")
                logger.info(f"   数据点数: {len(kline_data.get('item', []))}")
                
                # 显示最新的K线数据
                items = kline_data.get('item', [])
                if items:
                    latest = items[-1]
                    logger.info(f"   最新K线: 开盘={latest[1]} 最高={latest[2]} "
                              f"最低={latest[3]} 收盘={latest[5]}")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': kline_data,
                    'message': 'K线数据获取成功'
                }
                return True
            else:
                raise Exception("返回数据格式错误")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e),
                'message': f'{test_name}测试失败'
            }
            return False
    
    def test_suggest_stock(self, query='茅台'):
        """测试股票搜索API"""
        test_name = "股票搜索"
        logger.info(f"🧪 测试{test_name}: {query}")
        
        try:
            if not PYSNOWBALL_AVAILABLE:
                raise Exception("pysnowball未安装")
                
            result = ball.suggest_stock(query)
            
            if result and 'data' in result:
                stocks = result['data']
                logger.info(f"✅ {test_name}成功:")
                logger.info(f"   搜索结果数量: {len(stocks)}")
                
                for stock in stocks[:3]:  # 显示前3个结果
                    logger.info(f"   股票: {stock.get('query', 'N/A')} "
                              f"({stock.get('code', 'N/A')})")
                
                self.test_results[test_name] = {
                    'status': 'success',
                    'data': stocks,
                    'message': '股票搜索成功'
                }
                return True
            else:
                raise Exception("返回数据格式错误")
                
        except Exception as e:
            logger.error(f"❌ {test_name}失败: {e}")
            self.test_results[test_name] = {
                'status': 'failed',
                'error': str(e),
                'message': f'{test_name}测试失败'
            }
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始PySnowball API全面测试")
        logger.info("=" * 60)
        
        # 检查pysnowball是否可用
        if not PYSNOWBALL_AVAILABLE:
            logger.error("❌ pysnowball未安装，请先安装: pip install pysnowball")
            return False
        
        # 设置token
        if not self.load_and_set_token():
            logger.error("❌ Token设置失败，无法继续测试")
            return False
        
        # 运行各项测试
        tests = [
            ('实时行情', lambda: self.test_realtime_quote('SH600519')),
            ('五档行情', lambda: self.test_pankou('SH600519')),
            ('股票搜索', lambda: self.test_suggest_stock('茅台')),
            ('K线数据', lambda: self.test_kline('SH600519')),
            ('自选股列表', lambda: self.test_watch_list()),
            ('自选股详情', lambda: self.test_watch_stock(-1)),
        ]
        
        success_count = 0
        total_count = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    success_count += 1
                logger.info("-" * 40)
            except Exception as e:
                logger.error(f"❌ 测试 {test_name} 异常: {e}")
                logger.info("-" * 40)
        
        # 输出测试总结
        logger.info("📊 测试总结")
        logger.info("=" * 60)
        logger.info(f"总测试数: {total_count}")
        logger.info(f"成功数: {success_count}")
        logger.info(f"失败数: {total_count - success_count}")
        logger.info(f"成功率: {success_count/total_count*100:.1f}%")
        
        # 输出详细结果
        logger.info("\n📋 详细结果:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == 'success' else "❌"
            logger.info(f"   {status_icon} {test_name}: {result['message']}")
        
        return success_count == total_count
    
    def save_test_results(self, filename='pysnowball_test_results.json'):
        """保存测试结果到文件"""
        try:
            test_summary = {
                'timestamp': datetime.now().isoformat(),
                'pysnowball_available': PYSNOWBALL_AVAILABLE,
                'token_set': self.token_set,
                'results': self.test_results,
                'summary': {
                    'total_tests': len(self.test_results),
                    'successful_tests': len([r for r in self.test_results.values() if r['status'] == 'success']),
                    'failed_tests': len([r for r in self.test_results.values() if r['status'] == 'failed'])
                }
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(test_summary, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📄 测试结果已保存到: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存测试结果失败: {e}")
            return False

def install_pysnowball():
    """安装pysnowball"""
    try:
        import subprocess
        import sys
        
        logger.info("📦 正在安装pysnowball...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'pysnowball'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ pysnowball安装成功!")
            logger.info("请重新运行此脚本进行测试")
            return True
        else:
            logger.error(f"❌ pysnowball安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 安装过程异常: {e}")
        return False

def main():
    """主函数"""
    print("🐍 PySnowball API 测试脚本")
    print("基于pysnowball项目的雪球API测试和验证")
    print("=" * 60)
    
    # 检查是否需要安装pysnowball
    if not PYSNOWBALL_AVAILABLE:
        choice = input("pysnowball未安装，是否现在安装？(y/n): ").strip().lower()
        if choice == 'y':
            if install_pysnowball():
                print("请重新运行此脚本")
                return
            else:
                print("安装失败，请手动安装: pip install pysnowball")
                return
        else:
            print("跳过安装，退出测试")
            return
    
    # 创建测试器并运行测试
    tester = PySnowballTester()
    
    try:
        success = tester.run_all_tests()
        tester.save_test_results()
        
        if success:
            print("\n🎉 所有测试通过！PySnowball API工作正常")
        else:
            print("\n⚠️ 部分测试失败，请检查配置和网络连接")
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程发生异常: {e}")
        traceback.print_exc()

if __name__ == '__main__':
    main()
