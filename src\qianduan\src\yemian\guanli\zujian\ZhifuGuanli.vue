<template>
  <el-dialog
    v-model="visible"
    title="支付管理"
    width="1000px"
    :before-close="handleClose"
  >
    <div class="payment-management-container" v-loading="loading">
      <div class="payment-content">
        <!-- 支付渠道统计 -->
        <div class="payment-stats">
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="stat-card">
                <div class="stat-icon alipay">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">¥{{ formatMoney(alipayStats.total_amount) }}</div>
                  <div class="stat-label">支付宝收入</div>
                  <div class="stat-desc">手续费 ¥{{ formatMoney(alipayStats.total_fee) }}</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="stat-card">
                <div class="stat-icon wechat">
                  <el-icon><ChatDotRound /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">¥{{ formatMoney(wechatStats.total_amount) }}</div>
                  <div class="stat-label">微信收入</div>
                  <div class="stat-desc">手续费 ¥{{ formatMoney(wechatStats.total_fee) }}</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="stat-card">
                <div class="stat-icon bank">
                  <el-icon><CreditCard /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">¥{{ formatMoney(bankStats.total_amount) }}</div>
                  <div class="stat-label">银行卡收入</div>
                  <div class="stat-desc">手续费 ¥{{ formatMoney(bankStats.total_fee) }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 支付渠道列表 -->
        <div class="payment-channels">
          <div class="section-header">
            <h3>支付渠道配置</h3>
            <el-button type="primary" size="small" @click="handleAddChannel">
              <el-icon><Plus /></el-icon>
              添加渠道
            </el-button>
          </div>
          
          <el-table :data="paymentChannels" style="width: 100%">
            <el-table-column prop="name" label="渠道名称" width="120">
              <template #default="{ row }">
                <div class="channel-name">
                  <el-icon class="channel-icon" :class="row.type">
                    <Money v-if="row.type === 'alipay'" />
                    <ChatDotRound v-else-if="row.type === 'wechat'" />
                    <CreditCard v-else-if="row.type === 'bank'" />
                    <Wallet v-else />
                  </el-icon>
                  {{ row.name }}
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                  {{ row.status === 'active' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="fee_rate" label="手续费率" width="100">
              <template #default="{ row }">
                {{ row.fee_rate }}%
              </template>
            </el-table-column>
            
            <el-table-column prop="daily_limit" label="日限额" width="120">
              <template #default="{ row }">
                ¥{{ formatMoney(row.daily_limit) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="monthly_limit" label="月限额" width="120">
              <template #default="{ row }">
                ¥{{ formatMoney(row.monthly_limit) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="total_amount" label="累计收入" width="120">
              <template #default="{ row }">
                ¥{{ formatMoney(row.total_amount) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="total_fee" label="累计手续费" width="120">
              <template #default="{ row }">
                ¥{{ formatMoney(row.total_fee) }}
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleEditChannel(row)">
                  编辑
                </el-button>
                <el-button 
                  :type="row.status === 'active' ? 'warning' : 'success'" 
                  size="small" 
                  @click="handleToggleStatus(row)"
                >
                  {{ row.status === 'active' ? '禁用' : '启用' }}
                </el-button>
                <el-button type="danger" size="small" @click="handleDeleteChannel(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 支付配置 -->
        <div class="payment-config">
          <div class="section-header">
            <h3>支付配置</h3>
          </div>
          
          <el-form :model="paymentConfig" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="默认支付方式">
                  <el-select v-model="paymentConfig.default_method" style="width: 100%">
                    <el-option label="支付宝" value="alipay" />
                    <el-option label="微信支付" value="wechat" />
                    <el-option label="银行卡" value="bank" />
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="支付超时时间">
                  <el-input-number 
                    v-model="paymentConfig.timeout_minutes" 
                    :min="5" 
                    :max="60"
                    style="width: 100%"
                  />
                  <span style="margin-left: 8px;">分钟</span>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="最小支付金额">
                  <el-input-number 
                    v-model="paymentConfig.min_amount" 
                    :min="1" 
                    :precision="2"
                    style="width: 100%"
                  />
                  <span style="margin-left: 8px;">元</span>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="最大支付金额">
                  <el-input-number 
                    v-model="paymentConfig.max_amount" 
                    :min="100" 
                    :precision="2"
                    style="width: 100%"
                  />
                  <span style="margin-left: 8px;">元</span>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item>
              <el-button type="primary" @click="handleSaveConfig" :loading="saving">
                保存配置
              </el-button>
              <el-button @click="handleResetConfig">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
    
    <!-- 编辑渠道弹窗 -->
    <ChannelEdit
      v-model="showChannelEdit"
      :channel="selectedChannel"
      @refresh="loadPaymentChannels"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { financeService } from '@/fuwu/financeService'
import ChannelEdit from './ChannelEdit.vue'

// Props
interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const saving = ref(false)
const showChannelEdit = ref(false)
const selectedChannel = ref<any>(null)

// 数据
const paymentChannels = ref<any[]>([])
const paymentConfig = ref({
  default_method: 'alipay',
  timeout_minutes: 15,
  min_amount: 1,
  max_amount: 10000
})

// 计算属性
const alipayStats = computed(() => {
  return paymentChannels.value.find(c => c.type === 'alipay') || { total_amount: 0, total_fee: 0 }
})

const wechatStats = computed(() => {
  return paymentChannels.value.find(c => c.type === 'wechat') || { total_amount: 0, total_fee: 0 }
})

const bankStats = computed(() => {
  return paymentChannels.value.find(c => c.type === 'bank') || { total_amount: 0, total_fee: 0 }
})

// 工具函数
const formatMoney = (amount: number) => {
  if (amount >= 10000) {
    return `${(amount / 10000).toFixed(1)}万`
  }
  return amount.toLocaleString()
}

// 方法
const loadPaymentChannels = async () => {
  try {
    loading.value = true
    const channels = await financeService.getPaymentChannels()
    paymentChannels.value = channels
  } catch (error) {
    console.error('加载支付渠道失败:', error)
    ElMessage.error('加载支付渠道失败')
  } finally {
    loading.value = false
  }
}

const handleAddChannel = () => {
  selectedChannel.value = null
  showChannelEdit.value = true
}

const handleEditChannel = (channel: any) => {
  selectedChannel.value = channel
  showChannelEdit.value = true
}

const handleToggleStatus = async (channel: any) => {
  try {
    const newStatus = channel.status === 'active' ? 'inactive' : 'active'
    const success = await financeService.updatePaymentChannel(channel.id, { status: newStatus })
    
    if (success) {
      channel.status = newStatus
      ElMessage.success(`渠道已${newStatus === 'active' ? '启用' : '禁用'}`)
      emit('refresh')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleDeleteChannel = async (channel: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除支付渠道 "${channel.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    ElMessage.success('渠道已删除')
    loadPaymentChannels()
    emit('refresh')
  } catch {
    // 用户取消
  }
}

const handleSaveConfig = async () => {
  try {
    saving.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleResetConfig = () => {
  paymentConfig.value = {
    default_method: 'alipay',
    timeout_minutes: 15,
    min_amount: 1,
    max_amount: 10000
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(visible, (newVisible) => {
  if (newVisible) {
    loadPaymentChannels()
  }
})
</script>

<style lang="scss" scoped>
.payment-management-container {
  min-height: 500px;

  .payment-content {
    .payment-stats {
      margin-bottom: 24px;

      .stat-card {
        display: flex;
        align-items: center;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .stat-icon {
          width: 50px;
          height: 50px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          color: white;
          margin-right: 16px;

          &.alipay {
            background: linear-gradient(135deg, #1677ff, #69b1ff);
          }

          &.wechat {
            background: linear-gradient(135deg, #52c41a, #95de64);
          }

          &.bank {
            background: linear-gradient(135deg, #fa8c16, #ffc069);
          }
        }

        .stat-content {
          .stat-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: var(--el-text-color-regular);
            margin-bottom: 4px;
          }

          .stat-desc {
            font-size: 12px;
            color: var(--el-text-color-placeholder);
          }
        }
      }
    }

    .payment-channels,
    .payment-config {
      margin-bottom: 24px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
      }

      .channel-name {
        display: flex;
        align-items: center;

        .channel-icon {
          margin-right: 8px;

          &.alipay {
            color: #1677ff;
          }

          &.wechat {
            color: #52c41a;
          }

          &.bank {
            color: #fa8c16;
          }

          &.balance {
            color: #722ed1;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
