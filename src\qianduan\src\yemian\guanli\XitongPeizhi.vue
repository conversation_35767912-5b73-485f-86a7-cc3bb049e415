<template>
  <div class="system-config-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">系统配置</span>
          <el-button type="primary" @click="handleSaveConfig" :loading="saving">
            <el-icon><Check /></el-icon>
            保存配置
          </el-button>
        </div>
      </template>
      
      <div class="config-content">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基础设置 -->
          <el-tab-pane label="基础设置" name="basic">
            <el-form :model="basicConfig" label-width="120px">
              <el-form-item label="平台名称">
                <el-input v-model="basicConfig.platformName" />
              </el-form-item>
              <el-form-item label="平台描述">
                <el-input v-model="basicConfig.platformDescription" type="textarea" :rows="3" />
              </el-form-item>
              <el-form-item label="联系邮箱">
                <el-input v-model="basicConfig.contactEmail" />
              </el-form-item>
              <el-form-item label="客服电话">
                <el-input v-model="basicConfig.supportPhone" />
              </el-form-item>
              <el-form-item label="用户注册">
                <el-switch v-model="basicConfig.allowRegistration" />
                <span class="form-tip">关闭后新用户无法注册</span>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <!-- 安全设置 -->
          <el-tab-pane label="安全设置" name="security">
            <el-form :model="securityConfig" label-width="120px">
              <el-form-item label="密码最小长度">
                <el-input-number v-model="securityConfig.minPasswordLength" :min="6" :max="20" />
              </el-form-item>
              <el-form-item label="登录失败限制">
                <el-input-number v-model="securityConfig.maxLoginAttempts" :min="3" :max="10" />
                <span class="form-tip">次失败后锁定账户</span>
              </el-form-item>
              <el-form-item label="会话超时">
                <el-input-number v-model="securityConfig.sessionTimeout" :min="30" :max="1440" />
                <span class="form-tip">分钟后自动退出</span>
              </el-form-item>
              <el-form-item label="强制HTTPS">
                <el-switch v-model="securityConfig.forceHttps" />
              </el-form-item>
              <el-form-item label="启用验证码">
                <el-switch v-model="securityConfig.enableCaptcha" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <!-- 邮件设置 -->
          <el-tab-pane label="邮件设置" name="email">
            <el-form :model="emailConfig" label-width="120px">
              <el-form-item label="SMTP服务器">
                <el-input v-model="emailConfig.smtpHost" />
              </el-form-item>
              <el-form-item label="SMTP端口">
                <el-input-number v-model="emailConfig.smtpPort" :min="1" :max="65535" />
              </el-form-item>
              <el-form-item label="发件人邮箱">
                <el-input v-model="emailConfig.fromEmail" />
              </el-form-item>
              <el-form-item label="发件人名称">
                <el-input v-model="emailConfig.fromName" />
              </el-form-item>
              <el-form-item label="邮箱密码">
                <el-input v-model="emailConfig.password" type="password" show-password />
              </el-form-item>
              <el-form-item label="启用SSL">
                <el-switch v-model="emailConfig.enableSsl" />
              </el-form-item>
              <el-form-item>
                <el-button @click="testEmailConfig">测试邮件配置</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <!-- 数据源设置 -->
          <el-tab-pane label="数据源设置" name="datasource">
            <el-form :model="datasourceConfig" label-width="120px">
              <el-form-item label="TuShare Token">
                <el-input v-model="datasourceConfig.tushareToken" type="password" show-password />
              </el-form-item>
              <el-form-item label="数据更新频率">
                <el-select v-model="datasourceConfig.updateFrequency">
                  <el-option label="实时" value="realtime" />
                  <el-option label="每分钟" value="1min" />
                  <el-option label="每5分钟" value="5min" />
                  <el-option label="每15分钟" value="15min" />
                </el-select>
              </el-form-item>
              <el-form-item label="历史数据天数">
                <el-input-number v-model="datasourceConfig.historyDays" :min="30" :max="3650" />
              </el-form-item>
              <el-form-item label="启用数据缓存">
                <el-switch v-model="datasourceConfig.enableCache" />
              </el-form-item>
              <el-form-item>
                <el-button @click="testDatasourceConfig">测试数据源连接</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const activeTab = ref('basic')
const saving = ref(false)

// 基础配置
const basicConfig = reactive({
  platformName: 'A股量化交易平台',
  platformDescription: '专业的量化投资工具，为投资者提供策略开发、回测分析、实盘交易等服务',
  contactEmail: '<EMAIL>',
  supportPhone: '************',
  allowRegistration: true
})

// 安全配置
const securityConfig = reactive({
  minPasswordLength: 8,
  maxLoginAttempts: 5,
  sessionTimeout: 120,
  forceHttps: true,
  enableCaptcha: true
})

// 邮件配置
const emailConfig = reactive({
  smtpHost: 'smtp.qq.com',
  smtpPort: 587,
  fromEmail: '<EMAIL>',
  fromName: 'A股量化交易平台',
  password: '',
  enableSsl: true
})

// 数据源配置
const datasourceConfig = reactive({
  tushareToken: '',
  updateFrequency: '5min',
  historyDays: 365,
  enableCache: true
})

// 方法
const handleSaveConfig = async () => {
  saving.value = true
  
  try {
    // 模拟保存配置
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('配置保存成功')
  } catch (error) {
    ElMessage.error('配置保存失败')
  } finally {
    saving.value = false
  }
}

const testEmailConfig = async () => {
  try {
    // 模拟测试邮件配置
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('邮件配置测试成功')
  } catch (error) {
    ElMessage.error('邮件配置测试失败')
  }
}

const testDatasourceConfig = async () => {
  try {
    // 模拟测试数据源连接
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('数据源连接测试成功')
  } catch (error) {
    ElMessage.error('数据源连接测试失败')
  }
}
</script>

<style lang="scss" scoped>
.system-config-container {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .card-title {
    font-size: 18px;
    font-weight: 600;
  }
}

.config-content {
  .form-tip {
    margin-left: 10px;
    font-size: 12px;
    color: var(--el-text-color-regular);
  }
}

:deep(.el-tabs__content) {
  padding: 20px;
}
</style>
