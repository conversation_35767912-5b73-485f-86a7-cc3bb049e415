<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化交易平台 - 原型设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: #1a1a1a;
            color: #ffffff;
        }
        
        .header {
            background: #2d3748;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #4299e1;
        }
        
        .nav {
            display: flex;
            gap: 2rem;
        }
        
        .nav-item {
            padding: 0.5rem 1rem;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background: #4299e1;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .container {
            display: flex;
            height: calc(100vh - 70px);
        }
        
        .sidebar {
            width: 250px;
            background: #2d3748;
            padding: 1rem;
            overflow-y: auto;
        }
        
        .sidebar-section {
            margin-bottom: 2rem;
        }
        
        .sidebar-title {
            font-size: 0.9rem;
            color: #a0aec0;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
        }
        
        .sidebar-item {
            padding: 0.75rem;
            margin-bottom: 0.25rem;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .sidebar-item:hover {
            background: #4a5568;
        }
        
        .main-content {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 1rem;
            height: 100%;
        }
        
        .chart-panel {
            background: #2d3748;
            border-radius: 8px;
            padding: 1rem;
        }
        
        .right-panel {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .panel {
            background: #2d3748;
            border-radius: 8px;
            padding: 1rem;
        }
        
        .panel-title {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: #4299e1;
            border-bottom: 1px solid #4a5568;
            padding-bottom: 0.5rem;
        }
        
        .strategy-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            background: #4a5568;
            border-radius: 4px;
        }
        
        .status-running {
            color: #48bb78;
        }
        
        .status-stopped {
            color: #f56565;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }
        
        .btn-primary {
            background: #4299e1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3182ce;
        }
        
        .btn-success {
            background: #48bb78;
            color: white;
        }
        
        .btn-danger {
            background: #f56565;
            color: white;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .metric-value {
            font-weight: bold;
        }
        
        .positive {
            color: #48bb78;
        }
        
        .negative {
            color: #f56565;
        }
        
        .chart-placeholder {
            height: 400px;
            background: #1a202c;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #a0aec0;
            font-size: 1.2rem;
        }
        
        .order-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
            color: #a0aec0;
        }
        
        .form-input {
            padding: 0.5rem;
            border: 1px solid #4a5568;
            border-radius: 4px;
            background: #1a202c;
            color: white;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #4299e1;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="logo">📈 量化交易平台</div>
        <nav class="nav">
            <div class="nav-item active">仪表板</div>
            <div class="nav-item">策略中心</div>
            <div class="nav-item">交易面板</div>
            <div class="nav-item">数据中心</div>
            <div class="nav-item">回测分析</div>
        </nav>
        <div class="user-info">
            <span>💰 资金: ¥1,000,000</span>
            <span>👤 用户名</span>
        </div>
    </header>

    <div class="container">
        <!-- 左侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <div class="sidebar-title">我的策略</div>
                <div class="sidebar-item">ATR RSI策略</div>
                <div class="sidebar-item">双均线策略</div>
                <div class="sidebar-item">网格交易策略</div>
                <div class="sidebar-item">+ 新建策略</div>
            </div>
            
            <div class="sidebar-section">
                <div class="sidebar-title">自选股</div>
                <div class="sidebar-item">000001 平安银行</div>
                <div class="sidebar-item">000002 万科A</div>
                <div class="sidebar-item">600519 贵州茅台</div>
                <div class="sidebar-item">+ 添加自选</div>
            </div>
            
            <div class="sidebar-section">
                <div class="sidebar-title">热门板块</div>
                <div class="sidebar-item">🔥 人工智能</div>
                <div class="sidebar-item">⚡ 新能源汽车</div>
                <div class="sidebar-item">💊 生物医药</div>
                <div class="sidebar-item">🏠 房地产</div>
            </div>
        </aside>

        <!-- 主要内容区 -->
        <main class="main-content">
            <div class="dashboard-grid">
                <!-- 图表区域 -->
                <div class="chart-panel">
                    <div class="panel-title">📊 实时行情 - 上证指数</div>
                    <div class="chart-placeholder">
                        K线图表区域<br>
                        (集成 ECharts 显示实时K线)
                    </div>
                </div>

                <!-- 右侧面板 -->
                <div class="right-panel">
                    <!-- 策略状态 -->
                    <div class="panel">
                        <div class="panel-title">🤖 策略状态</div>
                        <div class="strategy-item">
                            <span>ATR RSI策略</span>
                            <span class="status-running">● 运行中</span>
                        </div>
                        <div class="strategy-item">
                            <span>双均线策略</span>
                            <span class="status-stopped">● 已停止</span>
                        </div>
                        <button class="btn btn-primary" style="width: 100%; margin-top: 1rem;">
                            新建策略
                        </button>
                    </div>

                    <!-- 账户概览 -->
                    <div class="panel">
                        <div class="panel-title">💰 账户概览</div>
                        <div class="metric">
                            <span>总资产:</span>
                            <span class="metric-value">¥1,050,000</span>
                        </div>
                        <div class="metric">
                            <span>可用资金:</span>
                            <span class="metric-value">¥800,000</span>
                        </div>
                        <div class="metric">
                            <span>持仓市值:</span>
                            <span class="metric-value">¥250,000</span>
                        </div>
                        <div class="metric">
                            <span>今日盈亏:</span>
                            <span class="metric-value positive">+¥5,000 (+2.1%)</span>
                        </div>
                        <div class="metric">
                            <span>总盈亏:</span>
                            <span class="metric-value positive">+¥50,000 (+5.0%)</span>
                        </div>
                    </div>

                    <!-- 快速下单 -->
                    <div class="panel">
                        <div class="panel-title">⚡ 快速下单</div>
                        <div class="order-form">
                            <div class="form-group">
                                <label class="form-label">股票代码</label>
                                <input type="text" class="form-input" placeholder="000001">
                            </div>
                            <div class="form-group">
                                <label class="form-label">交易类型</label>
                                <select class="form-input">
                                    <option>买入</option>
                                    <option>卖出</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">数量</label>
                                <input type="number" class="form-input" placeholder="100">
                            </div>
                            <div class="form-group">
                                <label class="form-label">价格</label>
                                <input type="number" class="form-input" placeholder="10.50" step="0.01">
                            </div>
                        </div>
                        <div style="display: flex; gap: 0.5rem;">
                            <button class="btn btn-success" style="flex: 1;">买入</button>
                            <button class="btn btn-danger" style="flex: 1;">卖出</button>
                        </div>
                    </div>

                    <!-- 最新资讯 -->
                    <div class="panel">
                        <div class="panel-title">📰 最新资讯</div>
                        <div style="font-size: 0.9rem; line-height: 1.5;">
                            <div style="margin-bottom: 0.5rem;">
                                • A股三大指数集体上涨，创业板指涨超1%
                            </div>
                            <div style="margin-bottom: 0.5rem;">
                                • 央行降准释放流动性，利好股市
                            </div>
                            <div style="margin-bottom: 0.5rem;">
                                • 人工智能板块持续活跃，多股涨停
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 简单的交互逻辑
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelector('.nav-item.active').classList.remove('active');
                this.classList.add('active');
            });
        });

        // 模拟实时数据更新
        setInterval(() => {
            const priceElement = document.querySelector('.user-info span');
            if (priceElement) {
                const currentValue = parseInt(priceElement.textContent.replace(/[^\d]/g, ''));
                const change = Math.random() * 1000 - 500;
                const newValue = Math.max(0, currentValue + change);
                priceElement.textContent = `💰 资金: ¥${newValue.toLocaleString()}`;
            }
        }, 5000);
    </script>
</body>
</html>
