#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取用户真实的雪球组合数据
基于用户ID ********** 获取真实持仓信息
"""

import json
import logging
import requests
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MyRealPortfolioData:
    """获取我的真实组合数据"""
    
    def __init__(self):
        self.user_id = "**********"
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载真实配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ 使用用户 {u} 的Token")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            return False
    
    def get_user_cubes_list(self):
        """获取用户创建的组合列表"""
        try:
            logger.info(f"🔍 获取用户 {self.user_id} 的组合列表")
            
            # 方法1: 通过用户页面API
            user_api_urls = [
                f"https://xueqiu.com/v4/stock/portfolio/list.json?user_id={self.user_id}",
                f"https://xueqiu.com/cubes/list.json?user_id={self.user_id}",
                f"https://xueqiu.com/u/{self.user_id}/cubes.json",
                f"https://xueqiu.com/v5/stock/portfolio/list.json?user_id={self.user_id}",
            ]
            
            for url in user_api_urls:
                try:
                    logger.info(f"   尝试API: {url}")
                    response = self.session.get(url, timeout=10)
                    
                    if response.status_code == 200:
                        data = response.json()
                        logger.info(f"   ✅ 成功响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        
                        # 检查是否包含组合数据
                        if 'list' in data or 'data' in data or 'cubes' in data:
                            return data
                    else:
                        logger.info(f"   ❌ 响应失败: {response.status_code}")
                        
                except Exception as e:
                    logger.info(f"   ❌ 请求异常: {e}")
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取组合列表失败: {e}")
            return None
    
    def get_user_portfolio_holdings(self):
        """获取用户投资组合的真实持仓"""
        try:
            logger.info(f"📊 获取用户真实持仓数据")
            
            # 方法1: 通过用户的投资组合API
            portfolio_apis = [
                f"https://xueqiu.com/v4/stock/portfolio/stocks.json?user_id={self.user_id}",
                f"https://xueqiu.com/v4/stock/portfolio/detail.json?user_id={self.user_id}",
                f"https://xueqiu.com/stock/portfolio/stocks.json?user_id={self.user_id}",
            ]
            
            for api_url in portfolio_apis:
                try:
                    logger.info(f"   尝试持仓API: {api_url}")
                    response = self.session.get(api_url, timeout=10)
                    
                    if response.status_code == 200:
                        data = response.json()
                        logger.info(f"   ✅ 持仓数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        return data
                    else:
                        logger.info(f"   ❌ 响应失败: {response.status_code}")
                        
                except Exception as e:
                    logger.info(f"   ❌ 请求异常: {e}")
            
            # 方法2: 通过自选股API（这个我们已经知道有数据）
            logger.info(f"   尝试自选股API...")
            result = ball.watch_stock(-4)  # 模拟组合
            if result and 'data' in result:
                stocks = result['data'].get('stocks', [])
                logger.info(f"   ✅ 自选股数据: {len(stocks)} 只股票")
                
                # 为每只股票获取详细信息
                detailed_holdings = []
                for stock in stocks:
                    symbol = stock.get('symbol', '')
                    name = stock.get('name', '')
                    
                    # 获取实时行情
                    quote = ball.quotec(symbol)
                    quote_data = quote['data'][0] if quote and 'data' in quote and quote['data'] else {}
                    
                    holding = {
                        'symbol': symbol,
                        'name': name,
                        'current_price': quote_data.get('current', 0),
                        'chg': quote_data.get('chg', 0),
                        'percent': quote_data.get('percent', 0),
                        'volume': quote_data.get('volume', 0),
                        'amount': quote_data.get('amount', 0),
                        'market_capital': quote_data.get('market_capital', 0),
                        'last_close': quote_data.get('last_close', 0),
                        'open': quote_data.get('open', 0),
                        'high': quote_data.get('high', 0),
                        'low': quote_data.get('low', 0),
                    }
                    detailed_holdings.append(holding)
                
                return {
                    'source': 'watch_stock',
                    'holdings': detailed_holdings,
                    'total_count': len(detailed_holdings)
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取持仓数据失败: {e}")
            return None
    
    def search_my_portfolios_by_name(self):
        """通过名称搜索我的组合"""
        try:
            logger.info(f"🔍 搜索用户的'测试'和'test'组合")
            
            search_terms = ['测试', 'test', '模拟']
            found_portfolios = []
            
            for term in search_terms:
                try:
                    search_url = f"https://xueqiu.com/cubes/search.json?q={term}&count=50&market=cn"
                    response = self.session.get(search_url, timeout=10)
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        if 'list' in data and data['list']:
                            cubes = data['list']
                            logger.info(f"   搜索'{term}'找到 {len(cubes)} 个组合")
                            
                            # 查找属于当前用户的组合
                            for cube in cubes:
                                cube_user_id = str(cube.get('user_id', ''))
                                if cube_user_id == self.user_id:
                                    logger.info(f"   ✅ 找到用户组合: {cube.get('name', 'N/A')} ({cube.get('symbol', 'N/A')})")
                                    found_portfolios.append(cube)
                
                except Exception as e:
                    logger.warning(f"   搜索'{term}'失败: {e}")
            
            return found_portfolios
            
        except Exception as e:
            logger.error(f"❌ 搜索组合失败: {e}")
            return []
    
    def get_portfolio_detailed_holdings(self, portfolio_symbol):
        """获取指定组合的详细持仓"""
        try:
            logger.info(f"📋 获取组合 {portfolio_symbol} 的详细持仓")
            
            # 获取组合当前调仓
            current_result = ball.rebalancing_current(portfolio_symbol)
            if current_result and 'last_rb' in current_result:
                last_rb = current_result['last_rb']
                holdings = last_rb.get('holdings', [])
                
                logger.info(f"   ✅ 找到 {len(holdings)} 个持仓")
                
                detailed_holdings = []
                for holding in holdings:
                    symbol = holding.get('stock_symbol', '')
                    name = holding.get('stock_name', '')
                    weight = holding.get('weight', 0)
                    volume = holding.get('volume', 0)
                    price = holding.get('price', 0)
                    
                    # 获取实时行情
                    quote = ball.quotec(symbol)
                    quote_data = quote['data'][0] if quote and 'data' in quote and quote['data'] else {}
                    
                    current_price = quote_data.get('current', 0)
                    
                    # 计算盈亏
                    if current_price > 0 and price > 0 and volume > 0:
                        cost_value = volume * price
                        current_value = volume * current_price
                        unrealized_pnl = current_value - cost_value
                        unrealized_pnl_percent = (unrealized_pnl / cost_value) * 100 if cost_value > 0 else 0
                    else:
                        cost_value = 0
                        current_value = 0
                        unrealized_pnl = 0
                        unrealized_pnl_percent = 0
                    
                    detailed_holding = {
                        'symbol': symbol,
                        'name': name,
                        'shares_held': volume,
                        'avg_cost': price,
                        'weight': weight,
                        'current_price': current_price,
                        'cost_value': cost_value,
                        'current_value': current_value,
                        'unrealized_pnl': unrealized_pnl,
                        'unrealized_pnl_percent': unrealized_pnl_percent,
                        'chg': quote_data.get('chg', 0),
                        'percent': quote_data.get('percent', 0),
                        'last_trade_time': last_rb.get('created_at', ''),
                    }
                    detailed_holdings.append(detailed_holding)
                
                return {
                    'portfolio_symbol': portfolio_symbol,
                    'holdings': detailed_holdings,
                    'rebalancing_info': last_rb
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取组合详细持仓失败: {e}")
            return None
    
    def get_all_my_data(self):
        """获取我的所有真实数据"""
        logger.info("🚀 获取用户所有真实数据")
        logger.info("=" * 60)
        
        all_data = {
            'user_id': self.user_id,
            'cubes_list': None,
            'portfolio_holdings': None,
            'search_results': None,
            'detailed_portfolios': []
        }
        
        # 1. 获取组合列表
        logger.info("\n📋 1. 获取组合列表")
        cubes_list = self.get_user_cubes_list()
        all_data['cubes_list'] = cubes_list
        
        # 2. 获取持仓数据
        logger.info("\n💼 2. 获取持仓数据")
        portfolio_holdings = self.get_user_portfolio_holdings()
        all_data['portfolio_holdings'] = portfolio_holdings
        
        # 3. 搜索我的组合
        logger.info("\n🔍 3. 搜索我的组合")
        search_results = self.search_my_portfolios_by_name()
        all_data['search_results'] = search_results
        
        # 4. 获取找到的组合的详细信息
        if search_results:
            logger.info("\n📊 4. 获取组合详细信息")
            for portfolio in search_results:
                symbol = portfolio.get('symbol', '')
                if symbol:
                    detailed = self.get_portfolio_detailed_holdings(symbol)
                    if detailed:
                        all_data['detailed_portfolios'].append(detailed)
        
        # 5. 汇总结果
        logger.info("\n📈 5. 数据汇总")
        logger.info("=" * 50)
        
        if portfolio_holdings and 'holdings' in portfolio_holdings:
            holdings = portfolio_holdings['holdings']
            logger.info(f"✅ 持仓股票: {len(holdings)} 只")
            for holding in holdings:
                logger.info(f"   📊 {holding['name']} ({holding['symbol']}): ¥{holding['current_price']}")
        
        if search_results:
            logger.info(f"✅ 找到组合: {len(search_results)} 个")
            for portfolio in search_results:
                logger.info(f"   📋 {portfolio.get('name', 'N/A')} ({portfolio.get('symbol', 'N/A')})")
        
        if all_data['detailed_portfolios']:
            logger.info(f"✅ 详细组合: {len(all_data['detailed_portfolios'])} 个")
            for detailed in all_data['detailed_portfolios']:
                holdings = detailed.get('holdings', [])
                logger.info(f"   💼 {detailed['portfolio_symbol']}: {len(holdings)} 只股票")
                for holding in holdings:
                    logger.info(f"      - {holding['name']}: {holding['shares_held']}股 @¥{holding['avg_cost']:.2f}")
        
        return all_data

def main():
    """主函数"""
    print("🎯 获取用户真实雪球数据")
    print("基于用户ID **********")
    print("=" * 60)
    
    data_provider = MyRealPortfolioData()
    
    # 获取所有真实数据
    my_data = data_provider.get_all_my_data()
    
    # 保存数据到文件
    try:
        with open('my_real_portfolio_data.json', 'w', encoding='utf-8') as f:
            json.dump(my_data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 数据已保存到 my_real_portfolio_data.json")
    except Exception as e:
        print(f"\n❌ 保存数据失败: {e}")
    
    print(f"\n🎉 数据获取完成！")

if __name__ == '__main__':
    main()
