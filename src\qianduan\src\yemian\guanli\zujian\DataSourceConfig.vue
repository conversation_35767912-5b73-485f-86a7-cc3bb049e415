<template>
  <el-dialog
    v-model="visible"
    title="数据源配置"
    width="800px"
    :before-close="handleClose"
  >
    <div class="datasource-config-container" v-loading="loading">
      <div class="config-content">
        <!-- 配置表单 -->
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
          <el-form-item label="数据源名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入数据源名称" />
          </el-form-item>
          
          <el-form-item label="数据源类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择数据源类型" style="width: 100%">
              <el-option label="TuShare" value="tushare" />
              <el-option label="东方财富" value="eastmoney" />
              <el-option label="新浪财经" value="sina" />
              <el-option label="自定义" value="custom" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="API端点" prop="endpoint">
            <el-input v-model="form.endpoint" placeholder="请输入API端点URL" />
          </el-form-item>
          
          <el-form-item label="API密钥" prop="apiKey" v-if="form.type === 'tushare'">
            <el-input 
              v-model="form.apiKey" 
              type="password" 
              placeholder="请输入TuShare API Token"
              show-password
            />
            <div class="form-tip">
              <el-link href="https://tushare.pro/register" target="_blank" type="primary">
                获取TuShare API Token
              </el-link>
            </div>
          </el-form-item>
          
          <el-form-item label="超时时间" prop="timeout">
            <el-input-number 
              v-model="form.timeout" 
              :min="1000" 
              :max="60000" 
              :step="1000"
              style="width: 100%"
            />
            <span style="margin-left: 8px;">毫秒</span>
          </el-form-item>
          
          <el-form-item label="重试次数" prop="retryCount">
            <el-input-number 
              v-model="form.retryCount" 
              :min="0" 
              :max="10"
              style="width: 100%"
            />
          </el-form-item>
          
          <el-form-item label="优先级" prop="priority">
            <el-input-number 
              v-model="form.priority" 
              :min="1" 
              :max="100"
              style="width: 100%"
            />
            <div class="form-tip">数字越小优先级越高</div>
          </el-form-item>
          
          <el-form-item label="启用状态" prop="enabled">
            <el-switch v-model="form.enabled" />
          </el-form-item>
          
          <el-form-item label="描述" prop="description">
            <el-input 
              v-model="form.description" 
              type="textarea" 
              :rows="3"
              placeholder="请输入数据源描述"
            />
          </el-form-item>
        </el-form>
        
        <!-- 连接测试 -->
        <div class="connection-test">
          <el-divider content-position="left">连接测试</el-divider>
          <div class="test-section">
            <el-button 
              type="primary" 
              @click="testConnection" 
              :loading="testing"
              :disabled="!canTest"
            >
              <el-icon><Connection /></el-icon>
              测试连接
            </el-button>
            
            <div v-if="testResult" class="test-result">
              <div v-if="testResult.success" class="test-success">
                <el-icon><SuccessFilled /></el-icon>
                <span>连接成功！响应时间: {{ testResult.responseTime }}ms</span>
              </div>
              <div v-else class="test-error">
                <el-icon><CircleCloseFilled /></el-icon>
                <span>连接失败: {{ testResult.error }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { datasourceService, type DataSourceConfig } from '@/fuwu/datasourceService'

// Props
interface Props {
  modelValue: boolean
  config?: DataSourceConfig | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  config: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const testing = ref(false)
const testResult = ref<{ success: boolean, responseTime?: number, error?: string } | null>(null)

const form = ref({
  name: '',
  type: 'tushare' as 'tushare' | 'eastmoney' | 'sina' | 'custom',
  endpoint: '',
  apiKey: '',
  timeout: 10000,
  retryCount: 3,
  priority: 1,
  enabled: true,
  description: ''
})

const rules = {
  name: [
    { required: true, message: '请输入数据源名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据源类型', trigger: 'change' }
  ],
  endpoint: [
    { required: true, message: '请输入API端点', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  timeout: [
    { required: true, message: '请输入超时时间', trigger: 'blur' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.config)

const canTest = computed(() => {
  return form.value.endpoint && form.value.name
})

// 方法
const testConnection = async () => {
  try {
    testing.value = true
    testResult.value = null
    
    // 创建临时配置进行测试
    const tempConfig: DataSourceConfig = {
      id: 'temp_test',
      name: form.value.name,
      type: form.value.type,
      endpoint: form.value.endpoint,
      apiKey: form.value.apiKey,
      timeout: form.value.timeout,
      retryCount: form.value.retryCount,
      enabled: form.value.enabled,
      priority: form.value.priority,
      description: form.value.description,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    
    // 这里应该调用真实的连接测试API
    // 暂时模拟测试结果
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const success = Math.random() > 0.3 // 70%成功率
    
    if (success) {
      testResult.value = {
        success: true,
        responseTime: Math.floor(Math.random() * 1000) + 100
      }
      ElMessage.success('连接测试成功')
    } else {
      testResult.value = {
        success: false,
        error: '连接超时或API密钥无效'
      }
      ElMessage.error('连接测试失败')
    }
  } catch (error: any) {
    testResult.value = {
      success: false,
      error: error.message || '测试失败'
    }
    ElMessage.error('连接测试失败')
  } finally {
    testing.value = false
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (isEdit.value && props.config) {
      // 更新配置
      const success = await datasourceService.updateDataSourceConfig({
        ...props.config,
        ...form.value,
        updateTime: new Date().toISOString()
      })
      
      if (success) {
        ElMessage.success('数据源配置更新成功')
        emit('refresh')
        handleClose()
      } else {
        ElMessage.error('更新失败')
      }
    } else {
      // 添加配置
      const id = await datasourceService.addDataSourceConfig(form.value)
      
      if (id) {
        ElMessage.success('数据源配置添加成功')
        emit('refresh')
        handleClose()
      } else {
        ElMessage.error('添加失败')
      }
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  formRef.value?.resetFields()
  testResult.value = null
  visible.value = false
}

// 监听器
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    form.value = {
      name: newConfig.name,
      type: newConfig.type,
      endpoint: newConfig.endpoint,
      apiKey: newConfig.apiKey || '',
      timeout: newConfig.timeout,
      retryCount: newConfig.retryCount,
      priority: newConfig.priority,
      enabled: newConfig.enabled,
      description: newConfig.description
    }
  } else {
    form.value = {
      name: '',
      type: 'tushare',
      endpoint: '',
      apiKey: '',
      timeout: 10000,
      retryCount: 3,
      priority: 1,
      enabled: true,
      description: ''
    }
  }
  testResult.value = null
}, { immediate: true })

// 监听数据源类型变化，自动填充默认端点
watch(() => form.value.type, (newType) => {
  const defaultEndpoints = {
    tushare: 'http://api.tushare.pro',
    eastmoney: 'http://push2.eastmoney.com',
    sina: 'http://hq.sinajs.cn',
    custom: ''
  }
  
  if (!form.value.endpoint || Object.values(defaultEndpoints).includes(form.value.endpoint)) {
    form.value.endpoint = defaultEndpoints[newType]
  }
})
</script>

<style lang="scss" scoped>
.datasource-config-container {
  min-height: 400px;
  
  .config-content {
    .form-tip {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
      margin-top: 4px;
    }
    
    .connection-test {
      margin-top: 20px;
      
      .test-section {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .test-result {
          .test-success {
            display: flex;
            align-items: center;
            color: #67c23a;
            font-size: 14px;
            
            .el-icon {
              margin-right: 4px;
            }
          }
          
          .test-error {
            display: flex;
            align-items: center;
            color: #f56c6c;
            font-size: 14px;
            
            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
