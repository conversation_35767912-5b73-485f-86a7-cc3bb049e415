<!DOCTYPE html><html lang="zh-Hans"><head><meta charset="UTF-8"><meta http-equiv="x-ua-compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1"><meta name="renderer" content="webkit"><meta name="keywords" content="股票,炒股,股票资讯,行情,财经,证券,投资,金融,港股,美股,基金,债券,期货,外汇,科创板,保险,雪球财经,雪球,雪球股票,投资社区,雪球投资"><meta name="description" content="雪球，聪明的投资者都在这里 - 6300万投资者都在用的投资社区和财富管理平台，沪深、港股、美股全球市场实时行情，公募私募股票基金债券免费热点资讯，与投资高手实战交流。支持股票基金在线开户，炒股、投资理财低佣金，交易安全、方便、快捷。提供选基工具、基金估值工具、基金定投、基金排名、指数估值、投资组合供投资者使用参考。"><meta name="format-detection" content="telephone=no"><meta name="baidu-site-verification" content="codeva-Q0pduO3YRC"><meta content="always" name="referrer"><link href="//xqdoc.imedao.com/17aebcfb84a145d33fc18679.ico" rel="shortcut icon"><link link rel="manifest" href="//xqdoc.imedao.com/18c3cec1ce530fef3fe4b4d7.json"><link rel="stylesheet" href="//assets.imedao.com/ugc/css/component/index-43d7534b60.css"><title>持仓盈亏&nbsp;-&nbsp;雪球</title><link rel="stylesheet" href="//assets.imedao.com/ugc/css/performance-93dab0e522.css"><script src="//g.alicdn.com/frontend-lib/frontend-lib/2.3.74/interfaceacting.js"></script><script src="//g.alicdn.com/frontend-lib/frontend-lib/2.3.74/antidom.js"></script><script src="//o.alicdn.com/frontend-lib/common-lib/jquery.min.js"></script><script>window.SNOWMAN_USER = {"id":4380321271,"name":null,"province":null,"city":null,"location":null,"description":null,"url":null,"domain":null,"gender":null,"verified":false,"type":"1","step":"null","profile":"/4380321271","recommend":null,"intro":null,"status":0,"following":false,"blocking":false,"subscribeable":false,"remark":null,"constrained":0,"screen_name":"用户4380321271","created_at":1755488536364,"followers_count":0,"friends_count":0,"status_count":0,"last_status_id":0,"blog_description":null,"st_color":"1","stocks_count":0,"cube_count":0,"donate_count":0,"verified_type":0,"verified_description":null,"verified_realname":false,"stock_status_count":null,"follow_me":false,"allow_all_stock":false,"name_pinyin":null,"screenname_pinyin":null,"group_ids":null,"common_count":0,"recommend_reason":null,"verified_infos":[],"select_company_background":null,"select_company_banners":null,"privacy_agreement":null,"ip_location":"未知","reg_time":1755488536383,"national_network_verify":null,"photo_domain":"//xavatar.imedao.com/","profile_image_url":"community/default/avatar.png,community/default/avatar.png!180x180.png,community/default/avatar.png!50x50.png,community/default/avatar.png!30x30.png"};</script><script>window.SNOWMAN_LOGIN = true;
if(window.SNOWMAN_USER && !window.SNOWMAN_USER.id){
  //- window.location.reload();
}</script><script>window.SNB = {
  staticDomain: 'https://assets.imedao.com/'
};</script></head><body><div id="app"><div class="nav__placeholder"></div><nav class="nav stickyFixed"><div class="container"><div style="float:left"><div class="logo" id="logo"><a class="nav__logo" href="/" data-analytics="1036" data-analytics-page="1000"></a></div><div class="home"><a href="/" data-analytics="1011" data-analytics-page="1000"><img class="logo-type" src="https://xqimg.imedao.com/18cc98da6c19cdc03fdb7c76.png" alt="雪球" data-analytics="1011" data-analytics-page="1000"><span data-analytics="1011" data-analytics-page="1000">首页</span></a></div></div><div class="navr"><search></search><nav-info></nav-info></div></div></nav><div class="performance__container__wrap red-rise-green-fall"><div class="performance__container"><div class="user__col--middle no_border"><div class="moni-create"><div class="moni-create__introduce"><h3>欢迎使用模拟盈亏功能</h3><ul><li> <img src="https://xqimg.imedao.com/162d22dff34312d33fd729c9.png"><h4>三大市场</h4><span>A股、港股、美股交易</span></li><li> <img src="https://xqimg.imedao.com/162d22dff09314033fd508e8.png"><h4>即时成交</h4><span>7*24小时即时成交</span></li><li><img src="https://xqimg.imedao.com/162d22dff0b312823fe06901.png"><h4>收益计算</h4><span>根据实时行情统计收益</span></li><li><img src="https://xqimg.imedao.com/162d22dff3b312d43feb9f3b.png"><h4>记账平台</h4><span>订单可按需求修改</span></li></ul></div><div class="moni-create__btn"><button>创建模拟盈亏</button></div></div><div class="performance-postion"><moni-tab></moni-tab><moni-position></moni-position></div></div></div><page-footer></page-footer><div class="modals dimmer"><modals></modals><confirms></confirms><alert></alert><alert-pay-success></alert-pay-success></div><div class="toast"><i class="iconfont">&#xe630;</i><i class="iconfont error-icon">&#xe631;</i><i class="iconfont warn-icon">&#xe632;</i><i class="iconfont loading-icon">&#xe64a;</i><span></span></div><tooltip></tooltip></div><upgrade-tip></upgrade-tip><widget-download-app></widget-download-app></div><script src="//assets.imedao.com/ugc/js/jquery-3-bf5be4b4bc.1.1.js" rel="preload" as="script"></script><script src="//assets.imedao.com/ugc/js/polyfill-7-1a461e6e65.4.4.js" rel="preload" as="script"></script><script src="//assets.imedao.com/ugc/js/raven-ff524be6f0.min.js"></script><script>Raven.config('https://<EMAIL>/10').install();
Raven.setUserContext({
  id: SNOWMAN_USER.id || -1,
  username: SNOWMAN_USER.screen_name || '',
  ip_address: '**************'
});</script><script src="//assets.imedao.com/ugc/js/medium-editor-bd85ca59df.js"></script><script src="//assets.imedao.com/ugc/js/page-a4bb14f83d.js"></script><script>var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "//hm.baidu.com/hm.js?1db88642e346389874251b5a1eded6e3";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();</script><script src="//assets.imedao.com/ugc/js/apm-4-df4081db31.1.js"></script><script>if (window.SNOWMAN_LOGIN  && window.SNOWMAN_USER && !window.SNOWMAN_USER.id) {
  $.subscribe('vue-ready', (event, bus) => {
    bus.$on('confirm-submit', () => {
      window.location.reload();
    });
    bus.$emit('confirm', 1, {
      title: window.SNOWMAN_USER.error_description || "登陆状态失效，请点击确定刷新",
    });
  });
}
if(window.SNOWMAN_FrequentUser){
  $.subscribe('vue-ready', (event, bus) => {
    bus.$on('confirm-submit', () => {
      bus.$emit('confirm', 0);
    });
    bus.$emit('confirm', 1, {
      title: '请求过于频繁，稍后重试',
    });
  });
}
if(window && window.elasticApm){
  var apm = window.elasticApm.init({
    serviceName: 'snowman-web',
    serverUrl: 'https://open.xueqiu.com/es-apm',
    transactionSampleRate: 0.1,
  })
  var apmTransactionName = location.pathname;

  if (apmTransactionName.match(/^\/S\//i)) {
    //- 个股页
    apmTransactionName = 'stock pages';
  } else if (apmTransactionName.match(/^\/$/)){
    //- 首页
    apmTransactionName = 'home page';
  } else if (apmTransactionName.match(/^\/[0-9]{10}\/[0-9]{8}/)) {
    //- 文章页
    apmTransactionName = 'article pages';
  } else if (apmTransactionName.match(/^\/(u\/|[0-9]{10})/)) {
    //- 个人页
    apmTransactionName = 'profile pages';
  } else {
    //- 其他页面
    apmTransactionName = 'other pages';
  }
  apm.setInitialPageLoadName(apmTransactionName);
  if (window.SNOWMAN_LOGIN) {
    apm.setUserContext({
      id: window.SNOWMAN_USER.id,
      username: window.SNOWMAN_USER.screen_name
    })
  }
}</script><script src="//assets.imedao.com/ugc/js/performance-a481f6c407.js"></script><script src="//assets.imedao.com/ugc/js/vue-web-2d2730e699.js"></script></body></html>