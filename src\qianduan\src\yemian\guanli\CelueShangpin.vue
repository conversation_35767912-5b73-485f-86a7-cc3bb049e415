<template>
  <div class="strategy-product-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">策略商品管理</span>
          <div class="header-actions">
            <el-button type="success" @click="handleAuditAll">
              <el-icon><Select /></el-icon>
              批量审核
            </el-button>
            <el-button type="primary" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="strategy-content">
        <!-- 统计信息 -->
        <div class="strategy-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ strategyStats.total }}</div>
                <div class="stat-label">策略总数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value active">{{ strategyStats.active }}</div>
                <div class="stat-label">上架中</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value pending">{{ strategyStats.pending }}</div>
                <div class="stat-label">待审核</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value revenue">¥{{ formatMoney(strategyStats.totalRevenue) }}</div>
                <div class="stat-label">总销售额</div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 搜索筛选 -->
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索策略名称或作者"
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select v-model="statusFilter" placeholder="状态" style="width: 120px">
            <el-option label="全部状态" value="" />
            <el-option label="上架中" value="active" />
            <el-option label="已下架" value="inactive" />
            <el-option label="已删除" value="deleted" />
          </el-select>
          
          <el-select v-model="categoryFilter" placeholder="分类" style="width: 140px">
            <el-option label="全部分类" value="" />
            <el-option label="趋势策略" value="趋势策略" />
            <el-option label="套利策略" value="套利策略" />
            <el-option label="量化策略" value="量化策略" />
            <el-option label="其他策略" value="其他策略" />
          </el-select>
        </div>
        
        <!-- 策略表格 -->
        <el-table :data="filteredStrategies" style="width: 100%" v-loading="loading">
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="id" label="策略ID" width="120">
            <template #default="{ row }">
              <el-link type="primary" @click="handleViewStrategy(row)">
                {{ row.id }}
              </el-link>
            </template>
          </el-table-column>
          
          <el-table-column prop="name" label="策略名称" width="200" show-overflow-tooltip />
          
          <el-table-column prop="creator_name" label="作者" width="120" />
          
          <el-table-column prop="category" label="分类" width="120">
            <template #default="{ row }">
              <el-tag size="small">{{ row.category }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="price" label="价格" width="100">
            <template #default="{ row }">
              <span class="price-text">¥{{ row.price }}</span>
              <span v-if="row.original_price" class="original-price">¥{{ row.original_price }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="sales_count" label="销量" width="80" />
          
          <el-table-column prop="rating" label="评分" width="100">
            <template #default="{ row }">
              <el-rate
                v-model="row.rating"
                disabled
                show-score
                text-color="#ff9900"
                score-template="{value}"
                size="small"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="create_time" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.create_time) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button type="info" size="small" @click="handleViewStrategy(row)">
                详情
              </el-button>
              <el-button 
                v-if="row.status === 'active'"
                type="warning" 
                size="small" 
                @click="handleOfflineStrategy(row)"
              >
                下架
              </el-button>
              <el-button 
                v-if="row.status === 'inactive'"
                type="success" 
                size="small" 
                @click="handleOnlineStrategy(row)"
              >
                上架
              </el-button>
              <el-button type="danger" size="small" @click="handleDeleteStrategy(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50]"
            :total="totalStrategies"
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
      </div>
    </el-card>
    
    <!-- 策略详情弹窗 -->
    <CelueXiangqing
      v-model="showStrategyDetail"
      :strategy-id="selectedStrategyId"
      @refresh="loadStrategies"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { orderService } from '@/fuwu/orderService'
import CelueXiangqing from './zujian/CelueXiangqing.vue'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const categoryFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalStrategies = ref(0)

// 弹窗控制
const showStrategyDetail = ref(false)
const selectedStrategyId = ref('')

// 策略数据
const strategies = ref<any[]>([])
const strategyStats = ref({
  total: 0,
  active: 0,
  pending: 0,
  totalRevenue: 0
})

// 计算属性
const filteredStrategies = computed(() => {
  let filtered = strategies.value
  
  if (searchKeyword.value) {
    filtered = filtered.filter(strategy => 
      strategy.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      strategy.creator_name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(strategy => strategy.status === statusFilter.value)
  }
  
  if (categoryFilter.value) {
    filtered = filtered.filter(strategy => strategy.category === categoryFilter.value)
  }
  
  return filtered
})

// 工具函数
const formatMoney = (amount: number) => {
  return amount.toLocaleString()
}

const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '上架中',
    inactive: '已下架',
    deleted: '已删除'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    inactive: 'warning',
    deleted: 'danger'
  }
  return statusMap[status] || 'info'
}

// 方法
const loadStrategies = async () => {
  try {
    loading.value = true
    const products = await orderService.getStrategyProducts()
    strategies.value = products
    totalStrategies.value = products.length
    
    // 计算统计数据
    strategyStats.value = {
      total: products.length,
      active: products.filter(p => p.status === 'active').length,
      pending: products.filter(p => p.status === 'inactive').length,
      totalRevenue: products.reduce((sum, p) => sum + (p.price * p.sales_count), 0)
    }
  } catch (error) {
    console.error('加载策略数据失败:', error)
    ElMessage.error('加载策略数据失败')
  } finally {
    loading.value = false
  }
}

const handleViewStrategy = (strategy: any) => {
  selectedStrategyId.value = strategy.id
  showStrategyDetail.value = true
}

const handleOnlineStrategy = async (strategy: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要上架策略 "${strategy.name}" 吗？`,
      '上架策略',
      {
        confirmButtonText: '确定上架',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    strategy.status = 'active'
    ElMessage.success('策略已上架')
    loadStrategies()
  } catch {
    // 用户取消操作
  }
}

const handleOfflineStrategy = async (strategy: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要下架策略 "${strategy.name}" 吗？`,
      '下架策略',
      {
        confirmButtonText: '确定下架',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    strategy.status = 'inactive'
    ElMessage.success('策略已下架')
    loadStrategies()
  } catch {
    // 用户取消操作
  }
}

const handleDeleteStrategy = async (strategy: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除策略 "${strategy.name}" 吗？此操作不可恢复！`,
      '删除策略',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    strategy.status = 'deleted'
    ElMessage.success('策略已删除')
    loadStrategies()
  } catch {
    // 用户取消操作
  }
}

const handleAuditAll = () => {
  ElMessage.info('批量审核功能开发中')
}

const handleRefresh = () => {
  loadStrategies()
}

// 监听器
watch([searchKeyword, statusFilter, categoryFilter], () => {
  // 搜索和筛选是前端处理，不需要重新加载数据
})

// 生命周期
onMounted(() => {
  loadStrategies()
})
</script>

<style lang="scss" scoped>
.strategy-product-container {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .card-title {
    font-size: 18px;
    font-weight: 600;
  }

  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.strategy-content {
  .strategy-stats {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .stat-item {
      text-align: center;

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;

        &.active {
          color: #67c23a;
        }

        &.pending {
          color: #e6a23c;
        }

        &.revenue {
          color: #409eff;
        }
      }

      .stat-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }

  .search-bar {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
  }

  .price-text {
    font-weight: 600;
    color: #f56c6c;
    margin-right: 8px;
  }

  .original-price {
    font-size: 12px;
    color: var(--el-text-color-placeholder);
    text-decoration: line-through;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    align-items: stretch;

    .el-input,
    .el-select {
      width: 100% !important;
    }
  }

  .strategy-stats {
    .el-row {
      flex-direction: column;
    }

    .el-col {
      width: 100% !important;
      margin-bottom: 10px;
    }
  }
}
</style>
