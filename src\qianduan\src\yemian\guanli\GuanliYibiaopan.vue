<template>
  <div class="admin-dashboard-container">
    <!-- 路由测试按钮 -->
    <div class="test-buttons" style="margin-bottom: 20px;">
      <el-button type="primary" @click="testSubscriptionRoute">
        测试订阅管理路由
      </el-button>
      <el-button type="success" @click="checkPermissions">
        检查权限
      </el-button>
    </div>

    <!-- 管理员统计卡片 -->
    <div class="admin-stats-grid">
      <el-card class="admin-stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon users">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ adminStats.totalUsers }}</div>
            <div class="stat-label">总用户数</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="admin-stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon active">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ adminStats.activeUsers }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="admin-stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon strategies">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ adminStats.totalStrategies }}</div>
            <div class="stat-label">策略总数</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="admin-stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon system">
            <el-icon><Monitor /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value system-health" :class="systemHealthClass">
              {{ systemHealthText }}
            </div>
            <div class="stat-label">系统状态</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="admin-main-content">
      <!-- 左侧图表区域 -->
      <div class="admin-chart-section">
        <el-card class="admin-chart-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">用户增长趋势</span>
              <div class="chart-controls">
                <el-radio-group v-model="chartPeriod" size="small">
                  <el-radio-button label="7D">7天</el-radio-button>
                  <el-radio-button label="30D">30天</el-radio-button>
                  <el-radio-button label="90D">90天</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          
          <div class="chart-container">
            <v-chart 
              :option="userGrowthChartOption" 
              :loading="chartLoading"
              class="user-growth-chart"
              autoresize
            />
          </div>
        </el-card>
      </div>
      
      <!-- 右侧信息面板 -->
      <div class="admin-info-section">
        <!-- 最新用户 -->
        <el-card class="admin-info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">最新注册用户</span>
              <el-button type="primary" size="small" @click="$router.push('/numendavid中国/users')">
                管理用户
              </el-button>
            </div>
          </template>
          
          <div class="recent-users-list">
            <div 
              v-for="user in recentUsers" 
              :key="user.id"
              class="user-item"
            >
              <div class="user-info">
                <div class="user-name">{{ user.yonghuming }}</div>
                <div class="user-email">{{ user.youxiang }}</div>
              </div>
              <div class="user-status">
                <el-tag 
                  :type="user.shi_huoyue ? 'success' : 'info'"
                  size="small"
                >
                  {{ user.shi_huoyue ? '活跃' : '未激活' }}
                </el-tag>
              </div>
            </div>
            
            <div v-if="recentUsers.length === 0" class="empty-state">
              <el-empty description="暂无新用户" :image-size="80" />
            </div>
          </div>
        </el-card>
        
        <!-- 系统监控 -->
        <el-card class="admin-info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">系统监控</span>
              <el-button type="primary" size="small" @click="refreshSystemInfo">
                刷新
              </el-button>
            </div>
          </template>
          
          <div class="system-monitor">
            <div class="monitor-item">
              <div class="monitor-label">CPU使用率</div>
              <el-progress :percentage="systemInfo.cpuUsage" :color="getProgressColor(systemInfo.cpuUsage)" />
            </div>
            
            <div class="monitor-item">
              <div class="monitor-label">内存使用率</div>
              <el-progress :percentage="systemInfo.memoryUsage" :color="getProgressColor(systemInfo.memoryUsage)" />
            </div>
            
            <div class="monitor-item">
              <div class="monitor-label">磁盘使用率</div>
              <el-progress :percentage="systemInfo.diskUsage" :color="getProgressColor(systemInfo.diskUsage)" />
            </div>
            
            <div class="monitor-item">
              <div class="monitor-label">数据库连接</div>
              <div class="monitor-value">
                <el-tag :type="systemInfo.dbConnections > 80 ? 'danger' : 'success'" size="small">
                  {{ systemInfo.dbConnections }}/100
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
        
        <!-- 操作日志 -->
        <el-card class="admin-info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">最新操作日志</span>
              <el-button type="primary" size="small" @click="$router.push('/numendavid中国/security')">
                查看全部
              </el-button>
            </div>
          </template>
          
          <div class="operation-logs">
            <div 
              v-for="log in recentLogs" 
              :key="log.id"
              class="log-item"
            >
              <div class="log-content">
                <div class="log-action">{{ log.action }}</div>
                <div class="log-time">{{ formatTime(log.timestamp) }}</div>
              </div>
              <div class="log-user">{{ log.user }}</div>
            </div>
            
            <div v-if="recentLogs.length === 0" class="empty-state">
              <el-empty description="暂无操作日志" :image-size="80" />
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { use } from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import dayjs from 'dayjs'
import { useAdminStore } from '@/shangdian/admin'

// 注册ECharts组件
use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

const adminStore = useAdminStore()
const router = useRouter()

// 测试方法
const testSubscriptionRoute = () => {
  console.log('测试订阅管理路由...')
  console.log('管理员登录状态:', adminStore.isAdminLoggedIn)
  console.log('管理员权限:', adminStore.adminPermissions)
  console.log('是否有订阅管理权限:', adminStore.hasAdminPermission('subscription_management'))

  router.push('/numendavid中国/subscriptions').catch(err => {
    console.error('路由跳转失败:', err)
    ElMessage.error('路由跳转失败: ' + err.message)
  })
}

const checkPermissions = () => {
  console.log('=== 权限检查 ===')
  console.log('管理员登录状态:', adminStore.isAdminLoggedIn)
  console.log('管理员信息:', adminStore.adminInfo)
  console.log('管理员权限列表:', adminStore.adminPermissions)
  console.log('订阅管理权限:', adminStore.hasAdminPermission('subscription_management'))

  ElMessage.info(`权限检查完成，请查看控制台。订阅管理权限: ${adminStore.hasAdminPermission('subscription_management') ? '有' : '无'}`)
}

// 响应式数据
const chartPeriod = ref('30D')
const chartLoading = ref(false)

// 管理员统计数据
const adminStats = ref({
  totalUsers: 1250,
  activeUsers: 890,
  totalStrategies: 3420,
  systemHealth: 'good' as 'good' | 'warning' | 'error'
})

// 系统信息
const systemInfo = ref({
  cpuUsage: 35,
  memoryUsage: 68,
  diskUsage: 45,
  dbConnections: 23
})

// 最新用户
const recentUsers = ref([
  { id: 1, yonghuming: 'trader001', youxiang: '<EMAIL>', shi_huoyue: true },
  { id: 2, yonghuming: 'investor_pro', youxiang: '<EMAIL>', shi_huoyue: false },
  { id: 3, yonghuming: 'quant_master', youxiang: '<EMAIL>', shi_huoyue: true }
])

// 操作日志
const recentLogs = ref([
  { id: 1, action: '用户登录', user: 'trader001', timestamp: new Date() },
  { id: 2, action: '创建策略', user: 'investor_pro', timestamp: new Date(Date.now() - 300000) },
  { id: 3, action: '系统配置更新', user: 'superadmin', timestamp: new Date(Date.now() - 600000) }
])

// 计算属性
const systemHealthClass = computed(() => ({
  'health-good': adminStats.value.systemHealth === 'good',
  'health-warning': adminStats.value.systemHealth === 'warning',
  'health-error': adminStats.value.systemHealth === 'error'
}))

const systemHealthText = computed(() => {
  const healthMap = {
    good: '正常',
    warning: '警告',
    error: '异常'
  }
  return healthMap[adminStats.value.systemHealth]
})

// 用户增长图表配置
const userGrowthChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const data = params[0]
      return `${data.name}<br/>新增用户: ${data.value}人`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: generateDateRange(),
    axisLine: {
      lineStyle: {
        color: '#e4e7ed'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => `${value}人`
    },
    axisLine: {
      lineStyle: {
        color: '#e4e7ed'
      }
    },
    splitLine: {
      lineStyle: {
        color: '#f5f7fa'
      }
    }
  },
  series: [
    {
      name: '新增用户',
      type: 'line',
      data: generateUserGrowthData(),
      smooth: true,
      lineStyle: {
        color: '#f59e0b',
        width: 3
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(245, 158, 11, 0.3)' },
            { offset: 1, color: 'rgba(245, 158, 11, 0.05)' }
          ]
        }
      }
    }
  ]
}))

// 工具函数
const formatTime = (time: Date) => {
  return dayjs(time).format('HH:mm')
}

const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const generateDateRange = () => {
  const days = chartPeriod.value === '7D' ? 7 : chartPeriod.value === '30D' ? 30 : 90
  const dates = []
  for (let i = days - 1; i >= 0; i--) {
    dates.push(dayjs().subtract(i, 'day').format('MM-DD'))
  }
  return dates
}

const generateUserGrowthData = () => {
  const days = chartPeriod.value === '7D' ? 7 : chartPeriod.value === '30D' ? 30 : 90
  const data = []
  
  for (let i = 0; i < days; i++) {
    const baseValue = Math.floor(Math.random() * 20) + 5
    data.push(baseValue)
  }
  
  return data
}

const refreshSystemInfo = () => {
  // 模拟刷新系统信息
  systemInfo.value = {
    cpuUsage: Math.floor(Math.random() * 100),
    memoryUsage: Math.floor(Math.random() * 100),
    diskUsage: Math.floor(Math.random() * 100),
    dbConnections: Math.floor(Math.random() * 100)
  }
}

// 生命周期
onMounted(() => {
  console.log('管理员仪表板已加载')
})
</script>

<style lang="scss" scoped>
.admin-dashboard-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  
  .admin-stat-card {
    border-radius: 8px;
    
    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        
        &.users {
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          color: white;
        }
        
        &.active {
          background: linear-gradient(135deg, #10b981, #059669);
          color: white;
        }
        
        &.strategies {
          background: linear-gradient(135deg, #f59e0b, #d97706);
          color: white;
        }
        
        &.system {
          background: linear-gradient(135deg, #8b5cf6, #7c3aed);
          color: white;
        }
      }
      
      .stat-info {
        flex: 1;
        
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
          
          &.system-health {
            font-size: 18px;
            
            &.health-good {
              color: #10b981;
            }
            
            &.health-warning {
              color: #f59e0b;
            }
            
            &.health-error {
              color: #ef4444;
            }
          }
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

.admin-main-content {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  min-height: 0;
}

.admin-chart-section {
  .admin-chart-card {
    height: 100%;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-title {
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .chart-container {
      height: 400px;
      
      .user-growth-chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.admin-info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  .admin-info-card {
    flex: 1;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-title {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
}

.recent-users-list,
.operation-logs {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.user-item,
.log-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  
  &:last-child {
    border-bottom: none;
  }
}

.user-info,
.log-content {
  flex: 1;
  
  .user-name,
  .log-action {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
  
  .user-email,
  .log-time {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-top: 2px;
  }
}

.log-user {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.system-monitor {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  .monitor-item {
    .monitor-label {
      font-size: 14px;
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
    }
    
    .monitor-value {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
}

// 响应式设计
@media (max-width: 1200px) {
  .admin-main-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  .admin-info-section {
    flex-direction: row;
    
    .admin-info-card {
      flex: 1;
    }
  }
}

@media (max-width: 768px) {
  .admin-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .admin-info-section {
    flex-direction: column;
  }
  
  .chart-container {
    height: 300px !important;
  }
}
</style>
