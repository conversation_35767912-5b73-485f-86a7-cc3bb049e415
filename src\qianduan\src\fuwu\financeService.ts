// 财务服务 - 处理收入统计、支付管理、退款处理等

export interface FinanceStats {
  today_revenue: number
  yesterday_revenue: number
  week_revenue: number
  month_revenue: number
  year_revenue: number
  total_revenue: number
  
  today_orders: number
  week_orders: number
  month_orders: number
  
  refund_amount: number
  refund_rate: number
  
  avg_order_value: number
  conversion_rate: number
}

export interface PaymentChannel {
  id: string
  name: string
  type: 'alipay' | 'wechat' | 'bank' | 'balance'
  status: 'active' | 'inactive'
  fee_rate: number // 手续费率 %
  daily_limit: number
  monthly_limit: number
  total_amount: number
  total_fee: number
  create_time: string
}

export interface RefundRequest {
  id: string
  order_id: string
  user_id: number
  user_name: string
  order_amount: number
  refund_amount: number
  refund_reason: string
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  apply_time: string
  process_time?: string
  processor?: string
  remark?: string
}

export interface FinanceReport {
  date: string
  revenue: number
  orders: number
  refunds: number
  net_revenue: number
  new_users: number
  active_users: number
}

// 模拟财务数据库
class FinanceDatabase {
  private paymentChannels: PaymentChannel[] = []
  private refundRequests: RefundRequest[] = []
  private dailyReports: FinanceReport[] = []

  constructor() {
    this.initializeData()
  }

  private initializeData() {
    // 初始化支付渠道
    this.paymentChannels = [
      {
        id: 'alipay_001',
        name: '支付宝',
        type: 'alipay',
        status: 'active',
        fee_rate: 0.6,
        daily_limit: 1000000,
        monthly_limit: ********,
        total_amount: 2580000,
        total_fee: 15480,
        create_time: '2024-01-01T00:00:00Z'
      },
      {
        id: 'wechat_001',
        name: '微信支付',
        type: 'wechat',
        status: 'active',
        fee_rate: 0.6,
        daily_limit: 1000000,
        monthly_limit: ********,
        total_amount: 1920000,
        total_fee: 11520,
        create_time: '2024-01-01T00:00:00Z'
      },
      {
        id: 'bank_001',
        name: '银行卡支付',
        type: 'bank',
        status: 'active',
        fee_rate: 0.5,
        daily_limit: 500000,
        monthly_limit: ********,
        total_amount: 680000,
        total_fee: 3400,
        create_time: '2024-01-01T00:00:00Z'
      }
    ]

    // 生成退款申请
    this.generateRefundRequests()
    
    // 生成财务报表数据
    this.generateDailyReports()
  }

  private generateRefundRequests() {
    const reasons = [
      '策略效果不佳',
      '误操作购买',
      '重复购买',
      '不满意服务',
      '技术问题',
      '其他原因'
    ]

    for (let i = 0; i < 25; i++) {
      const applyTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      const orderAmount = [99, 199, 299, 599][Math.floor(Math.random() * 4)]
      const refundAmount = Math.random() > 0.8 ? orderAmount * 0.8 : orderAmount // 80%概率全额退款
      
      const statuses = ['pending', 'approved', 'rejected', 'completed']
      const status = statuses[Math.floor(Math.random() * statuses.length)] as any
      
      this.refundRequests.push({
        id: `refund_${Date.now()}_${i}`,
        order_id: `ORD${Date.now()}${i.toString().padStart(3, '0')}`,
        user_id: Math.floor(Math.random() * 100) + 1,
        user_name: `user${Math.floor(Math.random() * 100) + 1}`,
        order_amount: orderAmount,
        refund_amount: Math.round(refundAmount),
        refund_reason: reasons[Math.floor(Math.random() * reasons.length)],
        status,
        apply_time: applyTime.toISOString(),
        process_time: status !== 'pending' ? 
          new Date(applyTime.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString() : 
          undefined,
        processor: status !== 'pending' ? 'admin' : undefined
      })
    }
  }

  private generateDailyReports() {
    // 生成最近90天的财务报表
    for (let i = 89; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
      const baseRevenue = 15000 + Math.random() * 10000 // 15k-25k基础收入
      const weekendFactor = [0, 6].includes(date.getDay()) ? 0.7 : 1 // 周末收入降低
      const revenue = Math.round(baseRevenue * weekendFactor)
      
      const orders = Math.floor(revenue / 200) + Math.floor(Math.random() * 20)
      const refunds = Math.floor(orders * 0.05) // 5%退款率
      const refundAmount = refunds * 200
      
      this.dailyReports.push({
        date: date.toISOString().split('T')[0],
        revenue,
        orders,
        refunds: refundAmount,
        net_revenue: revenue - refundAmount,
        new_users: Math.floor(Math.random() * 50) + 10,
        active_users: Math.floor(Math.random() * 200) + 100
      })
    }
  }

  // 获取财务统计
  getFinanceStats(): FinanceStats {
    const today = new Date()
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
    const yearAgo = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000)

    const todayStr = today.toISOString().split('T')[0]
    const yesterdayStr = yesterday.toISOString().split('T')[0]

    const todayReport = this.dailyReports.find(r => r.date === todayStr)
    const yesterdayReport = this.dailyReports.find(r => r.date === yesterdayStr)
    
    const weekReports = this.dailyReports.filter(r => new Date(r.date) >= weekAgo)
    const monthReports = this.dailyReports.filter(r => new Date(r.date) >= monthAgo)
    const yearReports = this.dailyReports.filter(r => new Date(r.date) >= yearAgo)

    const weekRevenue = weekReports.reduce((sum, r) => sum + r.revenue, 0)
    const monthRevenue = monthReports.reduce((sum, r) => sum + r.revenue, 0)
    const yearRevenue = yearReports.reduce((sum, r) => sum + r.revenue, 0)
    const totalRevenue = this.dailyReports.reduce((sum, r) => sum + r.revenue, 0)

    const weekOrders = weekReports.reduce((sum, r) => sum + r.orders, 0)
    const monthOrders = monthReports.reduce((sum, r) => sum + r.orders, 0)

    const totalRefunds = this.dailyReports.reduce((sum, r) => sum + r.refunds, 0)
    const totalOrders = this.dailyReports.reduce((sum, r) => sum + r.orders, 0)

    return {
      today_revenue: todayReport?.revenue || 0,
      yesterday_revenue: yesterdayReport?.revenue || 0,
      week_revenue: weekRevenue,
      month_revenue: monthRevenue,
      year_revenue: yearRevenue,
      total_revenue: totalRevenue,
      
      today_orders: todayReport?.orders || 0,
      week_orders: weekOrders,
      month_orders: monthOrders,
      
      refund_amount: totalRefunds,
      refund_rate: totalOrders > 0 ? (totalRefunds / totalRevenue) * 100 : 0,
      
      avg_order_value: totalOrders > 0 ? totalRevenue / totalOrders : 0,
      conversion_rate: 12.5 // 模拟转化率
    }
  }

  // 获取支付渠道
  getPaymentChannels(): PaymentChannel[] {
    return this.paymentChannels
  }

  // 获取退款申请
  getRefundRequests(status?: string): RefundRequest[] {
    if (status) {
      return this.refundRequests.filter(r => r.status === status)
    }
    return this.refundRequests
  }

  // 处理退款申请
  processRefund(refundId: string, action: 'approve' | 'reject', remark?: string): boolean {
    const refund = this.refundRequests.find(r => r.id === refundId)
    if (!refund || refund.status !== 'pending') return false

    refund.status = action === 'approve' ? 'approved' : 'rejected'
    refund.process_time = new Date().toISOString()
    refund.processor = 'admin'
    if (remark) refund.remark = remark

    return true
  }

  // 获取财务报表
  getFinanceReports(days: number = 30): FinanceReport[] {
    return this.dailyReports.slice(-days)
  }

  // 获取收入趋势
  getRevenueTrend(days: number = 30): { date: string, revenue: number }[] {
    return this.dailyReports.slice(-days).map(r => ({
      date: r.date,
      revenue: r.revenue
    }))
  }

  // 更新支付渠道
  updatePaymentChannel(channelId: string, updates: Partial<PaymentChannel>): boolean {
    const channel = this.paymentChannels.find(c => c.id === channelId)
    if (!channel) return false

    Object.assign(channel, updates)
    return true
  }
}

// 创建全局财务数据库实例
const financeDB = new FinanceDatabase()

// 导出财务服务
export const financeService = {
  // 获取财务统计
  getFinanceStats: async () => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return financeDB.getFinanceStats()
  },

  // 获取支付渠道
  getPaymentChannels: async () => {
    await new Promise(resolve => setTimeout(resolve, 200))
    return financeDB.getPaymentChannels()
  },

  // 获取退款申请
  getRefundRequests: async (status?: string) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return financeDB.getRefundRequests(status)
  },

  // 处理退款
  processRefund: async (refundId: string, action: 'approve' | 'reject', remark?: string) => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return financeDB.processRefund(refundId, action, remark)
  },

  // 获取财务报表
  getFinanceReports: async (days: number = 30) => {
    await new Promise(resolve => setTimeout(resolve, 400))
    return financeDB.getFinanceReports(days)
  },

  // 获取收入趋势
  getRevenueTrend: async (days: number = 30) => {
    await new Promise(resolve => setTimeout(resolve, 300))
    return financeDB.getRevenueTrend(days)
  },

  // 更新支付渠道
  updatePaymentChannel: async (channelId: string, updates: Partial<PaymentChannel>) => {
    await new Promise(resolve => setTimeout(resolve, 400))
    return financeDB.updatePaymentChannel(channelId, updates)
  }
}

export default financeService
