#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa量化交易系统启动脚本
"""

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

def main():
    """启动VeighNa交易系统"""
    print("🚀 启动VeighNa量化交易系统")
    print("=" * 60)
    
    # 创建Qt应用
    qapp = create_qapp()
    
    # 创建事件引擎
    event_engine = EventEngine()
    
    # 创建主引擎
    main_engine = MainEngine(event_engine)
    
    # 创建主窗口
    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()
    
    print("✅ VeighNa交易系统启动成功")
    print("💡 功能说明:")
    print("   📊 系统: 连接交易接口、管理账户")
    print("   📈 功能: 策略开发、数据管理、风险控制")
    print("   🔧 扩展: 可以安装更多交易接口和应用模块")
    
    # 运行应用
    qapp.exec()

if __name__ == "__main__":
    main()
