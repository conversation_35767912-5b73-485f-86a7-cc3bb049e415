#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa量化交易系统启动脚本
"""

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

# 导入应用模块
try:
    from vnpy_ctastrategy import CtaStrategyApp
    CTA_AVAILABLE = True
except ImportError:
    CTA_AVAILABLE = False
    print("⚠️ CTA策略模块未安装")

try:
    from vnpy_ctabacktester import CtaBacktesterApp
    BACKTEST_AVAILABLE = True
except ImportError:
    BACKTEST_AVAILABLE = False
    print("⚠️ CTA回测模块未安装")

try:
    from vnpy_paperaccount import PaperAccountApp
    PAPER_AVAILABLE = True
except ImportError:
    PAPER_AVAILABLE = False
    print("⚠️ 模拟账户模块未安装")

try:
    from vnpy_datamanager import DataManagerApp
    DATA_AVAILABLE = True
except ImportError:
    DATA_AVAILABLE = False
    print("⚠️ 数据管理模块未安装")

try:
    from vnpy_riskmanager import RiskManagerApp
    RISK_AVAILABLE = True
except ImportError:
    RISK_AVAILABLE = False
    print("⚠️ 风险管理模块未安装")

def main():
    """启动VeighNa交易系统"""
    print("🚀 启动VeighNa量化交易系统")
    print("=" * 60)

    # 创建Qt应用
    qapp = create_qapp()

    # 创建事件引擎
    event_engine = EventEngine()

    # 创建主引擎
    main_engine = MainEngine(event_engine)

    # 添加应用模块
    if CTA_AVAILABLE:
        main_engine.add_app(CtaStrategyApp)
        print("✅ CTA策略模块已加载")

    if BACKTEST_AVAILABLE:
        main_engine.add_app(CtaBacktesterApp)
        print("✅ CTA回测模块已加载")

    if PAPER_AVAILABLE:
        main_engine.add_app(PaperAccountApp)
        print("✅ 模拟账户模块已加载")

    if DATA_AVAILABLE:
        main_engine.add_app(DataManagerApp)
        print("✅ 数据管理模块已加载")

    if RISK_AVAILABLE:
        main_engine.add_app(RiskManagerApp)
        print("✅ 风险管理模块已加载")

    # 创建主窗口
    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()

    print("\n✅ VeighNa交易系统启动成功")
    print("💡 功能说明:")
    print("   📊 CTA策略: 趋势跟踪策略开发和运行")
    print("   🔙 策略回测: 历史数据回测验证")
    print("   💰 模拟账户: 虚拟资金模拟交易")
    print("   📈 数据管理: 历史数据下载和管理")
    print("   ⚠️ 风险管理: 实时风险监控")

    # 运行应用
    qapp.exec()

if __name__ == "__main__":
    main()
