# VeighNa ATR RSI策略操作步骤

## 🎯 当前状态

✅ **VeighNa系统已启动** - 图形界面正在运行  
✅ **ATR RSI策略已准备** - 策略文件和配置已创建  
✅ **所有模块已安装** - CTA策略、模拟账户、数据库等  
✅ **策略验证完成** - 策略类正确加载  

## 📋 操作步骤

### 第1步: 确认VeighNa界面已打开 🖥️

您应该能看到VeighNa的主界面，包含以下菜单：
- **系统** - 连接管理、应用管理等
- **功能** - CTA策略、模拟账户、数据管理等
- **帮助** - 关于、文档等

### 第2步: 启动模拟账户 💰

1. **打开模拟账户模块**:
   ```
   点击菜单: 功能 → 模拟账户
   ```

2. **配置模拟账户**:
   ```
   初始资金: 1000000 (一百万)
   手续费率: 0.0001 (万分之一)
   滑点: 0.2
   ```

3. **启动模拟账户**:
   ```
   点击 "启动" 按钮
   确认状态显示为 "运行中"
   ```

### 第3步: 打开CTA策略模块 📈

1. **打开CTA策略**:
   ```
   点击菜单: 功能 → CTA策略
   ```

2. **如果出现错误**:
   - 确认左侧应用列表中有 "CTA策略"
   - 如果没有，检查模块是否正确安装

### 第4步: 添加ATR RSI策略 🎯

1. **新增策略**:
   ```
   在CTA策略界面点击 "新增策略" 按钮
   ```

2. **填写策略信息**:
   ```
   策略类名: AtrRsiStrategy
   策略实例名: atr_rsi_test
   本地代码: IF2312.CFFEX
   ```

3. **配置策略参数**:
   ```
   atr_length: 14
   atr_ma_length: 30
   rsi_length: 14
   rsi_entry: 16
   trailing_percent: 0.8
   fixed_size: 1
   ```

### 第5步: 启动策略 🚀

1. **初始化策略**:
   ```
   选中您的策略实例
   点击 "初始化" 按钮
   等待状态变为 "已初始化"
   ```

2. **启动策略**:
   ```
   点击 "启动" 按钮
   策略状态变为 "交易中"
   ```

3. **监控策略**:
   ```
   观察策略日志
   查看持仓变化
   监控盈亏情况
   ```

## ⚠️ 可能遇到的问题

### 问题1: 找不到策略类 "AtrRsiStrategy"

**解决方案**:
1. 确认 `atr_rsi_strategy.py` 文件在当前目录
2. 重启VeighNa系统
3. 检查策略文件是否有语法错误

### 问题2: 模拟账户启动失败

**解决方案**:
1. 检查是否安装了 `vnpy-paperaccount` 模块
2. 确认没有其他程序占用端口
3. 重启VeighNa系统

### 问题3: CTA策略模块打开失败

**解决方案**:
1. 确认安装了所有必需模块:
   ```bash
   pip install vnpy-ctastrategy vnpy-sqlite
   ```
2. 重启VeighNa系统

### 问题4: 策略初始化失败

**解决方案**:
1. 检查交易品种代码是否正确
2. 确认模拟账户已启动
3. 查看策略日志中的错误信息

## 📊 策略监控

### 实时监控指标

1. **持仓信息**:
   - 当前仓位 (多头/空头/空仓)
   - 持仓数量
   - 平均成本

2. **技术指标**:
   - ATR值和ATR均线
   - RSI值
   - 买入/卖出信号

3. **交易记录**:
   - 委托记录
   - 成交记录
   - 盈亏统计

### 性能分析

1. **收益指标**:
   - 总盈亏
   - 收益率
   - 胜率

2. **风险指标**:
   - 最大回撤
   - 夏普比率
   - 波动率

## 🔧 参数调优

### 保守型参数 (适合新手)
```
atr_length: 20
atr_ma_length: 40
rsi_entry: 20
trailing_percent: 1.0
```

### 激进型参数 (适合有经验者)
```
atr_length: 10
atr_ma_length: 20
rsi_entry: 12
trailing_percent: 0.5
```

### 参数说明

- **atr_length**: ATR计算周期，越大越平滑
- **atr_ma_length**: ATR均线周期，用于判断趋势
- **rsi_length**: RSI计算周期，一般不调整
- **rsi_entry**: RSI入场阈值，越大越严格
- **trailing_percent**: 移动止损百分比，越小越严格

## 📈 策略优化建议

1. **回测验证**: 先用历史数据回测验证策略
2. **参数优化**: 尝试不同参数组合
3. **风险控制**: 设置合理的止损和仓位
4. **定期评估**: 定期检查策略表现

## 🎉 成功标志

当您看到以下情况时，说明策略运行成功：

✅ **模拟账户状态**: "运行中"  
✅ **策略状态**: "交易中"  
✅ **策略日志**: 显示技术指标计算  
✅ **交易信号**: 出现买入/卖出信号  
✅ **持仓变化**: 根据信号产生持仓  

## 📞 获取帮助

如果遇到问题，可以：

1. **查看日志**: 策略运行日志中的错误信息
2. **检查配置**: 确认所有参数设置正确
3. **重启系统**: 重新启动VeighNa系统
4. **查看文档**: VeighNa官方文档和社区

---

**祝您模拟交易成功！** 🚀

记住：这是模拟交易，使用虚拟资金，可以放心测试和学习。
