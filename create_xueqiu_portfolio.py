#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建雪球模拟组合的指导脚本
"""

import webbrowser
import json
import time

def open_xueqiu_create_portfolio():
    """打开雪球创建组合页面"""
    print("🚀 雪球模拟组合创建助手")
    print("=" * 50)
    
    print("📋 当前问题:")
    print("   组合代码 ZH4380321271 在雪球系统中不存在")
    print("   需要创建一个新的模拟组合")
    
    print("\n🎯 解决步骤:")
    print("1. 自动打开雪球网站")
    print("2. 登录你的雪球账户")
    print("3. 创建模拟组合")
    print("4. 获取组合代码")
    print("5. 更新配置文件")
    
    input("\n按回车键继续...")
    
    # 打开雪球主页
    print("🌐 正在打开雪球网站...")
    webbrowser.open('https://xueqiu.com')
    time.sleep(2)
    
    # 打开组合页面
    print("🌐 正在打开组合页面...")
    webbrowser.open('https://xueqiu.com/cubes')
    
    print("\n📝 请按以下步骤操作:")
    print("=" * 30)
    
    print("步骤1: 登录账户")
    print("   - 如果未登录，请点击右上角「登录」")
    print("   - 使用你的手机号和密码登录")
    
    print("\n步骤2: 创建模拟组合")
    print("   - 点击「创建组合」按钮")
    print("   - 选择「模拟组合」类型")
    print("   - 填写组合信息:")
    print("     * 组合名称: 我的量化交易组合")
    print("     * 组合描述: 用于量化交易测试")
    print("     * 初始资金: 1000000 (100万)")
    print("   - 点击「创建」")
    
    print("\n步骤3: 获取组合代码")
    print("   - 创建成功后会跳转到组合页面")
    print("   - 查看浏览器地址栏的URL")
    print("   - 格式如: https://xueqiu.com/P/ZH123456789")
    print("   - 复制其中的组合代码 (ZH开头的部分)")
    
    # 等待用户完成操作
    while True:
        portfolio_code = input("\n请输入获取到的组合代码 (如ZH123456789): ").strip()
        
        if not portfolio_code:
            print("❌ 组合代码不能为空")
            continue
            
        if not portfolio_code.startswith('ZH'):
            print("❌ 组合代码应该以ZH开头")
            continue
            
        if len(portfolio_code) < 8:
            print("❌ 组合代码长度不正确")
            continue
            
        # 更新配置文件
        if update_portfolio_code(portfolio_code):
            print(f"\n✅ 配置已更新为: {portfolio_code}")
            
            # 测试新配置
            if test_new_portfolio(portfolio_code):
                print("\n🎉 恭喜！组合配置成功，现在可以正常使用了！")
                break
            else:
                print("\n⚠️ 组合代码可能还有问题，请检查是否正确")
        else:
            print("\n❌ 配置更新失败")

def update_portfolio_code(portfolio_code):
    """更新配置文件中的组合代码"""
    try:
        with open('xq.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config['portfolio_code'] = portfolio_code
        
        with open('xq.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        print(f"更新配置失败: {e}")
        return False

def test_new_portfolio(portfolio_code):
    """测试新的组合代码"""
    try:
        import easytrader
        
        print(f"🧪 测试组合代码: {portfolio_code}")
        
        user = easytrader.use('xq')
        user.prepare('xq.json')
        
        # 测试账户信息
        balance = user.balance
        print("✅ 账户信息获取成功!")
        
        # 测试持仓信息
        position = user.position
        print("✅ 持仓信息获取成功!")
        print(f"当前持仓数量: {len(position) if position else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_alternative_solution():
    """显示替代解决方案"""
    print("\n🔄 替代解决方案")
    print("=" * 30)
    
    print("如果无法创建组合，可以:")
    print("1. 使用现有功能:")
    print("   ✅ 实时行情查询")
    print("   ✅ 五档买卖盘显示") 
    print("   ✅ 模拟交易操作")
    print("   ✅ 价格快速选择")
    
    print("\n2. 忽略持仓警告:")
    print("   - 持仓信息获取失败不影响核心功能")
    print("   - 可以正常进行买卖操作")
    print("   - 系统会显示默认的账户信息")
    
    print("\n3. 稍后配置:")
    print("   - 可以随时创建组合并更新配置")
    print("   - 重启Web界面即可生效")

def main():
    """主函数"""
    print("🎯 选择操作方式:")
    print("1. 创建新的雪球模拟组合 (推荐)")
    print("2. 查看替代解决方案")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            open_xueqiu_create_portfolio()
            break
        elif choice == '2':
            show_alternative_solution()
            break
        elif choice == '3':
            print("👋 退出")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == '__main__':
    main()
