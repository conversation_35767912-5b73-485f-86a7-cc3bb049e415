// 用户相关API服务

import request from './request'
import type { 
  LoginForm, 
  RegisterForm, 
  ChangePasswordForm,
  UserInfo,
  LoginResponse,
  ApiResponse 
} from '@/types/user'

export const userApi = {
  // 用户登录
  login(data: LoginForm): Promise<ApiResponse<LoginResponse>> {
    return request.post('/auth/login', data)
  },

  // 用户注册
  register(data: RegisterForm): Promise<ApiResponse<UserInfo>> {
    return request.post('/auth/register', data)
  },

  // 获取用户信息
  getUserInfo(): Promise<ApiResponse<UserInfo>> {
    return request.get('/user/info')
  },

  // 更新用户信息
  updateUserInfo(data: Partial<UserInfo>): Promise<ApiResponse<UserInfo>> {
    return request.put('/user/info', data)
  },

  // 修改密码
  changePassword(data: ChangePasswordForm): Promise<ApiResponse<void>> {
    return request.post('/user/change-password', data)
  },

  // 用户登出
  logout(): Promise<ApiResponse<void>> {
    return request.post('/auth/logout')
  },

  // 刷新token
  refreshToken(): Promise<ApiResponse<{ token: string }>> {
    return request.post('/auth/refresh')
  },

  // 发送验证码
  sendVerificationCode(email: string): Promise<ApiResponse<void>> {
    return request.post('/auth/send-code', { email })
  },

  // 验证邮箱
  verifyEmail(code: string): Promise<ApiResponse<void>> {
    return request.post('/auth/verify-email', { code })
  },

  // 重置密码
  resetPassword(data: { email: string; code: string; new_password: string }): Promise<ApiResponse<void>> {
    return request.post('/auth/reset-password', data)
  },

  // 获取用户设置
  getUserSettings(): Promise<ApiResponse<any>> {
    return request.get('/user/settings')
  },

  // 更新用户设置
  updateUserSettings(data: any): Promise<ApiResponse<any>> {
    return request.put('/user/settings', data)
  },

  // 获取订阅信息
  getSubscription(): Promise<ApiResponse<any>> {
    return request.get('/user/subscription')
  },

  // 更新订阅
  updateSubscription(data: any): Promise<ApiResponse<any>> {
    return request.post('/user/subscription', data)
  },

  // 取消订阅
  cancelSubscription(): Promise<ApiResponse<void>> {
    return request.delete('/user/subscription')
  }
}
