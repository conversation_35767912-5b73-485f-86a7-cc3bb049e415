// 全局样式文件
@import './variables.scss';
@import './mixins.scss';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
               'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 
               'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 
               'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
}

#app {
  height: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-light);
  border-radius: 4px;
  transition: background-color 0.3s ease;
  
  &:hover {
    background: var(--el-border-color);
  }
}

// 暗色主题滚动条
.dark {
  ::-webkit-scrollbar-track {
    background: #1a1a1a;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #404040;
    
    &:hover {
      background: #606060;
    }
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

// 间距工具类
@for $i from 0 through 10 {
  .m-#{$i} {
    margin: #{$i * 4}px;
  }
  
  .mt-#{$i} {
    margin-top: #{$i * 4}px;
  }
  
  .mr-#{$i} {
    margin-right: #{$i * 4}px;
  }
  
  .mb-#{$i} {
    margin-bottom: #{$i * 4}px;
  }
  
  .ml-#{$i} {
    margin-left: #{$i * 4}px;
  }
  
  .p-#{$i} {
    padding: #{$i * 4}px;
  }
  
  .pt-#{$i} {
    padding-top: #{$i * 4}px;
  }
  
  .pr-#{$i} {
    padding-right: #{$i * 4}px;
  }
  
  .pb-#{$i} {
    padding-bottom: #{$i * 4}px;
  }
  
  .pl-#{$i} {
    padding-left: #{$i * 4}px;
  }
}

// 颜色工具类
.text-primary {
  color: var(--el-color-primary);
}

.text-success {
  color: var(--el-color-success);
}

.text-warning {
  color: var(--el-color-warning);
}

.text-danger {
  color: var(--el-color-danger);
}

.text-info {
  color: var(--el-color-info);
}

.profit-text {
  color: var(--el-color-success);
  font-weight: 500;
}

.loss-text {
  color: var(--el-color-danger);
  font-weight: 500;
}

// Element Plus 组件样式覆盖
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

.el-button {
  font-weight: 500;
  border-radius: 6px;
  
  &.is-round {
    border-radius: 20px;
  }
}

.el-input {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

.el-select {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
  
  .el-table__header {
    th {
      background-color: var(--el-fill-color-light);
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: var(--el-fill-color-lighter);
    }
  }
}

.el-dialog {
  border-radius: 8px;
  
  .el-dialog__header {
    padding: 20px 20px 10px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 10px 20px 20px;
  }
}

.el-drawer {
  .el-drawer__header {
    padding: 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .el-drawer__body {
    padding: 20px;
  }
}

.el-form {
  .el-form-item {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }
}

.el-menu {
  border-right: none;
  
  .el-menu-item,
  .el-sub-menu__title {
    height: 48px;
    line-height: 48px;
    border-radius: 6px;
    margin: 2px 8px;
    
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
    
    &.is-active {
      background-color: var(--el-color-primary-light-8);
      color: var(--el-color-primary);
      font-weight: 500;
    }
  }
  
  .el-sub-menu {
    .el-menu-item {
      margin: 2px 16px;
    }
  }
}

// 响应式断点
@media (max-width: 768px) {
  .el-card {
    .el-card__header {
      padding: 12px 16px;
    }
    
    .el-card__body {
      padding: 16px;
    }
  }
  
  .el-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  .loading-text {
    margin-top: 16px;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  .empty-icon {
    font-size: 48px;
    color: var(--el-text-color-placeholder);
    margin-bottom: 16px;
  }
  
  .empty-text {
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin-bottom: 16px;
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  
  .error-icon {
    font-size: 48px;
    color: var(--el-color-danger);
    margin-bottom: 16px;
  }
  
  .error-text {
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin-bottom: 16px;
    text-align: center;
  }
}

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }
  
  .el-card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
