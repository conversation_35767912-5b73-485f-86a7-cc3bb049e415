#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
访问用户的模拟盈亏组合
基于测试发现的"模拟"组合（symbol_count=1）
"""

import json
import logging
import requests
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserSimulationPortfolio:
    """用户模拟组合管理器"""
    
    def __init__(self, config_file='xq.json'):
        self.config_file = config_file
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ Token设置成功: u={u}")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            return False
    
    def get_simulation_portfolio_stocks(self, portfolio_id=-4):
        """获取模拟组合的股票列表"""
        try:
            logger.info(f"🔍 获取模拟组合股票列表 (ID: {portfolio_id})")
            
            # 使用pysnowball的watch_stock API
            result = ball.watch_stock(portfolio_id)
            
            if result and 'data' in result:
                data = result['data']
                stocks = data.get('stocks', [])
                
                logger.info(f"✅ 模拟组合股票信息:")
                logger.info(f"   组合ID: {data.get('pid', 'N/A')}")
                logger.info(f"   股票数量: {len(stocks)}")
                
                if stocks:
                    logger.info("   股票详情:")
                    for i, stock in enumerate(stocks, 1):
                        logger.info(f"     {i}. {stock.get('name', 'N/A')} ({stock.get('symbol', 'N/A')})")
                        logger.info(f"        当前价: {stock.get('current', 'N/A')}")
                        logger.info(f"        涨跌: {stock.get('chg', 'N/A')} ({stock.get('percent', 'N/A')}%)")
                        logger.info(f"        成交量: {stock.get('volume', 'N/A')}")
                else:
                    logger.info("   📝 模拟组合为空")
                
                return stocks
            else:
                logger.warning("⚠️ 无法获取模拟组合数据")
                return []
                
        except Exception as e:
            logger.error(f"❌ 获取模拟组合股票失败: {e}")
            return []
    
    def search_user_created_portfolios(self):
        """搜索用户创建的投资组合"""
        try:
            logger.info("🔍 搜索用户创建的投资组合")
            
            # 尝试不同的搜索方式
            search_methods = [
                # 方法1: 通过"我的组合"分类搜索
                ('我的组合分类', lambda: ball.watch_stock(-8)),
                
                # 方法2: 搜索可能的组合名称
                ('搜索测试组合', lambda: self.search_portfolio_by_name('测试')),
                ('搜索test组合', lambda: self.search_portfolio_by_name('test')),
                
                # 方法3: 尝试访问用户的组合页面
                ('用户组合页面', lambda: self.get_user_portfolio_page()),
            ]
            
            found_portfolios = []
            
            for method_name, method_func in search_methods:
                try:
                    logger.info(f"   尝试方法: {method_name}")
                    result = method_func()
                    
                    if result:
                        logger.info(f"   ✅ {method_name} 成功")
                        if isinstance(result, list):
                            found_portfolios.extend(result)
                        else:
                            found_portfolios.append(result)
                    else:
                        logger.info(f"   ⚠️ {method_name} 无结果")
                        
                except Exception as e:
                    logger.info(f"   ❌ {method_name} 失败: {e}")
            
            return found_portfolios
            
        except Exception as e:
            logger.error(f"❌ 搜索用户组合失败: {e}")
            return []
    
    def search_portfolio_by_name(self, name):
        """通过名称搜索组合"""
        try:
            # 使用雪球的搜索API
            search_url = f"https://xueqiu.com/cubes/search.json?q={name}&count=20&market=cn"
            response = self.session.get(search_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'list' in data and data['list']:
                    cubes = data['list']
                    logger.info(f"     搜索到 {len(cubes)} 个包含'{name}'的组合")
                    
                    # 查找用户自己的组合
                    user_id = self.session.cookies.get('u', '')
                    user_cubes = []
                    
                    for cube in cubes:
                        cube_user_id = str(cube.get('user_id', ''))
                        if cube_user_id == user_id:
                            user_cubes.append(cube)
                            logger.info(f"     ✅ 找到用户组合: {cube.get('name', 'N/A')} ({cube.get('symbol', 'N/A')})")
                    
                    return user_cubes
                else:
                    logger.info(f"     没有找到包含'{name}'的组合")
                    return []
            else:
                logger.info(f"     搜索请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"     搜索异常: {e}")
            return []
    
    def get_user_portfolio_page(self):
        """获取用户组合页面信息"""
        try:
            user_id = self.session.cookies.get('u', '')
            if not user_id:
                return None
            
            # 访问用户的组合页面
            user_url = f"https://xueqiu.com/u/{user_id}"
            response = self.session.get(user_url, timeout=10)
            
            if response.status_code == 200:
                page_content = response.text
                
                # 查找页面中的组合信息
                if '测试' in page_content or 'test' in page_content:
                    logger.info("     ✅ 用户页面包含'测试'或'test'相关内容")
                    
                    # 尝试提取组合信息（这里需要解析HTML，比较复杂）
                    # 简单起见，我们返回页面包含相关内容的信息
                    return {'type': 'page_content', 'contains_target': True}
                else:
                    logger.info("     ⚠️ 用户页面不包含目标组合信息")
                    return None
            else:
                logger.info(f"     用户页面访问失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"     获取用户页面异常: {e}")
            return None
    
    def try_direct_portfolio_access(self):
        """尝试直接访问可能的组合ID"""
        try:
            logger.info("🔍 尝试直接访问可能的组合ID")
            
            user_id = self.session.cookies.get('u', '')
            if not user_id:
                logger.error("❌ 无法获取用户ID")
                return []
            
            user_id_int = int(user_id)
            logger.info(f"   用户ID: {user_id_int}")
            
            # 尝试一些可能的组合ID范围
            test_ranges = [
                # 小范围ID
                range(1, 50),
                # 基于用户ID的范围
                range(max(1, user_id_int - 1000), user_id_int + 1000, 100),
            ]
            
            found_portfolios = []
            
            for test_range in test_ranges:
                logger.info(f"   测试范围: {test_range.start} - {test_range.stop}")
                
                for cube_id in test_range:
                    cube_symbol = f"ZH{cube_id:06d}"
                    
                    try:
                        # 尝试获取组合净值
                        result = ball.nav_daily(cube_symbol)
                        if result and len(result) > 0:
                            cube_data = result[0]
                            cube_name = cube_data.get('name', '')
                            
                            # 检查是否包含目标关键词
                            if any(keyword in cube_name.lower() for keyword in ['测试', 'test', '模拟']):
                                logger.info(f"   ✅ 找到可能的目标组合: {cube_name} ({cube_symbol})")
                                
                                # 获取组合详细信息
                                portfolio_info = {
                                    'symbol': cube_symbol,
                                    'name': cube_name,
                                    'id': cube_id,
                                    'nav_data': cube_data
                                }
                                
                                # 尝试获取持仓信息
                                try:
                                    holdings_result = ball.rebalancing_current(cube_symbol)
                                    if holdings_result:
                                        portfolio_info['holdings'] = holdings_result
                                        logger.info(f"     ✅ 获取到持仓信息")
                                except:
                                    logger.info(f"     ⚠️ 无法获取持仓信息")
                                
                                found_portfolios.append(portfolio_info)
                        
                        # 限制搜索数量
                        if len(found_portfolios) >= 3:
                            break
                            
                    except:
                        # 忽略错误，继续搜索
                        pass
                
                if found_portfolios:
                    break
            
            return found_portfolios
            
        except Exception as e:
            logger.error(f"❌ 直接访问组合失败: {e}")
            return []
    
    def refresh_user_portfolios(self):
        """刷新用户的投资组合"""
        logger.info("🚀 刷新用户投资组合")
        logger.info("=" * 60)
        
        # 1. 获取模拟组合股票
        logger.info("📊 1. 获取模拟组合股票")
        simulation_stocks = self.get_simulation_portfolio_stocks()
        
        # 2. 搜索用户创建的组合
        logger.info("\n🔍 2. 搜索用户创建的组合")
        user_portfolios = self.search_user_created_portfolios()
        
        # 3. 尝试直接访问组合
        logger.info("\n🎯 3. 尝试直接访问可能的组合")
        direct_portfolios = self.try_direct_portfolio_access()
        
        # 4. 汇总结果
        logger.info("\n📋 4. 汇总结果")
        logger.info("=" * 50)
        
        all_portfolios = []
        
        if simulation_stocks:
            logger.info(f"✅ 模拟组合: 找到 {len(simulation_stocks)} 只股票")
            all_portfolios.append({
                'type': 'simulation',
                'name': '模拟组合',
                'stocks': simulation_stocks
            })
        
        if user_portfolios:
            logger.info(f"✅ 用户组合: 找到 {len(user_portfolios)} 个组合")
            all_portfolios.extend(user_portfolios)
        
        if direct_portfolios:
            logger.info(f"✅ 直接访问: 找到 {len(direct_portfolios)} 个组合")
            all_portfolios.extend(direct_portfolios)
        
        if all_portfolios:
            logger.info(f"\n🎉 总共找到 {len(all_portfolios)} 个投资组合")
            
            for i, portfolio in enumerate(all_portfolios, 1):
                logger.info(f"\n📊 组合 {i}: {portfolio.get('name', 'N/A')}")
                
                if portfolio.get('type') == 'simulation':
                    stocks = portfolio.get('stocks', [])
                    logger.info(f"   类型: 模拟组合")
                    logger.info(f"   股票数: {len(stocks)}")
                    for stock in stocks:
                        logger.info(f"     - {stock.get('name', 'N/A')} ({stock.get('symbol', 'N/A')})")
                
                elif 'symbol' in portfolio:
                    logger.info(f"   类型: 投资组合")
                    logger.info(f"   代码: {portfolio.get('symbol', 'N/A')}")
                    
                    if 'holdings' in portfolio:
                        holdings = portfolio['holdings'].get('last_rb', {}).get('holdings', [])
                        logger.info(f"   持仓数: {len(holdings)}")
                        for holding in holdings:
                            logger.info(f"     - {holding.get('stock_name', 'N/A')} "
                                      f"({holding.get('stock_symbol', 'N/A')}): "
                                      f"{holding.get('weight', 0):.1f}%")
        else:
            logger.warning("⚠️ 没有找到任何投资组合")
            logger.info("💡 可能的原因:")
            logger.info("   1. 组合名称不是'测试'或'test'")
            logger.info("   2. 组合为私有状态")
            logger.info("   3. 组合ID超出搜索范围")
        
        return all_portfolios

def main():
    """主函数"""
    print("🚀 用户模拟盈亏组合访问器")
    print("连接并刷新用户的'测试'和'test'组合")
    print("=" * 60)
    
    manager = UserSimulationPortfolio()
    
    # 刷新用户投资组合
    portfolios = manager.refresh_user_portfolios()
    
    print(f"\n🎯 刷新完成！")
    if portfolios:
        print(f"✅ 成功访问 {len(portfolios)} 个投资组合")
    else:
        print("⚠️ 没有找到目标组合，请检查组合名称或权限设置")

if __name__ == '__main__':
    main()
