#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
帮助找到正确的雪球组合代码
通过多种方法查找用户的真实组合
"""

import json
import logging
import requests
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PortfolioCodeFinder:
    """组合代码查找器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/20100101 Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ Token设置成功: u={u}")
                self.user_id = u
            
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
    
    def search_user_cubes_directly(self):
        """直接搜索用户的组合"""
        logger.info("🔍 直接搜索用户组合")
        logger.info("=" * 60)
        
        # 尝试不同的用户组合API
        user_cube_apis = [
            f"https://xueqiu.com/cubes/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/cubes/list.json?user_id={self.user_id}&type=all",
            f"https://xueqiu.com/cubes/list.json?user_id={self.user_id}&category=all",
            f"https://xueqiu.com/v4/cubes/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/u/{self.user_id}/cubes.json",
            f"https://xueqiu.com/user/{self.user_id}/cubes.json",
        ]
        
        found_cubes = []
        
        for api_url in user_cube_apis:
            try:
                logger.info(f"\n🔗 测试: {api_url}")
                response = self.session.get(api_url, timeout=10)
                
                logger.info(f"   状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   ✅ 成功获取JSON数据")
                        
                        # 查找组合列表
                        cubes = []
                        if isinstance(data, dict):
                            if 'list' in data:
                                cubes = data['list']
                            elif 'data' in data:
                                cubes = data['data']
                            elif 'cubes' in data:
                                cubes = data['cubes']
                        elif isinstance(data, list):
                            cubes = data
                        
                        if cubes:
                            logger.info(f"   📊 找到 {len(cubes)} 个组合")
                            for cube in cubes:
                                cube_symbol = cube.get('symbol', '')
                                cube_name = cube.get('name', '')
                                cube_id = cube.get('id', '')
                                
                                logger.info(f"     • {cube_name} ({cube_symbol}) ID: {cube_id}")
                                
                                # 检查是否包含"test"或"测试"
                                if any(keyword in cube_name.lower() for keyword in ['test', '测试']):
                                    logger.info(f"       🎯 可能是目标组合！")
                                    found_cubes.append(cube)
                        else:
                            logger.info(f"   ⚠️ 未找到组合列表")
                            
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON响应")
                        
                else:
                    logger.info(f"   ❌ 失败")
                    
            except Exception as e:
                logger.info(f"   ❌ 异常: {e}")
        
        return found_cubes
    
    def check_performance_apis(self):
        """检查performance相关API"""
        logger.info("📊 检查performance相关API")
        logger.info("=" * 60)
        
        # performance相关API
        performance_apis = [
            "https://xueqiu.com/performance/list.json",
            f"https://xueqiu.com/performance/list.json?user_id={self.user_id}",
            "https://xueqiu.com/performance/portfolio/list.json",
            f"https://xueqiu.com/performance/portfolio/list.json?user_id={self.user_id}",
            "https://xueqiu.com/moni/list.json",
            f"https://xueqiu.com/moni/list.json?user_id={self.user_id}",
            "https://xueqiu.com/moni/portfolio/list.json",
            f"https://xueqiu.com/moni/portfolio/list.json?user_id={self.user_id}",
        ]
        
        found_portfolios = []
        
        for api_url in performance_apis:
            try:
                logger.info(f"\n🔗 测试: {api_url}")
                response = self.session.get(api_url, timeout=10)
                
                logger.info(f"   状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   ✅ 成功获取JSON数据")
                        
                        # 查找组合信息
                        data_str = json.dumps(data, ensure_ascii=False)
                        if any(keyword in data_str.lower() for keyword in ['test', '测试', 'portfolio', 'moni']):
                            logger.info(f"   🎯 包含相关关键词")
                            found_portfolios.append({
                                'api': api_url,
                                'data': data
                            })
                        
                        # 显示数据结构
                        if isinstance(data, dict):
                            keys = list(data.keys())
                            logger.info(f"   数据字段: {keys}")
                        
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON响应")
                        
                else:
                    logger.info(f"   ❌ 失败")
                    
            except Exception as e:
                logger.info(f"   ❌ 异常: {e}")
        
        return found_portfolios
    
    def try_create_test_portfolio(self):
        """尝试创建test组合"""
        logger.info("➕ 尝试创建test组合")
        logger.info("=" * 60)
        
        # 创建组合的可能API
        create_apis = [
            "https://xueqiu.com/cubes/create.json",
            "https://xueqiu.com/performance/create.json",
            "https://xueqiu.com/moni/create.json",
            "https://xueqiu.com/portfolio/create.json",
        ]
        
        # 创建数据
        create_data = {
            "name": "test",
            "description": "测试组合",
            "market": "cn",
            "initial_cash": 1000000
        }
        
        create_results = []
        
        for api_url in create_apis:
            try:
                logger.info(f"\n🔗 尝试创建: {api_url}")
                logger.info(f"   数据: {create_data}")
                
                response = self.session.post(api_url, json=create_data, timeout=10)
                logger.info(f"   状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        logger.info(f"   ✅ 创建成功: {result}")
                        create_results.append({
                            'api': api_url,
                            'result': result
                        })
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON响应: {response.text[:200]}")
                else:
                    logger.info(f"   ❌ 创建失败: {response.status_code}")
                    if response.text:
                        logger.info(f"   错误: {response.text[:200]}")
                        
            except Exception as e:
                logger.info(f"   ❌ 异常: {e}")
        
        return create_results
    
    def analyze_browser_network(self):
        """分析浏览器网络请求"""
        logger.info("🌐 分析浏览器网络请求")
        logger.info("=" * 60)
        
        logger.info("💡 建议手动操作:")
        logger.info("1. 打开浏览器开发者工具 (F12)")
        logger.info("2. 切换到 Network 标签")
        logger.info("3. 访问 https://xueqiu.com/performance")
        logger.info("4. 点击'管理组合'或'添加组合'按钮")
        logger.info("5. 查看Network中的API请求")
        logger.info("6. 找到包含组合信息的API调用")
        
        # 尝试模拟一些可能的网络请求
        possible_requests = [
            "https://xueqiu.com/performance/manage",
            "https://xueqiu.com/performance/add",
            "https://xueqiu.com/performance/portfolio/manage",
            "https://xueqiu.com/performance/portfolio/add",
        ]
        
        for url in possible_requests:
            try:
                logger.info(f"\n🔗 尝试访问: {url}")
                response = self.session.get(url, timeout=5)
                logger.info(f"   状态: {response.status_code}")
                
                if response.status_code == 200:
                    logger.info(f"   ✅ 页面存在")
                    # 可以进一步分析页面内容
                else:
                    logger.info(f"   ❌ 页面不存在")
                    
            except Exception as e:
                logger.info(f"   ❌ 访问失败: {e}")

def main():
    """主函数"""
    print("🎯 查找正确的雪球组合代码")
    print("帮助找到您的'test'组合的真实代码")
    print("=" * 80)
    
    finder = PortfolioCodeFinder()
    
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'user_id': getattr(finder, 'user_id', None),
        'user_cubes': [],
        'performance_portfolios': [],
        'create_results': []
    }
    
    # 1. 直接搜索用户组合
    logger.info("\n🔍 1. 直接搜索用户组合")
    user_cubes = finder.search_user_cubes_directly()
    all_results['user_cubes'] = user_cubes
    
    # 2. 检查performance API
    logger.info("\n📊 2. 检查performance相关API")
    performance_portfolios = finder.check_performance_apis()
    all_results['performance_portfolios'] = performance_portfolios
    
    # 3. 尝试创建test组合
    logger.info("\n➕ 3. 尝试创建test组合")
    create_results = finder.try_create_test_portfolio()
    all_results['create_results'] = create_results
    
    # 4. 分析浏览器网络请求
    logger.info("\n🌐 4. 分析浏览器网络请求")
    finder.analyze_browser_network()
    
    # 5. 汇总结果
    logger.info("\n📈 5. 结果汇总")
    logger.info("=" * 60)
    
    if user_cubes:
        logger.info(f"✅ 用户组合: 找到 {len(user_cubes)} 个组合")
        for cube in user_cubes:
            logger.info(f"   🎯 {cube.get('name', 'N/A')} ({cube.get('symbol', 'N/A')})")
    else:
        logger.warning("⚠️ 用户组合: 未找到任何组合")
    
    if performance_portfolios:
        logger.info(f"✅ Performance组合: 找到 {len(performance_portfolios)} 个相关API")
        for portfolio in performance_portfolios:
            logger.info(f"   📊 {portfolio['api']}")
    else:
        logger.warning("⚠️ Performance组合: 未找到相关API")
    
    if create_results:
        logger.info(f"✅ 创建组合: 找到 {len(create_results)} 个成功的API")
        for result in create_results:
            logger.info(f"   ➕ {result['api']}: {result['result']}")
    else:
        logger.warning("⚠️ 创建组合: 所有尝试都失败")
    
    # 6. 保存结果
    try:
        with open('portfolio_code_search_results.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        logger.info(f"\n💾 完整结果已保存到: portfolio_code_search_results.json")
    except Exception as e:
        logger.error(f"\n❌ 保存结果失败: {e}")
    
    print(f"\n🎉 组合代码查找完成！")
    
    # 给出建议
    if user_cubes:
        print(f"🎯 建议: 使用找到的组合代码更新xq.json配置")
        for cube in user_cubes:
            print(f"   组合: {cube.get('name', 'N/A')} -> 代码: {cube.get('symbol', 'N/A')}")
    elif create_results:
        print(f"🎯 建议: 使用创建的新组合")
    else:
        print(f"💡 建议:")
        print(f"   1. 手动在雪球网页上创建名为'test'的组合")
        print(f"   2. 记录创建后的组合代码")
        print(f"   3. 更新xq.json中的portfolio_code")

if __name__ == '__main__':
    main()
