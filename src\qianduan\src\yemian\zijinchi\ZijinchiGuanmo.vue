<template>
  <div class="fund-pool-container">
    <!-- 资金池概览 -->
    <div class="fund-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon total">
                <el-icon><Money /></el-icon>
              </div>
              <div class="overview-content">
                <div class="overview-value">{{ formatMoney(stats.totalAmount) }}</div>
                <div class="overview-label">总资金规模</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon profit">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="overview-content">
                <div class="overview-value profit-value">{{ formatMoney(stats.totalProfit) }}</div>
                <div class="overview-label">累计收益</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon rate">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="overview-content">
                <div class="overview-value rate-value">{{ stats.totalProfitRate }}%</div>
                <div class="overview-label">总收益率</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon daily">
                <el-icon><Sunny /></el-icon>
              </div>
              <div class="overview-content">
                <div class="overview-value daily-value">{{ formatMoney(stats.dailyProfit) }}</div>
                <div class="overview-label">今日收益</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 左侧：收益图表 -->
        <el-col :span="16">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">收益趋势</span>
                <div class="chart-controls">
                  <el-radio-group v-model="chartPeriod" size="small">
                    <el-radio-button label="7">7天</el-radio-button>
                    <el-radio-button label="30">30天</el-radio-button>
                    <el-radio-button label="90">90天</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>
            
            <div class="chart-container">
              <v-chart 
                :option="profitChartOption" 
                :loading="chartLoading"
                class="profit-chart"
                autoresize
              />
            </div>
          </el-card>
        </el-col>
        
        <!-- 右侧：资金池列表 -->
        <el-col :span="8">
          <el-card class="pool-list-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">资金池列表</span>
              </div>
            </template>
            
            <div class="pool-list">
              <div 
                v-for="pool in fundPools" 
                :key="pool.id"
                class="pool-item"
                :class="{ active: selectedPoolId === pool.id }"
                @click="selectPool(pool.id)"
              >
                <div class="pool-info">
                  <div class="pool-name">{{ pool.name }}</div>
                  <div class="pool-amount">{{ formatMoney(pool.total_amount) }}</div>
                </div>
                <div class="pool-profit">
                  <div class="profit-amount" :class="{ positive: pool.total_profit > 0 }">
                    {{ formatMoney(pool.total_profit) }}
                  </div>
                  <div class="profit-rate" :class="{ positive: pool.total_profit_rate > 0 }">
                    {{ pool.total_profit_rate }}%
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 交易记录 -->
    <div class="trade-records">
      <el-card>
        <template #header>
          <div class="card-header">
            <span class="card-title">最新交易记录</span>
            <div class="trade-filters">
              <el-select v-model="tradeFilter.type" placeholder="交易类型" size="small" style="width: 100px">
                <el-option label="全部" value="" />
                <el-option label="买入" value="buy" />
                <el-option label="卖出" value="sell" />
              </el-select>
              <el-input
                v-model="tradeFilter.stockCode"
                placeholder="股票代码"
                size="small"
                style="width: 120px"
                clearable
              />
            </div>
          </div>
        </template>
        
        <el-table :data="tradeRecords" v-loading="tradeLoading" style="width: 100%">
          <el-table-column prop="trade_time" label="交易时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.trade_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="stock_code" label="股票代码" width="100" />
          <el-table-column prop="stock_name" label="股票名称" width="120" />
          <el-table-column prop="trade_type" label="交易类型" width="80">
            <template #default="{ row }">
              <el-tag :type="row.trade_type === 'buy' ? 'success' : 'danger'" size="small">
                {{ row.trade_type === 'buy' ? '买入' : '卖出' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="价格" width="100">
            <template #default="{ row }">
              ¥{{ row.price.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" width="100">
            <template #default="{ row }">
              {{ row.quantity.toLocaleString() }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额" width="120">
            <template #default="{ row }">
              {{ formatMoney(row.amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="profit" label="收益" width="120">
            <template #default="{ row }">
              <span v-if="row.trade_type === 'sell'" :class="{ 'profit-positive': row.profit > 0, 'profit-negative': row.profit < 0 }">
                {{ formatMoney(row.profit) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="strategy_name" label="策略" />
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50]"
            :total="totalRecords"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="loadTradeRecords"
            @size-change="loadTradeRecords"
          />
        </div>
      </el-card>
    </div>

    <!-- 升级提示 -->
    <div class="upgrade-banner">
      <el-alert
        title="体验更多功能"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>您当前是注册用户版，只能观摩资金池交易情况。</p>
          <p>升级到基础版或高级版，即可创建自己的量化策略并进行实盘交易！</p>
          <div style="margin-top: 10px;">
            <el-button type="primary" size="small" @click="showUpgradeDialog">
              立即升级
            </el-button>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { use } from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { fundPoolService } from '@/fuwu/fundPoolService'

// 注册ECharts组件
use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

// 响应式数据
const chartLoading = ref(false)
const tradeLoading = ref(false)
const chartPeriod = ref('30')
const selectedPoolId = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalRecords = ref(0)

// 统计数据
const stats = ref({
  totalAmount: 0,
  totalProfit: 0,
  totalProfitRate: 0,
  dailyProfit: 0,
  dailyProfitRate: 0
})

// 资金池列表
const fundPools = ref<any[]>([])

// 交易记录
const tradeRecords = ref<any[]>([])

// 收益历史数据
const profitHistory = ref<any[]>([])

// 交易筛选条件
const tradeFilter = ref({
  type: '',
  stockCode: ''
})

// 工具函数
const formatMoney = (amount: number) => {
  if (amount >= 100000000) {
    return `${(amount / 100000000).toFixed(1)}亿`
  } else if (amount >= 10000) {
    return `${(amount / 10000).toFixed(1)}万`
  } else {
    return `¥${amount.toLocaleString()}`
  }
}

const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('MM-DD HH:mm')
}

// 收益图表配置
const profitChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const data = params[0]
      return `${data.name}<br/>累计收益: ${formatMoney(data.value)}`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: profitHistory.value.map(item => dayjs(item.date).format('MM-DD')),
    axisLine: {
      lineStyle: {
        color: '#e4e7ed'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => formatMoney(value)
    },
    axisLine: {
      lineStyle: {
        color: '#e4e7ed'
      }
    },
    splitLine: {
      lineStyle: {
        color: '#f5f7fa'
      }
    }
  },
  series: [
    {
      name: '累计收益',
      type: 'line',
      data: profitHistory.value.map(item => item.cumulative_profit),
      smooth: true,
      lineStyle: {
        color: '#67c23a',
        width: 3
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.05)' }
          ]
        }
      }
    }
  ]
}))

// 方法
const loadStats = async () => {
  try {
    const data = await fundPoolService.getFundPoolStats()
    stats.value = data
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadFundPools = async () => {
  try {
    const pools = await fundPoolService.getFundPools()
    fundPools.value = pools
    if (pools.length > 0 && !selectedPoolId.value) {
      selectedPoolId.value = pools[0].id
    }
  } catch (error) {
    console.error('加载资金池数据失败:', error)
  }
}

const loadProfitHistory = async () => {
  try {
    chartLoading.value = true
    const days = parseInt(chartPeriod.value)
    const history = await fundPoolService.getProfitHistory(days)
    profitHistory.value = history
  } catch (error) {
    console.error('加载收益历史失败:', error)
  } finally {
    chartLoading.value = false
  }
}

const loadTradeRecords = async () => {
  try {
    tradeLoading.value = true
    const result = await fundPoolService.getTradeRecords({
      fundPoolId: selectedPoolId.value,
      tradeType: tradeFilter.value.type || undefined,
      stockCode: tradeFilter.value.stockCode || undefined,
      page: currentPage.value,
      pageSize: pageSize.value
    })
    tradeRecords.value = result.records
    totalRecords.value = result.total
  } catch (error) {
    console.error('加载交易记录失败:', error)
  } finally {
    tradeLoading.value = false
  }
}

const selectPool = (poolId: string) => {
  selectedPoolId.value = poolId
  currentPage.value = 1
  loadTradeRecords()
}

const showUpgradeDialog = () => {
  ElMessage.info('升级功能开发中，敬请期待！')
}

// 监听器
watch(chartPeriod, () => {
  loadProfitHistory()
})

watch([() => tradeFilter.value.type, () => tradeFilter.value.stockCode], () => {
  currentPage.value = 1
  loadTradeRecords()
})

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadStats(),
    loadFundPools(),
    loadProfitHistory()
  ])
  loadTradeRecords()
})
</script>

<style lang="scss" scoped>
.fund-pool-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.fund-overview {
  margin-bottom: 20px;

  .overview-card {
    height: 120px;

    .overview-item {
      display: flex;
      align-items: center;
      height: 100%;

      .overview-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-right: 16px;

        &.total {
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }

        &.profit {
          background: linear-gradient(135deg, #10b981, #059669);
        }

        &.rate {
          background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        &.daily {
          background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }
      }

      .overview-content {
        flex: 1;

        .overview-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;

          &.profit-value {
            color: #10b981;
          }

          &.rate-value {
            color: #f59e0b;
          }

          &.daily-value {
            color: #8b5cf6;
          }
        }

        .overview-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

.main-content {
  margin-bottom: 20px;

  .chart-card {
    height: 400px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    .chart-container {
      height: 320px;

      .profit-chart {
        width: 100%;
        height: 100%;
      }
    }
  }

  .pool-list-card {
    height: 400px;

    .pool-list {
      max-height: 320px;
      overflow-y: auto;

      .pool-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 8px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: var(--el-color-primary);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }

        &.active {
          border-color: var(--el-color-primary);
          background: rgba(64, 158, 255, 0.05);
        }

        .pool-info {
          .pool-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }

          .pool-amount {
            font-size: 12px;
            color: var(--el-text-color-regular);
          }
        }

        .pool-profit {
          text-align: right;

          .profit-amount {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;

            &.positive {
              color: #10b981;
            }
          }

          .profit-rate {
            font-size: 12px;

            &.positive {
              color: #10b981;
            }
          }
        }
      }
    }
  }
}

.trade-records {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 16px;
      font-weight: 600;
    }

    .trade-filters {
      display: flex;
      gap: 10px;
    }
  }

  .profit-positive {
    color: #10b981;
  }

  .profit-negative {
    color: #f56c6c;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

.upgrade-banner {
  .el-alert {
    border-radius: 8px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    .el-row {
      flex-direction: column;
    }

    .el-col {
      width: 100% !important;
      margin-bottom: 20px;
    }
  }
}

@media (max-width: 768px) {
  .fund-overview {
    .el-row {
      flex-direction: column;
    }

    .el-col {
      width: 100% !important;
      margin-bottom: 10px;
    }
  }

  .trade-filters {
    flex-direction: column;
    gap: 5px !important;
  }
}
</style>
