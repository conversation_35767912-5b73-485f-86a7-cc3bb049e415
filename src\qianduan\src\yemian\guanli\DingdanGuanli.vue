<template>
  <div class="order-management-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">订单管理</span>
          <div class="header-actions">
            <el-button type="success" @click="handleExportOrders" :loading="exporting">
              <el-icon><Download /></el-icon>
              导出订单
            </el-button>
            <el-button type="primary" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="order-management-content">
        <!-- 订单统计 -->
        <div class="order-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ orderStats.totalOrders }}</div>
                <div class="stat-label">总订单数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value today">{{ orderStats.todayOrders }}</div>
                <div class="stat-label">今日订单</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value pending">{{ orderStats.pendingOrders }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value revenue">¥{{ formatMoney(orderStats.monthRevenue) }}</div>
                <div class="stat-label">本月收入</div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 搜索和筛选 -->
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索订单号、用户名或商品名称"
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select v-model="orderTypeFilter" placeholder="订单类型" style="width: 140px">
            <el-option label="全部类型" value="" />
            <el-option label="充值订单" value="recharge" />
            <el-option label="订阅订单" value="subscription" />
            <el-option label="策略购买" value="strategy" />
            <el-option label="提现订单" value="withdraw" />
            <el-option label="退款订单" value="refund" />
          </el-select>
          
          <el-select v-model="statusFilter" placeholder="订单状态" style="width: 120px">
            <el-option label="全部状态" value="" />
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="已退款" value="refunded" />
            <el-option label="支付失败" value="failed" />
          </el-select>
          
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </div>
        
        <!-- 订单表格 -->
        <el-table :data="orders" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="订单号" width="180">
            <template #default="{ row }">
              <el-link type="primary" @click="handleViewOrder(row)">
                {{ row.id }}
              </el-link>
            </template>
          </el-table-column>
          
          <el-table-column prop="user_name" label="用户" width="120" />
          
          <el-table-column prop="order_type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getOrderTypeTagType(row.order_type)" size="small">
                {{ getOrderTypeText(row.order_type) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="title" label="商品/服务" width="200" show-overflow-tooltip />
          
          <el-table-column prop="amount" label="金额" width="100">
            <template #default="{ row }">
              <span class="amount-text">¥{{ row.amount.toFixed(2) }}</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="payment_method" label="支付方式" width="100">
            <template #default="{ row }">
              {{ getPaymentMethodText(row.payment_method) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="create_time" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.create_time) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button type="info" size="small" @click="handleViewOrder(row)">
                详情
              </el-button>
              <el-button 
                v-if="row.status === 'pending'"
                type="success" 
                size="small" 
                @click="handleConfirmPayment(row)"
              >
                确认支付
              </el-button>
              <el-button 
                v-if="row.status === 'paid' && ['subscription', 'strategy'].includes(row.order_type)"
                type="warning" 
                size="small" 
                @click="handleRefund(row)"
              >
                退款
              </el-button>
              <el-button 
                v-if="row.status === 'pending'"
                type="danger" 
                size="small" 
                @click="handleCancelOrder(row)"
              >
                取消
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalOrders"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="loadOrders"
            @size-change="loadOrders"
          />
        </div>
      </div>
    </el-card>
    
    <!-- 订单详情弹窗 -->
    <DingdanXiangqing
      v-model="showOrderDetail"
      :order-id="selectedOrderId"
      @refresh="loadOrders"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { orderService } from '@/fuwu/orderService'
import DingdanXiangqing from './zujian/DingdanXiangqing.vue'

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const searchKeyword = ref('')
const orderTypeFilter = ref('')
const statusFilter = ref('')
const dateRange = ref<[string, string] | null>(null)
const currentPage = ref(1)
const pageSize = ref(20)
const totalOrders = ref(0)

// 弹窗控制
const showOrderDetail = ref(false)
const selectedOrderId = ref('')

// 订单数据
const orders = ref<any[]>([])
const orderStats = ref({
  totalOrders: 0,
  todayOrders: 0,
  pendingOrders: 0,
  monthRevenue: 0
})

// 工具函数
const formatMoney = (amount: number) => {
  return amount.toLocaleString()
}

const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm')
}

const getOrderTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    recharge: '充值',
    subscription: '订阅',
    strategy: '策略',
    withdraw: '提现',
    refund: '退款'
  }
  return typeMap[type] || '未知'
}

const getOrderTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    recharge: 'success',
    subscription: 'primary',
    strategy: 'warning',
    withdraw: 'info',
    refund: 'danger'
  }
  return typeMap[type] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    cancelled: '已取消',
    refunded: '已退款',
    failed: '支付失败'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    paid: 'success',
    cancelled: 'info',
    refunded: 'danger',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

const getPaymentMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    alipay: '支付宝',
    wechat: '微信支付',
    bank: '银行卡',
    balance: '余额支付',
    combo: '组合支付'
  }
  return methodMap[method] || '未知'
}

// 方法
const loadOrders = async () => {
  try {
    loading.value = true
    
    const options: any = {
      page: currentPage.value,
      pageSize: pageSize.value
    }
    
    if (orderTypeFilter.value) options.orderType = orderTypeFilter.value
    if (statusFilter.value) options.status = statusFilter.value
    if (searchKeyword.value) options.keyword = searchKeyword.value
    if (dateRange.value) {
      options.startDate = dateRange.value[0]
      options.endDate = dateRange.value[1]
    }
    
    const result = await orderService.getOrders(options)
    orders.value = result.orders
    totalOrders.value = result.total
  } catch (error) {
    console.error('加载订单数据失败:', error)
    ElMessage.error('加载订单数据失败')
  } finally {
    loading.value = false
  }
}

const loadOrderStats = async () => {
  try {
    const stats = await orderService.getOrderStats()
    orderStats.value = stats
  } catch (error) {
    console.error('加载订单统计失败:', error)
  }
}

const handleViewOrder = (order: any) => {
  selectedOrderId.value = order.id
  showOrderDetail.value = true
}

const handleConfirmPayment = async (order: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要将订单 ${order.id} 标记为已支付吗？`,
      '确认支付',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    const success = await orderService.updateOrderStatus(order.id, 'paid', '管理员手动确认支付')
    
    if (success) {
      ElMessage.success('订单状态更新成功')
      loadOrders()
      loadOrderStats()
    } else {
      ElMessage.error('订单状态更新失败')
    }
  } catch {
    // 用户取消操作
  }
}

const handleRefund = async (order: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要退款订单 ${order.id} 吗？退款金额：¥${order.amount}`,
      '确认退款',
      {
        confirmButtonText: '确定退款',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await orderService.updateOrderStatus(order.id, 'refunded', '管理员手动退款')
    
    if (success) {
      ElMessage.success('退款处理成功')
      loadOrders()
      loadOrderStats()
    } else {
      ElMessage.error('退款处理失败')
    }
  } catch {
    // 用户取消操作
  }
}

const handleCancelOrder = async (order: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消订单 ${order.id} 吗？`,
      '取消订单',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '返回',
        type: 'warning'
      }
    )
    
    const success = await orderService.updateOrderStatus(order.id, 'cancelled', '管理员手动取消')
    
    if (success) {
      ElMessage.success('订单已取消')
      loadOrders()
      loadOrderStats()
    } else {
      ElMessage.error('订单取消失败')
    }
  } catch {
    // 用户取消操作
  }
}

const handleExportOrders = async () => {
  try {
    exporting.value = true
    
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('订单数据导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const handleRefresh = () => {
  loadOrders()
  loadOrderStats()
}

// 监听器
watch([searchKeyword, orderTypeFilter, statusFilter, dateRange], () => {
  currentPage.value = 1
  loadOrders()
}, { deep: true })

// 生命周期
onMounted(() => {
  loadOrders()
  loadOrderStats()
})
</script>

<style lang="scss" scoped>
.order-management-container {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .card-title {
    font-size: 18px;
    font-weight: 600;
  }

  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.order-management-content {
  .order-stats {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .stat-item {
      text-align: center;

      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;

        &.today {
          color: #409eff;
        }

        &.pending {
          color: #e6a23c;
        }

        &.revenue {
          color: #67c23a;
        }
      }

      .stat-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }

  .search-bar {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
  }

  .amount-text {
    font-weight: 600;
    color: #67c23a;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    align-items: stretch;

    .el-input,
    .el-select,
    .el-date-picker {
      width: 100% !important;
    }
  }

  .order-stats {
    .el-row {
      flex-direction: column;
    }

    .el-col {
      width: 100% !important;
      margin-bottom: 10px;
    }
  }
}
</style>
