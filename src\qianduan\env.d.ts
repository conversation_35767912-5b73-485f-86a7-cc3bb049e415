/// <reference types="vite/client" />

// 环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_PORT: string
  readonly VITE_USE_MOCK: string
  readonly VITE_USE_PROXY: string
  readonly VITE_PROXY_TARGET: string
  readonly VITE_USE_HTTPS: string
  readonly VITE_USE_PWA: string
  readonly VITE_USE_CDN: string
  readonly VITE_CDN_URL: string
  readonly VITE_APP_VERSION: string
  readonly VITE_BUILD_TIME: string
  readonly VITE_AUTHOR: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Vue组件类型声明
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 图片资源类型声明
declare module '*.svg'
declare module '*.png'
declare module '*.jpg'
declare module '*.jpeg'
declare module '*.gif'
declare module '*.bmp'
declare module '*.tiff'
declare module '*.webp'
declare module '*.ico'

// CSS模块类型声明
declare module '*.module.css' {
  const classes: { readonly [key: string]: string }
  export default classes
}

declare module '*.module.scss' {
  const classes: { readonly [key: string]: string }
  export default classes
}

declare module '*.module.sass' {
  const classes: { readonly [key: string]: string }
  export default classes
}

// JSON文件类型声明
declare module '*.json' {
  const value: any
  export default value
}

// 全局类型扩展
declare global {
  interface Window {
    // 可以在这里添加全局window对象的扩展
  }
}
