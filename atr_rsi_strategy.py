#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ATR RSI策略
结合ATR(平均真实波幅)和RSI(相对强弱指数)的趋势跟踪策略
"""

from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)
import numpy as np


class AtrRsiStrategy(CtaTemplate):
    """ATR RSI策略"""
    
    author = "VeighNa量化"
    
    # 策略参数
    atr_length = 14         # ATR计算周期
    atr_ma_length = 30      # ATR均线周期
    rsi_length = 14         # RSI计算周期
    rsi_entry = 16          # RSI入场阈值
    trailing_percent = 0.8  # 移动止损百分比
    fixed_size = 1          # 固定交易手数
    
    # 策略变量
    atr_value = 0.0         # ATR值
    atr_ma = 0.0           # ATR均线
    rsi_value = 0.0        # RSI值
    rsi_buy = 0.0          # RSI买入信号
    rsi_sell = 0.0         # RSI卖出信号
    intra_trade_high = 0.0 # 持仓期间最高价
    intra_trade_low = 0.0  # 持仓期间最低价
    
    parameters = [
        "atr_length", "atr_ma_length", "rsi_length", 
        "rsi_entry", "trailing_percent", "fixed_size"
    ]
    variables = [
        "atr_value", "atr_ma", "rsi_value", "rsi_buy", "rsi_sell",
        "intra_trade_high", "intra_trade_low"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化策略"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        self.rsi_buy = 50 + self.rsi_entry
        self.rsi_sell = 50 - self.rsi_entry
        
        self.bg = BarGenerator(self.on_bar, 15, self.on_15min_bar)
        self.am = ArrayManager()
    
    def on_init(self):
        """策略初始化"""
        self.write_log("ATR RSI策略初始化")
        self.load_bar(10)
    
    def on_start(self):
        """策略启动"""
        self.write_log("ATR RSI策略启动")
    
    def on_stop(self):
        """策略停止"""
        self.write_log("ATR RSI策略停止")
    
    def on_tick(self, tick: TickData):
        """收到行情TICK推送"""
        self.bg.update_tick(tick)
    
    def on_bar(self, bar: BarData):
        """收到Bar推送"""
        self.bg.update_bar(bar)
    
    def on_15min_bar(self, bar: BarData):
        """收到15分钟Bar推送"""
        self.cancel_all()
        
        self.am.update_bar(bar)
        if not self.am.inited:
            return
        
        # 计算技术指标
        atr_array = self.am.atr(self.atr_length, array=True)
        self.atr_value = atr_array[-1]
        self.atr_ma = atr_array[-self.atr_ma_length:].mean()
        
        self.rsi_value = self.am.rsi(self.rsi_length)
        
        # 判断趋势条件
        if self.atr_value > self.atr_ma:
            # 趋势市场
            if self.pos == 0:
                self.intra_trade_high = bar.high_price
                self.intra_trade_low = bar.low_price
                
                # 多头信号：RSI超买后回落
                if self.rsi_value > self.rsi_buy:
                    self.buy(bar.close_price + 5, self.fixed_size)
                
                # 空头信号：RSI超卖后反弹
                elif self.rsi_value < self.rsi_sell:
                    self.short(bar.close_price - 5, self.fixed_size)
            
            elif self.pos > 0:
                # 多头持仓
                self.intra_trade_high = max(self.intra_trade_high, bar.high_price)
                
                # 移动止损
                long_stop = self.intra_trade_high * (1 - self.trailing_percent / 100)
                self.sell(long_stop, abs(self.pos), stop=True)
            
            elif self.pos < 0:
                # 空头持仓
                self.intra_trade_low = min(self.intra_trade_low, bar.low_price)
                
                # 移动止损
                short_stop = self.intra_trade_low * (1 + self.trailing_percent / 100)
                self.cover(short_stop, abs(self.pos), stop=True)
        
        else:
            # 震荡市场，平仓
            if self.pos > 0:
                self.sell(bar.close_price - 5, abs(self.pos))
            elif self.pos < 0:
                self.cover(bar.close_price + 5, abs(self.pos))
        
        self.put_event()
    
    def on_order(self, order: OrderData):
        """收到委托变化推送"""
        pass
    
    def on_trade(self, trade: TradeData):
        """收到成交推送"""
        if trade.direction.value == "多":
            self.write_log(f"买入成交: {trade.volume}@{trade.price}")
        else:
            self.write_log(f"卖出成交: {trade.volume}@{trade.price}")
        
        self.put_event()
    
    def on_stop_order(self, stop_order: StopOrder):
        """收到停止单推送"""
        pass
