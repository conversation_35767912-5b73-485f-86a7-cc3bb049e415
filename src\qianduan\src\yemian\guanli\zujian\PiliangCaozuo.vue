<template>
  <el-dialog
    v-model="visible"
    title="批量操作"
    width="600px"
    :before-close="handleClose"
  >
    <div class="batch-operation-container">
      <el-alert
        :title="`已选择 ${selectedUsers.length} 个用户`"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />
      
      <el-form :model="batchForm" label-width="120px">
        <el-form-item label="操作类型">
          <el-select v-model="batchForm.operation" placeholder="请选择操作类型" style="width: 100%">
            <el-option label="批量启用" value="enable" />
            <el-option label="批量禁用" value="disable" />
            <el-option label="批量删除" value="delete" />
            <el-option label="批量升级订阅" value="upgrade" />
            <el-option label="批量降级订阅" value="downgrade" />
            <el-option label="批量延长订阅" value="extend" />
          </el-select>
        </el-form-item>
        
        <!-- 订阅相关操作的额外选项 -->
        <template v-if="['upgrade', 'downgrade'].includes(batchForm.operation)">
          <el-form-item label="目标订阅类型">
            <el-select v-model="batchForm.targetSubscription" placeholder="请选择订阅类型" style="width: 100%">
              <el-option label="注册用户版" value="zhuce" />
              <el-option label="基础版" value="jiben" />
              <el-option label="高级版" value="gaoji" />
            </el-select>
          </el-form-item>
        </template>
        
        <template v-if="batchForm.operation === 'extend'">
          <el-form-item label="延长天数">
            <el-input-number
              v-model="batchForm.extendDays"
              :min="1"
              :max="365"
              placeholder="请输入延长天数"
              style="width: 100%"
            />
          </el-form-item>
        </template>
        
        <!-- 操作原因 -->
        <el-form-item label="操作原因">
          <el-input
            v-model="batchForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入操作原因（可选）"
          />
        </el-form-item>
      </el-form>
      
      <!-- 预览受影响的用户 -->
      <div class="affected-users">
        <h4>受影响的用户：</h4>
        <div class="user-list">
          <el-tag
            v-for="user in selectedUsers"
            :key="user.id"
            class="user-tag"
            closable
            @close="removeUser(user.id)"
          >
            {{ user.yonghuming }} ({{ getSubscriptionText(user.dingyue_leixing) }})
          </el-tag>
        </div>
      </div>
      
      <!-- 操作确认 -->
      <div v-if="batchForm.operation" class="operation-preview">
        <el-alert
          :title="getOperationDescription()"
          :type="getOperationAlertType()"
          :closable="false"
          show-icon
        />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleExecute"
          :loading="executing"
          :disabled="!batchForm.operation || selectedUsers.length === 0"
        >
          执行操作
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { userService } from '@/fuwu/userService'

// Props
interface Props {
  modelValue: boolean
  selectedUsers: any[]
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  selectedUsers: () => []
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'refresh': []
  'clear-selection': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const executing = ref(false)

const batchForm = reactive({
  operation: '',
  targetSubscription: '',
  extendDays: 30,
  reason: ''
})

// 工具函数
const getSubscriptionText = (type: string) => {
  const typeMap: Record<string, string> = {
    zhuce: '注册用户版',
    jiben: '基础版',
    gaoji: '高级版'
  }
  return typeMap[type] || '未知'
}

const getOperationDescription = () => {
  const count = props.selectedUsers.length
  switch (batchForm.operation) {
    case 'enable':
      return `将启用 ${count} 个用户账户`
    case 'disable':
      return `将禁用 ${count} 个用户账户`
    case 'delete':
      return `将删除 ${count} 个用户账户（此操作不可恢复）`
    case 'upgrade':
      return `将 ${count} 个用户升级到 ${getSubscriptionText(batchForm.targetSubscription)}`
    case 'downgrade':
      return `将 ${count} 个用户降级到 ${getSubscriptionText(batchForm.targetSubscription)}`
    case 'extend':
      return `将为 ${count} 个用户延长 ${batchForm.extendDays} 天订阅`
    default:
      return ''
  }
}

const getOperationAlertType = () => {
  switch (batchForm.operation) {
    case 'delete':
      return 'error'
    case 'disable':
      return 'warning'
    case 'upgrade':
    case 'extend':
      return 'success'
    default:
      return 'info'
  }
}

// 方法
const removeUser = (userId: number) => {
  // 这里应该通知父组件移除选中的用户
  ElMessage.info('请在用户列表中取消选择该用户')
}

const handleExecute = async () => {
  if (!batchForm.operation) {
    ElMessage.warning('请选择操作类型')
    return
  }
  
  if (props.selectedUsers.length === 0) {
    ElMessage.warning('请选择要操作的用户')
    return
  }
  
  // 危险操作需要二次确认
  if (['delete', 'disable'].includes(batchForm.operation)) {
    try {
      await ElMessageBox.confirm(
        getOperationDescription() + '，确定要继续吗？',
        '危险操作确认',
        {
          confirmButtonText: '确定执行',
          cancelButtonText: '取消',
          type: 'error'
        }
      )
    } catch {
      return
    }
  }
  
  try {
    executing.value = true
    
    // 执行批量操作
    const results = await Promise.allSettled(
      props.selectedUsers.map(user => executeSingleOperation(user))
    )
    
    // 统计结果
    const successCount = results.filter(r => r.status === 'fulfilled').length
    const failCount = results.length - successCount
    
    if (failCount === 0) {
      ElMessage.success(`批量操作完成，成功处理 ${successCount} 个用户`)
    } else {
      ElMessage.warning(`批量操作完成，成功 ${successCount} 个，失败 ${failCount} 个`)
    }
    
    // 刷新数据并关闭对话框
    emit('refresh')
    emit('clear-selection')
    handleClose()
    
  } catch (error) {
    console.error('批量操作失败:', error)
    ElMessage.error('批量操作失败')
  } finally {
    executing.value = false
  }
}

const executeSingleOperation = async (user: any) => {
  switch (batchForm.operation) {
    case 'enable':
      return await userService.updateUser(user.id, { shi_huoyue: true })
    case 'disable':
      return await userService.updateUser(user.id, { shi_huoyue: false })
    case 'delete':
      return await userService.deleteUser(user.id)
    case 'upgrade':
    case 'downgrade':
      return await userService.updateUser(user.id, { 
        dingyue_leixing: batchForm.targetSubscription 
      })
    case 'extend':
      // 计算新的结束日期
      const currentEndDate = new Date(user.dingyue_jieshu)
      const newEndDate = new Date(currentEndDate.getTime() + batchForm.extendDays * 24 * 60 * 60 * 1000)
      return await userService.updateUser(user.id, { 
        dingyue_jieshu: newEndDate.toISOString().split('T')[0]
      })
    default:
      throw new Error('未知操作类型')
  }
}

const handleClose = () => {
  // 重置表单
  batchForm.operation = ''
  batchForm.targetSubscription = ''
  batchForm.extendDays = 30
  batchForm.reason = ''
  
  visible.value = false
}
</script>

<style lang="scss" scoped>
.batch-operation-container {
  .affected-users {
    margin: 20px 0;
    
    h4 {
      margin-bottom: 10px;
      color: var(--el-text-color-primary);
    }
    
    .user-list {
      max-height: 120px;
      overflow-y: auto;
      
      .user-tag {
        margin: 4px 8px 4px 0;
      }
    }
  }
  
  .operation-preview {
    margin-top: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
