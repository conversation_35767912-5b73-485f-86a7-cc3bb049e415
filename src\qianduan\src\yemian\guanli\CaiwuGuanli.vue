<template>
  <div class="finance-management-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">财务管理</span>
          <div class="header-actions">
            <el-button type="success" @click="handleExportReport" :loading="exporting">
              <el-icon><Download /></el-icon>
              导出报表
            </el-button>
            <el-button type="primary" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="finance-content">
        <!-- 财务概览 -->
        <div class="finance-overview">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="overview-card">
                <div class="overview-icon today">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="overview-content">
                  <div class="overview-value">¥{{ formatMoney(financeStats.today_revenue) }}</div>
                  <div class="overview-label">今日收入</div>
                  <div class="overview-change" :class="{ positive: todayChange > 0, negative: todayChange < 0 }">
                    {{ todayChange > 0 ? '+' : '' }}{{ todayChange.toFixed(1) }}%
                  </div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="overview-card">
                <div class="overview-icon week">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="overview-content">
                  <div class="overview-value">¥{{ formatMoney(financeStats.week_revenue) }}</div>
                  <div class="overview-label">本周收入</div>
                  <div class="overview-sub">{{ financeStats.week_orders }} 笔订单</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="overview-card">
                <div class="overview-icon month">
                  <el-icon><DataAnalysis /></el-icon>
                </div>
                <div class="overview-content">
                  <div class="overview-value">¥{{ formatMoney(financeStats.month_revenue) }}</div>
                  <div class="overview-label">本月收入</div>
                  <div class="overview-sub">{{ financeStats.month_orders }} 笔订单</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="overview-card">
                <div class="overview-icon total">
                  <el-icon><Coin /></el-icon>
                </div>
                <div class="overview-content">
                  <div class="overview-value">¥{{ formatMoney(financeStats.total_revenue) }}</div>
                  <div class="overview-label">累计收入</div>
                  <div class="overview-sub">¥{{ formatMoney(financeStats.avg_order_value) }} 客单价</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 关键指标 -->
        <div class="key-metrics">
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="metric-card">
                <div class="metric-title">退款情况</div>
                <div class="metric-content">
                  <div class="metric-value refund">¥{{ formatMoney(financeStats.refund_amount) }}</div>
                  <div class="metric-desc">退款率 {{ financeStats.refund_rate.toFixed(1) }}%</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="metric-card">
                <div class="metric-title">转化率</div>
                <div class="metric-content">
                  <div class="metric-value conversion">{{ financeStats.conversion_rate.toFixed(1) }}%</div>
                  <div class="metric-desc">注册用户付费转化</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="8">
              <div class="metric-card">
                <div class="metric-title">今日订单</div>
                <div class="metric-content">
                  <div class="metric-value orders">{{ financeStats.today_orders }}</div>
                  <div class="metric-desc">比昨日订单数</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 收入趋势图表 -->
        <div class="revenue-chart">
          <el-card>
            <template #header>
              <div class="chart-header">
                <span class="chart-title">收入趋势</span>
                <div class="chart-controls">
                  <el-radio-group v-model="chartPeriod" size="small">
                    <el-radio-button label="7">7天</el-radio-button>
                    <el-radio-button label="30">30天</el-radio-button>
                    <el-radio-button label="90">90天</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>
            
            <div class="chart-container">
              <v-chart 
                :option="revenueChartOption" 
                :loading="chartLoading"
                class="chart"
                autoresize
              />
            </div>
          </el-card>
        </div>
        
        <!-- 功能导航 -->
        <div class="function-nav">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="nav-card" @click="activeTab = 'payment'">
                <div class="nav-icon">
                  <el-icon><CreditCard /></el-icon>
                </div>
                <div class="nav-content">
                  <div class="nav-title">支付管理</div>
                  <div class="nav-desc">支付渠道配置</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="nav-card" @click="activeTab = 'refund'">
                <div class="nav-icon">
                  <el-icon><RefreshLeft /></el-icon>
                </div>
                <div class="nav-content">
                  <div class="nav-title">退款管理</div>
                  <div class="nav-desc">退款申请处理</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="nav-card" @click="activeTab = 'report'">
                <div class="nav-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="nav-content">
                  <div class="nav-title">财务报表</div>
                  <div class="nav-desc">详细财务数据</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="nav-card" @click="activeTab = 'analysis'">
                <div class="nav-icon">
                  <el-icon><PieChart /></el-icon>
                </div>
                <div class="nav-content">
                  <div class="nav-title">数据分析</div>
                  <div class="nav-desc">收入分析报告</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
    
    <!-- 子功能弹窗 -->
    <ZhifuGuanli v-model="showPaymentDialog" @refresh="loadFinanceData" />
    <TuikuanGuanli v-model="showRefundDialog" @refresh="loadFinanceData" />
    <CaiwuBaobiao v-model="showReportDialog" />
    <ShouyiFenxi v-model="showAnalysisDialog" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { use } from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import { financeService } from '@/fuwu/financeService'
import ZhifuGuanli from './zujian/ZhifuGuanli.vue'
import TuikuanGuanli from './zujian/TuikuanGuanli.vue'
import CaiwuBaobiao from './zujian/CaiwuBaobiao.vue'
import ShouyiFenxi from './zujian/ShouyiFenxi.vue'

// 注册ECharts组件
use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const chartLoading = ref(false)
const chartPeriod = ref('30')
const activeTab = ref('')

// 弹窗控制
const showPaymentDialog = computed({
  get: () => activeTab.value === 'payment',
  set: (value) => activeTab.value = value ? 'payment' : ''
})

const showRefundDialog = computed({
  get: () => activeTab.value === 'refund',
  set: (value) => activeTab.value = value ? 'refund' : ''
})

const showReportDialog = computed({
  get: () => activeTab.value === 'report',
  set: (value) => activeTab.value = value ? 'report' : ''
})

const showAnalysisDialog = computed({
  get: () => activeTab.value === 'analysis',
  set: (value) => activeTab.value = value ? 'analysis' : ''
})

// 数据
const financeStats = ref<any>({})
const revenueTrend = ref<any[]>([])

// 计算属性
const todayChange = computed(() => {
  if (!financeStats.value.today_revenue || !financeStats.value.yesterday_revenue) return 0
  return ((financeStats.value.today_revenue - financeStats.value.yesterday_revenue) / financeStats.value.yesterday_revenue) * 100
})

// 收入图表配置
const revenueChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const data = params[0]
      return `${data.name}<br/>收入: ¥${data.value.toLocaleString()}`
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: revenueTrend.value.map(item => dayjs(item.date).format('MM-DD')),
    axisLine: {
      lineStyle: {
        color: '#e4e7ed'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: (value: number) => `¥${(value / 1000).toFixed(0)}k`
    },
    axisLine: {
      lineStyle: {
        color: '#e4e7ed'
      }
    },
    splitLine: {
      lineStyle: {
        color: '#f5f7fa'
      }
    }
  },
  series: [
    {
      name: '收入',
      type: 'line',
      data: revenueTrend.value.map(item => item.revenue),
      smooth: true,
      lineStyle: {
        color: '#67c23a',
        width: 3
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
            { offset: 1, color: 'rgba(103, 194, 58, 0.05)' }
          ]
        }
      }
    }
  ]
}))

// 工具函数
const formatMoney = (amount: number) => {
  if (amount >= 10000) {
    return `${(amount / 10000).toFixed(1)}万`
  }
  return amount.toLocaleString()
}

// 方法
const loadFinanceData = async () => {
  try {
    loading.value = true
    const stats = await financeService.getFinanceStats()
    financeStats.value = stats
  } catch (error) {
    console.error('加载财务数据失败:', error)
    ElMessage.error('加载财务数据失败')
  } finally {
    loading.value = false
  }
}

const loadRevenueTrend = async () => {
  try {
    chartLoading.value = true
    const days = parseInt(chartPeriod.value)
    const trend = await financeService.getRevenueTrend(days)
    revenueTrend.value = trend
  } catch (error) {
    console.error('加载收入趋势失败:', error)
  } finally {
    chartLoading.value = false
  }
}

const handleExportReport = async () => {
  try {
    exporting.value = true
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('财务报表导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const handleRefresh = () => {
  loadFinanceData()
  loadRevenueTrend()
}

// 监听器
watch(chartPeriod, () => {
  loadRevenueTrend()
})

// 生命周期
onMounted(() => {
  loadFinanceData()
  loadRevenueTrend()
})
</script>

<style lang="scss" scoped>
.finance-management-container {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .card-title {
    font-size: 18px;
    font-weight: 600;
  }

  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.finance-content {
  .finance-overview {
    margin-bottom: 20px;

    .overview-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      height: 100px;

      .overview-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-right: 16px;

        &.today {
          background: linear-gradient(135deg, #67c23a, #85ce61);
        }

        &.week {
          background: linear-gradient(135deg, #409eff, #66b1ff);
        }

        &.month {
          background: linear-gradient(135deg, #e6a23c, #ebb563);
        }

        &.total {
          background: linear-gradient(135deg, #f56c6c, #f78989);
        }
      }

      .overview-content {
        flex: 1;

        .overview-value {
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }

        .overview-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
          margin-bottom: 4px;
        }

        .overview-change {
          font-size: 12px;
          font-weight: 500;

          &.positive {
            color: #67c23a;
          }

          &.negative {
            color: #f56c6c;
          }
        }

        .overview-sub {
          font-size: 12px;
          color: var(--el-text-color-placeholder);
        }
      }
    }
  }

  .key-metrics {
    margin-bottom: 20px;

    .metric-card {
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      text-align: center;

      .metric-title {
        font-size: 14px;
        color: var(--el-text-color-regular);
        margin-bottom: 12px;
      }

      .metric-content {
        .metric-value {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 4px;

          &.refund {
            color: #f56c6c;
          }

          &.conversion {
            color: #67c23a;
          }

          &.orders {
            color: #409eff;
          }
        }

        .metric-desc {
          font-size: 12px;
          color: var(--el-text-color-placeholder);
        }
      }
    }
  }

  .revenue-chart {
    margin-bottom: 20px;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .chart-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    .chart-container {
      height: 300px;

      .chart {
        width: 100%;
        height: 100%;
      }
    }
  }

  .function-nav {
    .nav-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .nav-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: linear-gradient(135deg, #409eff, #66b1ff);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        margin-right: 12px;
      }

      .nav-content {
        .nav-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }

        .nav-desc {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .finance-overview,
  .key-metrics,
  .function-nav {
    .el-row {
      flex-direction: column;
    }

    .el-col {
      width: 100% !important;
      margin-bottom: 16px;
    }
  }
}
</style>
