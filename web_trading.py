#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的Web交易页面
基于Flask框架，提供Web界面进行股票交易
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
import easyquant
from easyquant import DefaultLogHandler
import json
import threading
import time
import os
import requests
import easyquotation

app = Flask(__name__)

# 全局变量
main_engine = None
engine_thread = None
is_running = False
account_info = {}
positions = []
current_quotes = {}
quotation_engine = None

class WebLogHandler:
    """Web日志处理器"""
    def __init__(self):
        self.logs = []
        self.max_logs = 100
    
    def info(self, msg):
        self.logs.append(f"[INFO] {time.strftime('%H:%M:%S')} {msg}")
        if len(self.logs) > self.max_logs:
            self.logs.pop(0)
    
    def error(self, msg):
        self.logs.append(f"[ERROR] {time.strftime('%H:%M:%S')} {msg}")
        if len(self.logs) > self.max_logs:
            self.logs.pop(0)
    
    def warning(self, msg):
        self.logs.append(f"[WARNING] {time.strftime('%H:%M:%S')} {msg}")
        if len(self.logs) > self.max_logs:
            self.logs.pop(0)

web_log = WebLogHandler()

def update_account_info():
    """更新账户信息"""
    global account_info, positions
    try:
        if main_engine and hasattr(main_engine, 'user') and main_engine.user:
            # 获取账户余额
            try:
                balance = main_engine.user.balance
                if balance:
                    account_info = balance[0] if isinstance(balance, list) else balance
            except Exception as e:
                web_log.warning(f"获取账户余额失败: {str(e)}")
                # 设置默认账户信息
                account_info = {
                    'asset_balance': 0,
                    'enable_balance': 0,
                    'market_value': 0,
                    'money_type': 'CNY'
                }

            # 获取持仓信息
            try:
                positions = main_engine.user.position or []
            except Exception as e:
                web_log.warning(f"获取持仓信息失败: {str(e)}")
                positions = []
    except Exception as e:
        web_log.error(f"更新账户信息失败: {str(e)}")

def init_quotation_engine():
    """初始化行情引擎"""
    global quotation_engine
    try:
        quotation_engine = easyquotation.use('sina')
        web_log.info("行情引擎初始化成功")
        return True
    except Exception as e:
        web_log.error(f"行情引擎初始化失败: {str(e)}")
        return False

def init_trading_user():
    """初始化交易用户（不使用完整引擎）"""
    global main_engine, is_running
    try:
        # 直接创建交易用户，避免信号处理问题
        import easytrader

        # 创建雪球交易用户
        user = easytrader.use('xq')
        user.prepare('xq.json')

        # 创建一个简化的引擎对象
        class SimpleEngine:
            def __init__(self, user):
                self.user = user

        main_engine = SimpleEngine(user)

        web_log.info("交易用户初始化成功")
        is_running = True
        return True

    except Exception as e:
        web_log.error(f"交易用户初始化失败: {str(e)}")
        is_running = False
        return False

def engine_worker():
    """引擎工作线程"""
    try:
        # 初始化行情引擎
        init_quotation_engine()

        # 初始化交易用户
        init_trading_user()

        # 保持线程运行
        while is_running:
            time.sleep(1)

    except Exception as e:
        web_log.error(f"引擎工作线程失败: {str(e)}")
        global is_running
        is_running = False

@app.route('/')
def index():
    """主页"""
    return render_template('trading.html')

@app.route('/start_engine', methods=['POST'])
def start_engine():
    """启动交易引擎"""
    global engine_thread, is_running
    
    if is_running:
        return jsonify({'success': False, 'message': '引擎已在运行中'})
    
    try:
        engine_thread = threading.Thread(target=engine_worker)
        engine_thread.daemon = True
        engine_thread.start()
        
        # 等待一下确保启动
        time.sleep(2)
        
        return jsonify({'success': True, 'message': '引擎启动成功'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'启动失败: {str(e)}'})

@app.route('/stop_engine', methods=['POST'])
def stop_engine():
    """停止交易引擎"""
    global main_engine, is_running
    
    try:
        if main_engine:
            main_engine.stop()
        is_running = False
        web_log.info("交易引擎已停止")
        return jsonify({'success': True, 'message': '引擎已停止'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'停止失败: {str(e)}'})

@app.route('/get_status')
def get_status():
    """获取系统状态"""
    update_account_info()
    
    return jsonify({
        'is_running': is_running,
        'account_info': account_info,
        'positions': positions,
        'logs': web_log.logs[-10:],  # 最近10条日志
        'quotes': current_quotes
    })

@app.route('/buy_stock', methods=['POST'])
def buy_stock():
    """买入股票"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        price = float(data.get('price', 0))
        amount = int(data.get('amount', 0))
        
        if not main_engine or not main_engine.user:
            return jsonify({'success': False, 'message': '交易引擎未启动'})
        
        # 执行买入操作
        result = main_engine.user.buy(stock_code, price=price, amount=amount)
        
        web_log.info(f"买入操作: {stock_code} 价格:{price} 数量:{amount}")
        
        return jsonify({
            'success': True, 
            'message': f'买入委托已提交: {stock_code}',
            'result': str(result)
        })
        
    except Exception as e:
        error_msg = f"买入失败: {str(e)}"
        web_log.error(error_msg)
        return jsonify({'success': False, 'message': error_msg})

@app.route('/sell_stock', methods=['POST'])
def sell_stock():
    """卖出股票"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        price = float(data.get('price', 0))
        amount = int(data.get('amount', 0))
        
        if not main_engine or not main_engine.user:
            return jsonify({'success': False, 'message': '交易引擎未启动'})
        
        # 执行卖出操作
        result = main_engine.user.sell(stock_code, price=price, amount=amount)
        
        web_log.info(f"卖出操作: {stock_code} 价格:{price} 数量:{amount}")
        
        return jsonify({
            'success': True, 
            'message': f'卖出委托已提交: {stock_code}',
            'result': str(result)
        })
        
    except Exception as e:
        error_msg = f"卖出失败: {str(e)}"
        web_log.error(error_msg)
        return jsonify({'success': False, 'message': error_msg})

@app.route('/get_quote/<stock_code>')
def get_quote(stock_code):
    """获取股票行情"""
    try:
        if not quotation_engine:
            return jsonify({'success': False, 'message': '行情引擎未启动'})

        # 获取实时行情数据
        quote_data = quotation_engine.real([stock_code])

        if stock_code in quote_data:
            stock_info = quote_data[stock_code]

            # 计算五档价格
            current_price = float(stock_info.get('now', 0))
            if current_price <= 0:
                return jsonify({'success': False, 'message': '无法获取股票价格'})

            # 生成五档买卖价格（简化版本）
            tick_size = 0.01 if current_price < 10 else 0.01  # 价格变动单位

            five_levels = []
            for i in range(5):
                buy_price = round(current_price - (i + 1) * tick_size, 2)
                sell_price = round(current_price + (i + 1) * tick_size, 2)
                five_levels.append({
                    'level': i + 1,
                    'buy_price': buy_price,
                    'sell_price': sell_price,
                    'buy_volume': int(stock_info.get(f'bid{i+1}_volume', 0)),
                    'sell_volume': int(stock_info.get(f'ask{i+1}_volume', 0))
                })

            quote = {
                'code': stock_code,
                'name': stock_info.get('name', f'股票{stock_code}'),
                'price': current_price,
                'open': float(stock_info.get('open', 0)),
                'close': float(stock_info.get('close', 0)),
                'high': float(stock_info.get('high', 0)),
                'low': float(stock_info.get('low', 0)),
                'change': round(current_price - float(stock_info.get('close', current_price)), 2),
                'change_percent': round((current_price - float(stock_info.get('close', current_price))) / float(stock_info.get('close', current_price)) * 100, 2) if float(stock_info.get('close', 0)) > 0 else 0,
                'volume': int(stock_info.get('turnover', 0)),
                'five_levels': five_levels
            }

            return jsonify({'success': True, 'quote': quote})
        else:
            return jsonify({'success': False, 'message': '股票代码不存在或无法获取数据'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取行情失败: {str(e)}'})

if __name__ == '__main__':
    # 确保templates目录存在
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("启动Web交易界面...")
    print("访问地址: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
