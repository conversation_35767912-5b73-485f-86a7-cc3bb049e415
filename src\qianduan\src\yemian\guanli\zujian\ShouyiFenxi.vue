<template>
  <el-dialog
    v-model="visible"
    title="收益分析"
    width="1200px"
    :before-close="handleClose"
  >
    <div class="revenue-analysis-container" v-loading="loading">
      <div class="analysis-content">
        <!-- 分析维度选择 -->
        <div class="analysis-controls">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-select v-model="analysisType" placeholder="分析维度" style="width: 100%">
                <el-option label="时间分析" value="time" />
                <el-option label="用户分析" value="user" />
                <el-option label="产品分析" value="product" />
                <el-option label="渠道分析" value="channel" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select v-model="timePeriod" placeholder="时间周期" style="width: 100%">
                <el-option label="最近7天" value="7" />
                <el-option label="最近30天" value="30" />
                <el-option label="最近90天" value="90" />
                <el-option label="本年度" value="365" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-button type="primary" @click="generateAnalysis">
                <el-icon><DataAnalysis /></el-icon>
                生成分析
              </el-button>
            </el-col>
          </el-row>
        </div>
        
        <!-- 核心指标卡片 -->
        <div class="key-metrics">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-icon revenue">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">¥{{ formatMoney(metrics.totalRevenue) }}</div>
                  <div class="metric-label">总收入</div>
                  <div class="metric-change positive">+{{ metrics.revenueGrowth }}%</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-icon users">
                  <el-icon><User /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">{{ metrics.payingUsers }}</div>
                  <div class="metric-label">付费用户</div>
                  <div class="metric-change positive">+{{ metrics.userGrowth }}%</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-icon arpu">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">¥{{ metrics.arpu.toFixed(0) }}</div>
                  <div class="metric-label">ARPU</div>
                  <div class="metric-change positive">+{{ metrics.arpuGrowth }}%</div>
                </div>
              </div>
            </el-col>
            
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-icon ltv">
                  <el-icon><Coin /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">¥{{ metrics.ltv.toFixed(0) }}</div>
                  <div class="metric-label">LTV</div>
                  <div class="metric-change positive">+{{ metrics.ltvGrowth }}%</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 分析图表 -->
        <div class="analysis-charts">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>收入趋势分析</span>
                </template>
                <div class="chart-container">
                  <v-chart 
                    :option="revenueAnalysisOption" 
                    :loading="chartLoading"
                    class="chart"
                    autoresize
                  />
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>用户价值分布</span>
                </template>
                <div class="chart-container">
                  <v-chart 
                    :option="userValueOption" 
                    :loading="chartLoading"
                    class="chart"
                    autoresize
                  />
                </div>
              </el-card>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" style="margin-top: 16px;">
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>产品收入占比</span>
                </template>
                <div class="chart-container">
                  <v-chart 
                    :option="productRevenueOption" 
                    :loading="chartLoading"
                    class="chart"
                    autoresize
                  />
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card>
                <template #header>
                  <span>渠道转化分析</span>
                </template>
                <div class="chart-container">
                  <v-chart 
                    :option="channelConversionOption" 
                    :loading="chartLoading"
                    class="chart"
                    autoresize
                  />
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <!-- 分析洞察 -->
        <div class="analysis-insights">
          <el-card>
            <template #header>
              <span>分析洞察</span>
            </template>
            
            <div class="insights-content">
              <div class="insight-item" v-for="insight in insights" :key="insight.id">
                <div class="insight-icon" :class="insight.type">
                  <el-icon>
                    <TrendCharts v-if="insight.type === 'trend'" />
                    <Warning v-else-if="insight.type === 'warning'" />
                    <SuccessFilled v-else-if="insight.type === 'success'" />
                    <InfoFilled v-else />
                  </el-icon>
                </div>
                <div class="insight-content">
                  <div class="insight-title">{{ insight.title }}</div>
                  <div class="insight-description">{{ insight.description }}</div>
                  <div class="insight-suggestion" v-if="insight.suggestion">
                    <strong>建议：</strong>{{ insight.suggestion }}
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportAnalysis">
          <el-icon><Download /></el-icon>
          导出分析报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { use } from 'echarts/core'
import { LineChart, BarChart, PieChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import { financeService } from '@/fuwu/financeService'

// 注册ECharts组件
use([LineChart, BarChart, PieChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer])

// Props
interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const chartLoading = ref(false)
const analysisType = ref('time')
const timePeriod = ref('30')

// 数据
const metrics = ref({
  totalRevenue: 1580000,
  revenueGrowth: 15.2,
  payingUsers: 1250,
  userGrowth: 8.5,
  arpu: 1264,
  arpuGrowth: 6.2,
  ltv: 3850,
  ltvGrowth: 12.8
})

const insights = ref([
  {
    id: 1,
    type: 'success',
    title: '收入增长强劲',
    description: '本月收入较上月增长15.2%，主要得益于高级版订阅用户增加',
    suggestion: '继续优化高级版功能，提升用户价值感知'
  },
  {
    id: 2,
    type: 'trend',
    title: 'ARPU值稳步提升',
    description: '用户平均收入贡献从1190元提升至1264元',
    suggestion: '可以考虑推出更高价值的产品套餐'
  },
  {
    id: 3,
    type: 'warning',
    title: '策略销售占比下降',
    description: '策略销售收入占比从35%下降至28%',
    suggestion: '加强策略推广，提升策略质量和曝光度'
  },
  {
    id: 4,
    type: 'info',
    title: '用户留存率良好',
    description: '付费用户30天留存率达到85%，高于行业平均水平',
    suggestion: '继续保持产品质量，关注用户反馈'
  }
])

// 图表配置
const revenueAnalysisOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['收入', '订单数']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: Array.from({ length: 30 }, (_, i) => dayjs().subtract(29 - i, 'day').format('MM-DD'))
  },
  yAxis: [
    {
      type: 'value',
      name: '收入',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    {
      type: 'value',
      name: '订单数',
      axisLabel: {
        formatter: '{value}'
      }
    }
  ],
  series: [
    {
      name: '收入',
      type: 'line',
      data: Array.from({ length: 30 }, () => 15000 + Math.random() * 10000),
      smooth: true
    },
    {
      name: '订单数',
      type: 'bar',
      yAxisIndex: 1,
      data: Array.from({ length: 30 }, () => 50 + Math.random() * 30)
    }
  ]
}))

const userValueOption = computed(() => ({
  tooltip: {
    trigger: 'item'
  },
  series: [
    {
      name: '用户价值分布',
      type: 'pie',
      radius: ['40%', '70%'],
      data: [
        { value: 450, name: '高价值用户(>1000元)' },
        { value: 380, name: '中价值用户(500-1000元)' },
        { value: 280, name: '低价值用户(100-500元)' },
        { value: 140, name: '新用户(<100元)' }
      ]
    }
  ]
}))

const productRevenueOption = computed(() => ({
  tooltip: {
    trigger: 'item'
  },
  series: [
    {
      name: '产品收入',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 948000, name: '订阅服务' },
        { value: 442400, name: '策略销售' },
        { value: 189600, name: '其他服务' }
      ]
    }
  ]
}))

const channelConversionOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['搜索引擎', '社交媒体', '直接访问', '推荐链接', '广告投放']
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '{value}%'
    }
  },
  series: [
    {
      name: '转化率',
      type: 'bar',
      data: [12.5, 8.3, 15.2, 6.8, 9.7],
      itemStyle: {
        color: '#67c23a'
      }
    }
  ]
}))

// 工具函数
const formatMoney = (amount: number) => {
  if (amount >= 10000) {
    return `${(amount / 10000).toFixed(1)}万`
  }
  return amount.toLocaleString()
}

// 方法
const generateAnalysis = async () => {
  try {
    loading.value = true
    chartLoading.value = true
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('分析报告生成完成')
  } catch (error) {
    console.error('生成分析失败:', error)
    ElMessage.error('生成分析失败')
  } finally {
    loading.value = false
    chartLoading.value = false
  }
}

const exportAnalysis = async () => {
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('分析报告导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handleClose = () => {
  visible.value = false
}

// 监听器
watch(visible, (newVisible) => {
  if (newVisible) {
    generateAnalysis()
  }
})
</script>

<style lang="scss" scoped>
.revenue-analysis-container {
  min-height: 800px;

  .analysis-content {
    .analysis-controls {
      margin-bottom: 20px;
    }

    .key-metrics {
      margin-bottom: 20px;

      .metric-card {
        display: flex;
        align-items: center;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .metric-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: white;
          margin-right: 16px;

          &.revenue {
            background: linear-gradient(135deg, #67c23a, #85ce61);
          }

          &.users {
            background: linear-gradient(135deg, #409eff, #66b1ff);
          }

          &.arpu {
            background: linear-gradient(135deg, #e6a23c, #ebb563);
          }

          &.ltv {
            background: linear-gradient(135deg, #f56c6c, #f78989);
          }
        }

        .metric-content {
          .metric-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            margin-bottom: 4px;
          }

          .metric-label {
            font-size: 14px;
            color: var(--el-text-color-regular);
            margin-bottom: 4px;
          }

          .metric-change {
            font-size: 12px;
            font-weight: 500;

            &.positive {
              color: #67c23a;
            }

            &.negative {
              color: #f56c6c;
            }
          }
        }
      }
    }

    .analysis-charts {
      margin-bottom: 20px;

      .chart-container {
        height: 300px;

        .chart {
          width: 100%;
          height: 100%;
        }
      }
    }

    .analysis-insights {
      .insights-content {
        .insight-item {
          display: flex;
          align-items: flex-start;
          padding: 16px;
          margin-bottom: 16px;
          background: #f8f9fa;
          border-radius: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .insight-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            margin-right: 12px;
            flex-shrink: 0;

            &.success {
              background: #67c23a;
            }

            &.trend {
              background: #409eff;
            }

            &.warning {
              background: #e6a23c;
            }

            &.info {
              background: #909399;
            }
          }

          .insight-content {
            flex: 1;

            .insight-title {
              font-size: 16px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              margin-bottom: 8px;
            }

            .insight-description {
              font-size: 14px;
              color: var(--el-text-color-regular);
              margin-bottom: 8px;
              line-height: 1.5;
            }

            .insight-suggestion {
              font-size: 13px;
              color: var(--el-text-color-secondary);
              line-height: 1.4;

              strong {
                color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
