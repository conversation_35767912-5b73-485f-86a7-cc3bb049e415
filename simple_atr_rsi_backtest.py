#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的ATR RSI策略回测
不依赖VeighNa，使用基础库进行策略测试
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SimpleATRRSIStrategy:
    """简化的ATR RSI策略"""
    
    def __init__(self, atr_length=14, atr_ma_length=30, rsi_length=14, 
                 rsi_entry=16, trailing_percent=0.8):
        self.atr_length = atr_length
        self.atr_ma_length = atr_ma_length
        self.rsi_length = rsi_length
        self.rsi_entry = rsi_entry
        self.trailing_percent = trailing_percent
        
        self.rsi_buy = 50 + rsi_entry
        self.rsi_sell = 50 - rsi_entry
        
        self.position = 0  # 0: 空仓, 1: 多头, -1: 空头
        self.entry_price = 0
        self.highest_price = 0
        self.lowest_price = 0
        
        self.trades = []
        self.equity_curve = []
        
    def calculate_atr(self, high, low, close, length):
        """计算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=length).mean()
        return atr
    
    def calculate_rsi(self, close, length):
        """计算RSI"""
        delta = close.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=length).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=length).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def backtest(self, data):
        """回测策略"""
        print("📊 开始ATR RSI策略回测")
        print("=" * 50)
        
        # 计算技术指标
        data['atr'] = self.calculate_atr(data['high'], data['low'], data['close'], self.atr_length)
        data['atr_ma'] = data['atr'].rolling(window=self.atr_ma_length).mean()
        data['rsi'] = self.calculate_rsi(data['close'], self.rsi_length)
        
        # 初始化
        initial_capital = 1000000
        capital = initial_capital
        shares = 0
        
        for i in range(len(data)):
            if i < max(self.atr_length, self.atr_ma_length, self.rsi_length):
                continue
                
            current_price = data.iloc[i]['close']
            atr_value = data.iloc[i]['atr']
            atr_ma_value = data.iloc[i]['atr_ma']
            rsi_value = data.iloc[i]['rsi']
            
            # 判断趋势
            is_trending = atr_value > atr_ma_value
            
            if self.position == 0:  # 空仓
                if is_trending:
                    # 多头信号
                    if rsi_value > self.rsi_buy:
                        self.position = 1
                        self.entry_price = current_price
                        self.highest_price = current_price
                        shares = capital // current_price
                        capital -= shares * current_price
                        
                        self.trades.append({
                            'date': data.index[i],
                            'action': 'BUY',
                            'price': current_price,
                            'shares': shares,
                            'capital': capital + shares * current_price
                        })
                    
                    # 空头信号
                    elif rsi_value < self.rsi_sell:
                        self.position = -1
                        self.entry_price = current_price
                        self.lowest_price = current_price
                        shares = capital // current_price
                        capital += shares * current_price  # 做空获得资金
                        
                        self.trades.append({
                            'date': data.index[i],
                            'action': 'SHORT',
                            'price': current_price,
                            'shares': shares,
                            'capital': capital - shares * current_price
                        })
            
            elif self.position == 1:  # 多头持仓
                self.highest_price = max(self.highest_price, current_price)
                
                # 移动止损或趋势结束
                stop_price = self.highest_price * (1 - self.trailing_percent / 100)
                
                if current_price <= stop_price or not is_trending:
                    self.position = 0
                    capital += shares * current_price
                    
                    self.trades.append({
                        'date': data.index[i],
                        'action': 'SELL',
                        'price': current_price,
                        'shares': shares,
                        'capital': capital
                    })
                    shares = 0
            
            elif self.position == -1:  # 空头持仓
                self.lowest_price = min(self.lowest_price, current_price)
                
                # 移动止损或趋势结束
                stop_price = self.lowest_price * (1 + self.trailing_percent / 100)
                
                if current_price >= stop_price or not is_trending:
                    self.position = 0
                    capital -= shares * current_price  # 平空头
                    
                    self.trades.append({
                        'date': data.index[i],
                        'action': 'COVER',
                        'price': current_price,
                        'shares': shares,
                        'capital': capital
                    })
                    shares = 0
            
            # 记录权益曲线
            if self.position == 0:
                total_value = capital
            elif self.position == 1:
                total_value = capital + shares * current_price
            else:  # position == -1
                total_value = capital - shares * current_price
            
            self.equity_curve.append({
                'date': data.index[i],
                'equity': total_value,
                'position': self.position,
                'price': current_price
            })
        
        return pd.DataFrame(self.equity_curve)
    
    def analyze_results(self, equity_df):
        """分析回测结果"""
        print("\n📈 回测结果分析")
        print("=" * 50)
        
        initial_capital = 1000000
        final_equity = equity_df['equity'].iloc[-1]
        total_return = (final_equity - initial_capital) / initial_capital * 100
        
        print(f"初始资金: ¥{initial_capital:,.0f}")
        print(f"最终资金: ¥{final_equity:,.0f}")
        print(f"总收益率: {total_return:.2f}%")
        
        # 计算最大回撤
        equity_df['peak'] = equity_df['equity'].cummax()
        equity_df['drawdown'] = (equity_df['equity'] - equity_df['peak']) / equity_df['peak'] * 100
        max_drawdown = equity_df['drawdown'].min()
        print(f"最大回撤: {max_drawdown:.2f}%")
        
        # 交易统计
        if self.trades:
            print(f"总交易次数: {len(self.trades)}")
            
            # 计算盈亏交易
            profits = []
            for i in range(0, len(self.trades), 2):
                if i + 1 < len(self.trades):
                    entry = self.trades[i]
                    exit = self.trades[i + 1]
                    
                    if entry['action'] in ['BUY']:
                        profit = (exit['price'] - entry['price']) / entry['price'] * 100
                    else:  # SHORT
                        profit = (entry['price'] - exit['price']) / entry['price'] * 100
                    
                    profits.append(profit)
            
            if profits:
                win_trades = [p for p in profits if p > 0]
                lose_trades = [p for p in profits if p <= 0]
                
                print(f"盈利交易: {len(win_trades)} 次")
                print(f"亏损交易: {len(lose_trades)} 次")
                print(f"胜率: {len(win_trades)/len(profits)*100:.1f}%")
                
                if win_trades:
                    print(f"平均盈利: {np.mean(win_trades):.2f}%")
                if lose_trades:
                    print(f"平均亏损: {np.mean(lose_trades):.2f}%")
        
        return equity_df
    
    def plot_results(self, data, equity_df):
        """绘制回测结果"""
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12))
        
        # 价格和信号
        ax1.plot(data.index, data['close'], label='价格', linewidth=1)
        
        # 标记交易信号
        for trade in self.trades:
            color = 'red' if trade['action'] in ['BUY', 'SHORT'] else 'green'
            marker = '^' if trade['action'] in ['BUY', 'COVER'] else 'v'
            ax1.scatter(trade['date'], trade['price'], color=color, marker=marker, s=100)
        
        ax1.set_title('ATR RSI策略 - 价格和交易信号')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True)
        
        # 技术指标
        ax2.plot(data.index, data['rsi'], label='RSI', color='purple')
        ax2.axhline(y=self.rsi_buy, color='red', linestyle='--', label=f'买入线({self.rsi_buy})')
        ax2.axhline(y=self.rsi_sell, color='green', linestyle='--', label=f'卖出线({self.rsi_sell})')
        ax2.axhline(y=50, color='gray', linestyle='-', alpha=0.5)
        ax2.set_title('RSI指标')
        ax2.set_ylabel('RSI')
        ax2.legend()
        ax2.grid(True)
        
        # 权益曲线
        ax3.plot(equity_df['date'], equity_df['equity'], label='权益曲线', color='blue')
        ax3.axhline(y=1000000, color='gray', linestyle='--', label='初始资金')
        ax3.set_title('权益曲线')
        ax3.set_ylabel('资金')
        ax3.set_xlabel('日期')
        ax3.legend()
        ax3.grid(True)
        
        plt.tight_layout()
        plt.savefig('atr_rsi_backtest_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\n📊 图表已保存为: atr_rsi_backtest_results.png")

def generate_sample_data():
    """生成示例数据"""
    print("📊 生成示例股票数据进行测试")
    
    # 生成一年的交易日数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    dates = dates[dates.weekday < 5]  # 只保留工作日
    
    # 生成价格数据 (模拟股票走势)
    np.random.seed(42)
    n = len(dates)
    
    # 基础趋势
    trend = np.linspace(100, 120, n)
    
    # 添加随机波动
    noise = np.random.normal(0, 2, n)
    
    # 添加周期性波动
    cycle = 5 * np.sin(np.linspace(0, 4*np.pi, n))
    
    # 合成价格
    close_prices = trend + noise + cycle
    
    # 生成高低价
    high_prices = close_prices + np.random.uniform(0.5, 2, n)
    low_prices = close_prices - np.random.uniform(0.5, 2, n)
    
    # 生成开盘价
    open_prices = close_prices + np.random.normal(0, 0.5, n)
    
    data = pd.DataFrame({
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': np.random.randint(1000000, 5000000, n)
    }, index=dates)
    
    return data

def main():
    """主函数"""
    print("🎯 ATR RSI策略简化回测")
    print("不依赖VeighNa的独立策略测试")
    print("=" * 80)
    
    # 生成测试数据
    data = generate_sample_data()
    print(f"✅ 生成测试数据: {len(data)} 个交易日")
    
    # 创建策略实例
    strategy = SimpleATRRSIStrategy(
        atr_length=14,
        atr_ma_length=30,
        rsi_length=14,
        rsi_entry=16,
        trailing_percent=0.8
    )
    
    # 运行回测
    equity_df = strategy.backtest(data)
    
    # 分析结果
    equity_df = strategy.analyze_results(equity_df)
    
    # 绘制结果
    try:
        strategy.plot_results(data, equity_df)
    except Exception as e:
        print(f"⚠️ 图表绘制失败: {e}")
        print("可能需要安装matplotlib: pip install matplotlib")
    
    print("\n🎉 ATR RSI策略回测完成！")
    print("\n💡 策略特点:")
    print("   • 趋势跟踪: 只在趋势市场中交易")
    print("   • 反转入场: 利用RSI极值反转信号")
    print("   • 移动止损: 保护利润，控制风险")
    print("   • 震荡过滤: 震荡市场中避免交易")

if __name__ == "__main__":
    main()
