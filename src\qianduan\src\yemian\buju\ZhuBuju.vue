<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <el-aside 
      :width="sidebarWidth" 
      class="layout-sidebar"
      :class="{ 'is-collapsed': appStore.sidebarCollapsed }"
    >
      <div class="sidebar-header">
        <div class="logo">
          <el-icon class="logo-icon"><TrendCharts /></el-icon>
          <span v-show="!appStore.sidebarCollapsed" class="logo-text">量化交易</span>
        </div>
      </div>
      
      <el-scrollbar class="sidebar-menu-wrapper">
        <el-menu
          :default-active="activeMenu"
          :collapse="appStore.sidebarCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <template v-for="route in menuRoutes" :key="route.path">
            <el-sub-menu 
              v-if="route.children && route.children.length > 0"
              :index="route.path"
            >
              <template #title>
                <el-icon><component :is="route.meta?.icon" /></el-icon>
                <span>{{ route.meta?.title }}</span>
              </template>
              
              <el-menu-item
                v-for="child in route.children"
                :key="child.path"
                :index="child.path"
              >
                <el-icon v-if="child.meta?.icon">
                  <component :is="child.meta.icon" />
                </el-icon>
                <span>{{ child.meta?.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            
            <el-menu-item v-else :index="route.path">
              <el-icon><component :is="route.meta?.icon" /></el-icon>
              <span>{{ route.meta?.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-scrollbar>
    </el-aside>
    
    <!-- 主内容区 -->
    <el-container class="layout-main">
      <!-- 顶部导航栏 -->
      <el-header class="layout-header">
        <div class="header-left">
          <el-button
            type="text"
            @click="appStore.toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon><Expand v-if="appStore.sidebarCollapsed" /><Fold v-else /></el-icon>
          </el-button>
          
          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item
              v-for="item in breadcrumbList"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 网络状态指示器 -->
          <div class="network-status" :class="{ offline: !appStore.online }">
            <el-icon><Connection v-if="appStore.online" /><Close v-else /></el-icon>
            <span class="status-text">{{ appStore.online ? '在线' : '离线' }}</span>
          </div>
          
          <!-- 主题切换 -->
          <el-dropdown @command="handleThemeChange" class="theme-dropdown">
            <el-button type="text">
              <el-icon><Sunny v-if="!appStore.isDark" /><Moon v-else /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="light">
                  <el-icon><Sunny /></el-icon>浅色主题
                </el-dropdown-item>
                <el-dropdown-item command="dark">
                  <el-icon><Moon /></el-icon>深色主题
                </el-dropdown-item>
                <el-dropdown-item command="auto">
                  <el-icon><Monitor /></el-icon>跟随系统
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          
          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand" class="user-dropdown">
            <div class="user-info">
              <el-avatar :size="32" class="user-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="user-name">{{ userStore.userInfo?.yonghuming || '用户' }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>个人资料
                </el-dropdown-item>
                <el-dropdown-item command="subscription">
                  <el-icon><CreditCard /></el-icon>订阅管理
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>系统设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <!-- 主要内容区域 -->
      <el-main class="layout-content">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-slide" mode="out-in">
            <keep-alive :include="appStore.keepAliveComponents">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/shangdian/app'
import { useUserStore } from '@/shangdian/user'
import type { RouteRecordRaw } from 'vue-router'

// 使用状态管理
const appStore = useAppStore()
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()

// 计算属性
const sidebarWidth = computed(() => appStore.sidebarCollapsed ? '64px' : '240px')

const activeMenu = computed(() => {
  const { path } = route
  // 处理子路由的激活状态
  if (path.includes('/strategy/')) return '/strategy/list'
  if (path.includes('/backtest/')) return '/backtest/list'
  if (path.includes('/trading/')) return '/trading/account'
  if (path.includes('/data/')) return '/data/market'
  if (path.includes('/settings/')) return '/settings/profile'
  return path
})

// 菜单路由配置
const menuRoutes = computed(() => {
  const routes = router.getRoutes()
  return routes
    .filter(route => route.path === '/' && route.children)
    .flatMap(route => route.children || [])
    .filter(route => !route.meta?.hideInMenu && route.meta?.title)
    .map(route => ({
      ...route,
      children: route.children?.filter(child => !child.meta?.hideInMenu)
    }))
})

// 面包屑导航
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  const breadcrumbs = matched.map(item => ({
    title: item.meta?.title as string,
    path: item.path
  }))
  
  // 添加首页
  if (breadcrumbs.length > 0 && breadcrumbs[0].path !== '/dashboard') {
    breadcrumbs.unshift({ title: '首页', path: '/dashboard' })
  }
  
  return breadcrumbs
})

// 方法
const handleThemeChange = (theme: 'light' | 'dark' | 'auto') => {
  appStore.setTheme(theme)
}

const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/settings/profile')
      break
    case 'subscription':
      router.push('/settings/subscription')
      break
    case 'settings':
      router.push('/settings/system')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await userStore.logout()
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}

// 监听路由变化，更新keep-alive组件
watch(
  () => route.name,
  (newName) => {
    if (newName && route.meta?.keepAlive) {
      appStore.addKeepAliveComponent(newName as string)
    }
  },
  { immediate: true }
)

// 响应式处理
watch(
  () => appStore.device,
  (device) => {
    if (device === 'mobile') {
      appStore.setSidebarCollapsed(true)
    }
  }
)
</script>

<style lang="scss" scoped>
.layout-container {
  display: flex;
  height: 100vh;
  width: 100vw;
}

.layout-sidebar {
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
  
  &.is-collapsed {
    .sidebar-header .logo-text {
      opacity: 0;
    }
  }
  
  .sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .logo {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-color-primary);
      
      .logo-icon {
        font-size: 24px;
        margin-right: 8px;
      }
      
      .logo-text {
        transition: opacity 0.3s ease;
      }
    }
  }
  
  .sidebar-menu-wrapper {
    height: calc(100vh - 60px);
  }
  
  .sidebar-menu {
    border-right: none;
    
    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      height: 48px;
      line-height: 48px;
      
      &:hover {
        background-color: var(--el-color-primary-light-9);
      }
      
      &.is-active {
        background-color: var(--el-color-primary-light-8);
        color: var(--el-color-primary);
      }
    }
  }
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 60px;
  
  .header-left {
    display: flex;
    align-items: center;
    
    .sidebar-toggle {
      margin-right: 16px;
      font-size: 18px;
    }
    
    .breadcrumb {
      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          color: var(--el-text-color-regular);
          
          &:hover {
            color: var(--el-color-primary);
          }
        }
        
        &:last-child .el-breadcrumb__inner {
          color: var(--el-text-color-primary);
          font-weight: 500;
        }
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .network-status {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: var(--el-color-success);
      
      &.offline {
        color: var(--el-color-danger);
      }
      
      .status-text {
        @media (max-width: 768px) {
          display: none;
        }
      }
    }
    
    .theme-dropdown,
    .user-dropdown {
      cursor: pointer;
    }
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.3s ease;
      
      &:hover {
        background-color: var(--el-fill-color-light);
      }
      
      .user-name {
        font-size: 14px;
        font-weight: 500;
        
        @media (max-width: 768px) {
          display: none;
        }
      }
      
      .dropdown-icon {
        font-size: 12px;
        transition: transform 0.3s ease;
      }
    }
  }
}

.layout-content {
  background: var(--el-bg-color-page);
  padding: 16px;
  overflow: auto;
}

// 页面切换动画
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 响应式设计
@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    height: 100vh;
    
    &:not(.is-collapsed) {
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    }
  }
  
  .layout-main {
    margin-left: 0;
  }
  
  .layout-header {
    padding: 0 12px;
  }
  
  .layout-content {
    padding: 12px;
  }
}
</style>
