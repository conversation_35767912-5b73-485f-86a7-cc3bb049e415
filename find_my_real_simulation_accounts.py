#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
寻找用户真实的模拟资金账户
用户只有2个模拟账户：测试 和 test
需要找到正确的API来获取这些账户
"""

import json
import logging
import requests
import pysnowball as ball
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MySimulationAccountFinder:
    """我的模拟账户查找器"""
    
    def __init__(self):
        self.user_id = "**********"
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:61.0) Gecko/******** Firefox/61.0',
            'Referer': 'https://xueqiu.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.session.headers.update(self.headers)
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            with open('xq.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cookies_str = config.get('cookies', '')
            for cookie in cookies_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    self.session.cookies.set(key, value)
            
            # 设置pysnowball token
            xq_a_token = self.session.cookies.get('xq_a_token', '')
            u = self.session.cookies.get('u', '')
            if xq_a_token and u:
                token = f"xq_a_token={xq_a_token};u={u}"
                ball.set_token(token)
                logger.info(f"✅ 使用用户 {u} 的Token")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            return False
    
    def search_simulation_accounts_api(self):
        """搜索模拟账户的API端点"""
        logger.info("🔍 搜索模拟账户API端点")
        logger.info("=" * 60)
        
        # 可能的模拟账户API端点
        simulation_apis = [
            # 模拟交易相关
            f"https://xueqiu.com/v4/stock/portfolio/list.json?type=simulation",
            f"https://xueqiu.com/v4/stock/portfolio/list.json?category=simulation",
            f"https://xueqiu.com/simulation/portfolio/list.json",
            f"https://xueqiu.com/simulation/account/list.json",
            
            # 用户模拟账户
            f"https://xueqiu.com/v4/stock/portfolio/list.json?user_id={self.user_id}&type=simulation",
            f"https://xueqiu.com/simulation/portfolio/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/account/simulation/list.json",
            
            # 组合相关（可能包含模拟账户）
            f"https://xueqiu.com/cubes/list.json?type=simulation",
            f"https://xueqiu.com/cubes/list.json?category=simulation",
            f"https://xueqiu.com/cubes/mine.json",
            f"https://xueqiu.com/cubes/user/{self.user_id}.json",
            
            # 交易账户相关
            f"https://xueqiu.com/account/list.json",
            f"https://xueqiu.com/account/portfolio/list.json",
            f"https://xueqiu.com/trading/account/list.json",
            
            # 可能的模拟交易API
            f"https://xueqiu.com/v5/stock/portfolio/list.json?type=simulation",
            f"https://xueqiu.com/v5/simulation/account/list.json",
        ]
        
        found_apis = []
        
        for api_url in simulation_apis:
            try:
                logger.info(f"\n🔗 测试API: {api_url}")
                response = self.session.get(api_url, timeout=10)
                
                logger.info(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   ✅ 成功获取JSON数据")
                        
                        # 检查是否包含模拟账户信息
                        data_str = json.dumps(data, ensure_ascii=False).lower()
                        
                        if any(keyword in data_str for keyword in ['测试', 'test', 'simulation', '模拟']):
                            logger.info(f"   🎯 可能包含模拟账户信息！")
                            found_apis.append({
                                'url': api_url,
                                'data': data,
                                'contains_simulation': True
                            })
                        
                        # 显示数据结构
                        if isinstance(data, dict):
                            keys = list(data.keys())
                            logger.info(f"   数据字段: {keys}")
                            
                            # 检查是否有列表数据
                            for key in ['list', 'data', 'accounts', 'portfolios', 'cubes']:
                                if key in data and isinstance(data[key], list):
                                    items = data[key]
                                    logger.info(f"   {key}: {len(items)} 个项目")
                                    
                                    # 检查每个项目
                                    for i, item in enumerate(items[:3]):  # 只检查前3个
                                        if isinstance(item, dict):
                                            item_str = json.dumps(item, ensure_ascii=False)
                                            if '测试' in item_str or 'test' in item_str.lower():
                                                logger.info(f"     项目 {i}: 🎯 包含目标关键词！")
                                                logger.info(f"     内容: {item}")
                        
                        # 保存有用的API
                        if data and (isinstance(data, dict) and data.keys()) or (isinstance(data, list) and data):
                            found_apis.append({
                                'url': api_url,
                                'data': data,
                                'contains_simulation': False
                            })
                        
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON响应")
                        
                elif response.status_code == 401:
                    logger.info(f"   🔒 需要认证")
                elif response.status_code == 403:
                    logger.info(f"   🚫 权限不足")
                else:
                    logger.info(f"   ❌ HTTP错误: {response.status_code}")
                    
            except Exception as e:
                logger.info(f"   ❌ 请求异常: {e}")
        
        return found_apis
    
    def search_by_portfolio_name(self):
        """通过组合名称搜索"""
        logger.info("🔍 通过组合名称搜索")
        logger.info("=" * 60)
        
        search_terms = ['测试', 'test']
        found_portfolios = []
        
        for term in search_terms:
            try:
                logger.info(f"\n🔍 搜索关键词: {term}")
                
                # 使用雪球搜索API
                search_url = f"https://xueqiu.com/cubes/search.json?q={term}&count=100&market=cn"
                response = self.session.get(search_url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if 'list' in data and data['list']:
                        cubes = data['list']
                        logger.info(f"   找到 {len(cubes)} 个包含'{term}'的组合")
                        
                        # 查找属于当前用户的组合
                        user_cubes = []
                        for cube in cubes:
                            cube_user_id = str(cube.get('user_id', ''))
                            cube_name = cube.get('name', '')
                            cube_symbol = cube.get('symbol', '')
                            
                            logger.info(f"     组合: {cube_name} ({cube_symbol}) 用户ID: {cube_user_id}")
                            
                            if cube_user_id == self.user_id:
                                logger.info(f"   🎯 找到用户组合: {cube_name} ({cube_symbol})")
                                user_cubes.append(cube)
                                found_portfolios.append(cube)
                        
                        if not user_cubes:
                            logger.info(f"   ⚠️ 没有找到属于用户 {self.user_id} 的组合")
                    else:
                        logger.info(f"   没有找到包含'{term}'的组合")
                else:
                    logger.info(f"   搜索请求失败: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"   搜索'{term}'失败: {e}")
        
        return found_portfolios
    
    def check_user_profile_page(self):
        """检查用户个人页面"""
        logger.info("🔍 检查用户个人页面")
        logger.info("=" * 60)
        
        try:
            # 访问用户个人页面
            profile_url = f"https://xueqiu.com/u/{self.user_id}"
            logger.info(f"访问: {profile_url}")
            
            response = self.session.get(profile_url, timeout=10)
            
            if response.status_code == 200:
                page_content = response.text
                logger.info(f"✅ 用户页面访问成功，内容长度: {len(page_content)}")
                
                # 搜索关键词
                keywords = ['测试', 'test', '模拟', 'simulation', 'portfolio', '组合']
                found_keywords = []
                
                for keyword in keywords:
                    if keyword in page_content:
                        found_keywords.append(keyword)
                
                if found_keywords:
                    logger.info(f"   🎯 页面包含关键词: {found_keywords}")
                    
                    # 尝试提取可能的组合信息
                    import re
                    
                    # 查找可能的组合ID
                    cube_patterns = [
                        r'ZH\d{6,8}',  # ZH开头的组合ID
                        r'cube[_-]?id["\']?\s*[:=]\s*["\']?(\w+)',  # cube_id
                        r'portfolio[_-]?id["\']?\s*[:=]\s*["\']?(\w+)',  # portfolio_id
                    ]
                    
                    for pattern in cube_patterns:
                        matches = re.findall(pattern, page_content, re.IGNORECASE)
                        if matches:
                            logger.info(f"   找到可能的组合ID: {matches}")
                
                # 保存页面内容用于分析
                with open('user_profile_page.html', 'w', encoding='utf-8') as f:
                    f.write(page_content)
                logger.info(f"   💾 页面内容已保存到 user_profile_page.html")
                
                return page_content
            else:
                logger.info(f"❌ 用户页面访问失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 检查用户页面失败: {e}")
            return None
    
    def try_direct_simulation_apis(self):
        """尝试直接的模拟交易API"""
        logger.info("🔍 尝试直接的模拟交易API")
        logger.info("=" * 60)
        
        # 基于easytrader可能使用的API端点
        simulation_endpoints = [
            # 模拟交易账户
            "https://xueqiu.com/v4/stock/portfolio/account/list.json",
            "https://xueqiu.com/v4/stock/portfolio/simulation/list.json",
            "https://xueqiu.com/simulation/v4/account/list.json",
            
            # 用户的模拟账户
            f"https://xueqiu.com/v4/stock/portfolio/account/list.json?user_id={self.user_id}",
            f"https://xueqiu.com/simulation/account/list.json?user_id={self.user_id}",
            
            # 可能的内部API
            "https://xueqiu.com/account/simulation.json",
            "https://xueqiu.com/portfolio/simulation/list.json",
            "https://xueqiu.com/trading/simulation/accounts.json",
        ]
        
        successful_apis = []
        
        for endpoint in simulation_endpoints:
            try:
                logger.info(f"\n🔗 测试: {endpoint}")
                response = self.session.get(endpoint, timeout=10)
                
                logger.info(f"   状态: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        logger.info(f"   ✅ 成功获取数据")
                        
                        # 检查数据内容
                        data_str = json.dumps(data, ensure_ascii=False)
                        if '测试' in data_str or 'test' in data_str.lower():
                            logger.info(f"   🎯 包含目标账户信息！")
                            logger.info(f"   数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                            
                            successful_apis.append({
                                'endpoint': endpoint,
                                'data': data,
                                'has_target': True
                            })
                        else:
                            logger.info(f"   数据预览: {data_str[:200]}...")
                            successful_apis.append({
                                'endpoint': endpoint,
                                'data': data,
                                'has_target': False
                            })
                            
                    except json.JSONDecodeError:
                        logger.info(f"   ⚠️ 非JSON响应")
                        
                elif response.status_code in [401, 403]:
                    logger.info(f"   🔒 需要更高权限")
                else:
                    logger.info(f"   ❌ 失败")
                    
            except Exception as e:
                logger.info(f"   ❌ 异常: {e}")
        
        return successful_apis

def main():
    """主函数"""
    print("🎯 寻找用户真实的模拟资金账户")
    print("目标：找到'测试'和'test'两个模拟账户")
    print("=" * 80)
    
    finder = MySimulationAccountFinder()
    
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'user_id': finder.user_id,
        'target_accounts': ['测试', 'test'],
        'api_search_results': [],
        'name_search_results': [],
        'profile_page_content': None,
        'simulation_api_results': []
    }
    
    # 1. 搜索模拟账户API
    logger.info("\n🔧 1. 搜索模拟账户API端点")
    api_results = finder.search_simulation_accounts_api()
    all_results['api_search_results'] = api_results
    
    # 2. 通过名称搜索
    logger.info("\n📋 2. 通过组合名称搜索")
    name_results = finder.search_by_portfolio_name()
    all_results['name_search_results'] = name_results
    
    # 3. 检查用户页面
    logger.info("\n👤 3. 检查用户个人页面")
    profile_content = finder.check_user_profile_page()
    all_results['profile_page_content'] = profile_content is not None
    
    # 4. 尝试直接模拟API
    logger.info("\n💰 4. 尝试直接模拟交易API")
    simulation_results = finder.try_direct_simulation_apis()
    all_results['simulation_api_results'] = simulation_results
    
    # 5. 汇总结果
    logger.info("\n📊 5. 搜索结果汇总")
    logger.info("=" * 60)
    
    found_target_accounts = False
    
    # 检查API搜索结果
    target_apis = [r for r in api_results if r.get('contains_simulation', False)]
    if target_apis:
        logger.info(f"✅ 找到 {len(target_apis)} 个可能包含模拟账户的API")
        for api in target_apis:
            logger.info(f"   🎯 {api['url']}")
        found_target_accounts = True
    
    # 检查名称搜索结果
    if name_results:
        logger.info(f"✅ 通过名称搜索找到 {len(name_results)} 个组合")
        for result in name_results:
            logger.info(f"   📊 {result.get('name', 'N/A')} ({result.get('symbol', 'N/A')})")
        found_target_accounts = True
    
    # 检查模拟API结果
    target_simulation_apis = [r for r in simulation_results if r.get('has_target', False)]
    if target_simulation_apis:
        logger.info(f"✅ 找到 {len(target_simulation_apis)} 个包含目标账户的模拟API")
        for api in target_simulation_apis:
            logger.info(f"   💰 {api['endpoint']}")
        found_target_accounts = True
    
    if not found_target_accounts:
        logger.warning("⚠️ 没有找到'测试'和'test'模拟账户")
        logger.info("💡 可能的原因:")
        logger.info("   1. 模拟账户是私有的，不在公开API中")
        logger.info("   2. 需要特殊的API端点或权限")
        logger.info("   3. 账户名称可能不是'测试'和'test'")
        logger.info("   4. 需要通过雪球App的内部API访问")
    
    # 6. 保存结果
    try:
        with open('simulation_account_search_results.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        logger.info(f"\n💾 完整搜索结果已保存到: simulation_account_search_results.json")
    except Exception as e:
        logger.error(f"\n❌ 保存结果失败: {e}")
    
    print(f"\n🎉 搜索完成！")
    
    if found_target_accounts:
        print(f"🎯 成功找到可能的目标账户！请查看详细结果。")
    else:
        print(f"⚠️ 未找到目标账户，可能需要其他方法或权限。")

if __name__ == '__main__':
    main()
